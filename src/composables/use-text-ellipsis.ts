import type { CSSProperties } from 'vue'

interface Options {
  lines?: MaybeRefOrGetter<number>
}

export function useTextEllipsis(
  element: MaybeRefOrGetter<HTMLElement | undefined | null>,
  { lines = 1 }: Options,
) {
  const style: CSSProperties = reactive({
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    textWrap: 'wrap',
    wordBreak: 'break-all',
    whiteSpace: 'wrap',
    display: '-webkit-box',
    WebkitBoxOrient: 'vertical',
  })

  const isOverflowing = ref(false)
  function checkOverflow() {
    const el = toValue(element)
    if (!el) {
      return
    }

    isOverflowing.value = el.scrollHeight > el.clientHeight
  }

  watchEffect(() => {
    style.WebkitLineClamp = toValue(lines)
    checkOverflow()
  })

  useMutationObserver(element, checkOverflow, {
    childList: true,
    characterData: true,
    subtree: true,
  })

  useResizeObserver(element, checkOverflow)

  return {
    style,
    isOverflowing,
  }
}
