import { useCookies } from '@vueuse/integrations/useCookies'
import type { CookieChangeListener } from 'universal-cookie'

const COOKIE_NAME = 'csrftoken'

export function useCSRFToken() {
  const { get, addChangeListener, removeChangeListener } = useCookies()
  const token = ref<string>(get(COOKIE_NAME))

  const listener: CookieChangeListener = ({ name, value }) => {
    if (name === COOKIE_NAME) {
      token.value = value
    }
  }

  addChangeListener(listener)
  onUnmounted(() => {
    removeChangeListener(listener)
  })

  return token
}
