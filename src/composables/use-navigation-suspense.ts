import { useNProgress } from '@vueuse/integrations/useNProgress'
import { message } from 'ant-design-vue'

interface Options {
  delay?: number
  suspense(): Promise<{ error: Error | undefined | null }>
}

export async function useNavigationSuspense({ delay = 0, suspense }: Options) {
  const { isLoading } = useNProgress()
  const router = useRouter()

  let resolved = false
  setTimeout(() => {
    if (!resolved) {
      isLoading.value = true
    }
  }, delay)
  const { error } = await suspense()
  resolved = true
  isLoading.value = false
  if (error) {
    message.error(error.message)
    router.go(-1)
    throw error
  }
}
