import { defineStore } from 'pinia';

interface homeworkDetails {
  id: number;
  name: string;
}
interface courseDetails {
  id: number;
  title: string;
}

interface ChapterItem {
  id: number | string;
  course: number;
  title: string;
  content: string;
  parent: number | null;
  order: number;
  open?: boolean;
  children: ChapterChild[]; // 子目录数组，可选
}

interface ChapterChild {
  id: number | string;
  title: string;
  content: string;
  parent: number | string;
  order: number;
}
export const useCourseStore = defineStore('course-store', {
    state: () => {
         return {
            semester_id: 2, //学期id，默认为2
            user_id: 2, //用户id，默认为2
            courseId: 0  ,//课程id
            courseDetails: <courseDetails>{}, //课程详情
            chapterSide: <ChapterItem[]>[], //章节详情
            homeworkDetails: <homeworkDetails>{}, //作业详情
        }
    },
    actions: {
        setCourseId(course:number) {
            this.courseId = course
        },
        setCourseDetails(details:any) {
            this.courseDetails = details
        },
        setHomeworkDetails(details:any) {
            this.homeworkDetails = details
        },
        setChapterDetails(details:any) {
            this.chapterSide = []
            this.chapterSide.push(details)
            this.chapterSide[0].open = true
        }
    },
    persist: true
})
