import { defineStore } from "pinia";
import { ref } from "vue";
// export interface questionState {
//     titleNumber: Number
// }

export const useQuestionStore = defineStore('question', {
    // 状态
    state() {
        return {
            titleNumber: 1,
            selectedQuestionIds: new Set<number | string>(),
            selectedQuestions: <any>[]
        }
    },
    // 动作
    actions: {
        setSelectedQuestionIds(ids: Set<number | string>) {
            this.selectedQuestionIds = ids
        },
        setSelectedQuestions(questions: any[]) {
            this.selectedQuestions = questions
        },
        setAiSelectedQuestionIds(ids: Set<number | string>) {
            ids.forEach(id => {
                this.selectedQuestionIds.add(id)
            })
        },
        setAiSelectedQuestions(questions: any[]) {
            this.selectedQuestions.push(...questions)
        },
        updateTitleNumber(value: number) {
            this.titleNumber = value;
        },
        prevTitle() {
            this.titleNumber--;
        },
        nextTitle() {
            this.titleNumber++
        }
    },
    
    // 计算
    getters: {}
})