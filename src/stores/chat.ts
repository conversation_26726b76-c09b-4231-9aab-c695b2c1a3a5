import { v4 as uuid } from 'uuid'

type Model = {
  value: string
  label: string
}

type KbAttachment = {
  id: string
  name: string
}

type FileAttachment = {
  id?: string
  file: File
  remotePath?: string
}

type CourseAttachment = {
  id: string
  name: string
}

/**
 * 当前版本 `@ai-sdk/vue` 的实现中，并不能跨组件实例使用 `stop()`，所以需要手动维护。
 */
class DoubleBuffer<T> {
  private buffers: [T | undefined, T | undefined] = [undefined, undefined]
  private currentIndex: 0 | 1 = 0

  private get nextIndex(): 0 | 1 {
    return this.currentIndex === 0 ? 1 : 0
  }

  swap(): void {
    this.currentIndex = this.nextIndex
  }

  write(value: T): void {
    const targetIndex = this.current !== undefined ? this.nextIndex : this.currentIndex
    this.buffers[targetIndex] = value
  }

  get current(): T | undefined {
    return this.buffers[this.currentIndex]
  }

  get next(): T | undefined {
    return this.buffers[this.nextIndex]
  }

  setCurrent(value: T | undefined): void {
    this.buffers[this.currentIndex] = value
  }

  setNext(value: T | undefined): void {
    this.buffers[this.nextIndex] = value
  }

  clear(): void {
    this.buffers = [undefined, undefined]
  }

  isEmpty(): boolean {
    return this.current === undefined && this.next === undefined
  }
}

type ChatId = string

export const createChatStore = (chatId: string) =>
  defineStore(`chat-${chatId}`, () => {
    const id = ref<string>(chatId)

    const selectedModel = ref<Model>()
    const kbs = ref([] as KbAttachment[])
    const files = ref([] as FileAttachment[])
    const courses = ref([] as CourseAttachment[])

    const stopStore = new Map<ChatId, DoubleBuffer<() => void>>()
    const staleStore = new Map<ChatId, boolean>()

    function setNewChatId() {
      id.value = uuid()
    }

    function setStopFnStale(chatId: string, stale: boolean) {
      staleStore.set(chatId, stale)
    }

    function registerStopFn({ chatId, stop }: { chatId: string; stop: () => void }) {
      const buffer = getStopFnBuffer(chatId)
      const hof = () => {
        stop()
        swapStopFnBufferIfStale(chatId)
      }
      buffer.write(hof)
    }

    function getStopFnBuffer(chatId: string) {
      let buffer = stopStore.get(chatId)
      if (!buffer) {
        buffer = new DoubleBuffer()
        stopStore.set(chatId, buffer)
      }
      return buffer
    }

    function getCurrentStopFn(chatId: string) {
      const buffer = getStopFnBuffer(chatId)
      if (!buffer.current) {
        throw new Error('No stop function registered')
      }
      return buffer.current
    }

    function swapStopFnBufferIfStale(chatId: string) {
      if (staleStore.get(chatId)) {
        const buffer = stopStore.get(chatId)!
        buffer.setCurrent(undefined)
        buffer.swap()
        staleStore.delete(chatId)
      }
    }

    return {
      id,
      selectedModel,
      kbs,
      files,
      courses,

      setNewChatId,
      setStopFnStale,
      registerStopFn,
      getCurrentStopFn,
      swapStopFnBufferIfStale,
      getStopFnBuffer,
    }
  })
