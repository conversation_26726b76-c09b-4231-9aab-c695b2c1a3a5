import { defineStore } from 'pinia'
import myClassIcon from '@/assets/image/img/myclassc.png'
import myClassIconActive from '@/assets/image/img/myclass.png'
import aiSpaceIcon from '@/assets/image/img/ai.png'
import aiSpaceIconActive from '@/assets/image/img/aiw.png'
import resSearchIcon from '@/assets/image/img/resources.png'
import resSearchIconActive from '@/assets/image/img/resourcesw.png'
import funcManagerIcon from '@/assets/image/img/function.png'
import funcManagerIconActive from '@/assets/image/img/functionw.png'

export const useSlidesStore = defineStore('slides', {
    state: () => {
        return {
            siderList:[{
                title: '我的课程',
                value: 'myCourse',
                icon: myClassIcon,
                iconActive: myClassIconActive,
                path: '/'
            },{
                title: 'AI空间',
                value: 'aiSpace',
                icon: aiSpaceIcon,
                iconActive: aiSpaceIconActive,
                path: '/aiSpace'
            },{
                title: '资源检索',
                value: 'resSearch',
                icon: resSearchIcon,
                iconActive: resSearchIconActive,
                path: '/resSearch'
            },{
                title: '功能管理',
                value: 'funcManager',
                icon: funcManagerIcon,
                iconActive: funcManagerIconActive,
                path: '/funcManager'
            }],
            activeSidebar: 'myCourse' ,//当前状态栏状态
            isCollapsed: false, //是否折叠
        }
    },
    actions: {
        setSlides(activeSidebar: string) {
            this.activeSidebar = activeSidebar
        },
        setCollapsed(isCollapsed: boolean) {
            this.isCollapsed = isCollapsed
        }
    }
})