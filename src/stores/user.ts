import type { RouteRecordRaw } from 'vue-router'

export type UserRole = 'student' | 'teacher' | 'admin' | 'unknown'

const USER_ROLE_ENUM_MAP = ['admin', 'teacher', 'student', 'unknown'] as const

export const useUserStore = defineStore('user', () => {
  const { data, suspense } = useQuery({
    queryKey: ['user'],
    async queryFn() {
      const { data } = await baseFetcher<{
        data: { id: number; role: 0 | 1 | 2; username: string }
      }>('/user/')

      return data
    },
    retry: false,
  })

  const role = computed(() => USER_ROLE_ENUM_MAP[data.value?.role ?? 3] ?? 'unknown')
  const id = computed(() => data.value?.id)
  const name = computed(() => data.value?.username)

  const router = useRouter()
  const currentRoute = useRoute()
  function removeRouteByRole() {
    const routes = router.getRoutes()
    for (const route of routes) {
      checkRouteRoles(route)
    }
  }

  function checkRouteRoles(route: RouteRecordRaw): void {
    if (!route.meta?.roles) {
      return
    }

    if (route.meta.roles.includes(role.value) === false) {
      router.removeRoute(route.name!)
      if (route.name === currentRoute.name) {
        router.replace(route.path)
      }
    }

    if (!route.children) {
      return
    }

    for (const child of route.children) {
      checkRouteRoles(child)
    }
  }

  return {
    role,
    id,
    name,
    suspense,
    removeRouteByRole,
  }
})
