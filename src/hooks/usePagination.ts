import { ref } from 'vue';

export function usePagination(
  initialPage: number,
  initialPageSize: number,
  initialTotal: number,
  fetchFunction: (params: { page: number; pageSize: number }) => Promise<void>
) {
  const currentPage = ref(initialPage);
  const pageSize = ref(initialPageSize);
  const total = ref(initialTotal);

  const handleSizeChange = (val: number) => {
    pageSize.value = val;
    fetchFunction({ page: currentPage.value, pageSize: val });
  };

  const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    fetchFunction({ page: val, pageSize: pageSize.value });
  };

  return {
    currentPage,
    pageSize,
    total,
    handleSizeChange,
    handleCurrentChange,
  };
}