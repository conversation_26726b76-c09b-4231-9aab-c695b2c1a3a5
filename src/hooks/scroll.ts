// src/hooks/useHorizontalScroll.ts
import { ref, onMounted, onUnmounted, type Ref } from 'vue';
import { nextTick } from 'vue';

interface HorizontalScrollReturn {
  showArrows: Ref<boolean>;
  scrollTo: (direction: 'left' | 'right') => void;
  canScrollLeft: Ref<boolean>;
  canScrollRight: Ref<boolean>;
}

export default function useHorizontalScroll(
  wrapperRef: Ref<HTMLElement | null>,
  containerRef: Ref<HTMLElement | null>
): HorizontalScrollReturn {
  const showArrows = ref(false);
  const canScrollLeft = ref(false);
  const canScrollRight = ref(false);

  const scrollTo = (direction: 'left' | 'right') => {
    const container = wrapperRef.value?.firstElementChild as HTMLElement;
    if (!container) return;
    
    const scrollAmount = container.clientWidth * 0.8;
    container.scrollTo({
      left: container.scrollLeft + (direction === 'left' ? -scrollAmount : scrollAmount),
      behavior: 'smooth'
    });
  };

  const checkScroll = () => {
    if (containerRef.value) {
      const { scrollWidth, clientWidth, scrollLeft } = containerRef.value;
      showArrows.value = scrollWidth > clientWidth;
      console.log('scrollWidth', showArrows.value);
      canScrollLeft.value = scrollLeft > 0;
      canScrollRight.value = scrollLeft + clientWidth < scrollWidth;
    }
  };

  const handleScroll = () => checkScroll();

  onMounted(() => {
    checkScroll();
    window.addEventListener('resize', checkScroll);
    containerRef.value?.addEventListener('scroll', handleScroll);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', checkScroll);
    containerRef.value?.removeEventListener('scroll', handleScroll);
  });

  return {
    showArrows,
    scrollTo,
    canScrollLeft,
    canScrollRight
  };
}