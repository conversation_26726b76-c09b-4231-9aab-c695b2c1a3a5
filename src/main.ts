import './assets/css/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedState from 'pinia-plugin-persistedstate'
import { VueQueryPlugin as tanstackQuery } from '@tanstack/vue-query'
import { zhCN } from 'date-fns/locale'
import { setDefaultOptions } from 'date-fns'
// @ts-expect-error no dts
import VResizable from 'v-resizable'

import App from './App.vue'
import { router } from './router'
import { env } from '../env'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const app = createApp(App)
//引用图标库
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

const pinia = createPinia()
pinia.use(piniaPluginPersistedState)

setDefaultOptions({ locale: zhCN })

// prettier-ignore
app
  .use(pinia)
  .use(router)
  .use(tanstackQuery, vueQueryPluginOptions)
  .use(ElementPlus)
  .use(VResizable)

async function enableMocking() {
  if (!env.VITE_ENABLE_MOCKING) {
    return
  }

  const { worker } = await import('./mocks')
  return worker.start({
    onUnhandledRequest: 'bypass',
  })
}

enableMocking().then(() => app.mount('#app'))
