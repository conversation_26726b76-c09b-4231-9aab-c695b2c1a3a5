import http from '@/services/http'
//获取考试/作业列表
export const getExamList = (params: any) => {
  return http({
    url: '/v1/exam/',
    method: 'get',
    params: params,
  })
}
//新增考试/作业
export const creatExam = (params: any) => {
  return http({
    url: '/v1/exam/',
    method: 'post',
    data: params,
  })
}
//编辑考试/作业
export const editExam = (params: any) => {
  return http({
    url: `/v1/exam/${params.id}/`,
    method: 'put',
    data: params,
  })
}

//加入考试/作业
export const joinExam = (params: any) => {
  return http({
    url: '/v1/paper/',
    method: 'post',
    data: params,
  })
}
//发布作业v1/exam/publish/
export const examPublish = (params: any) => {
  return http({
    url: '/v1/exam/publish/',
    method: 'post',
    data: params,
  })
}

//删除考试
export const deleteExam = (params: any) => {
  return http({
    url: '/v1/exam/delete/',
    method: 'post',
    data: params,
  })
}

//答卷api/v1/take_part_exam/student_answer/22/
export const takePartExamStudentAnswer = (examId: number) => {
  return http({
    url: `/v1/take_part_exam/student_answer/${examId}/`,
    method: 'get',
  })
}

//提交作业v1/exam/submit/
export const takePartExamSubmit = (params: any) => {
  return http({
    url: '/v1/exam/submit/',
    method: 'post',
    data: params,
  })
}

//作业批阅列表  v1/exam/overview
export const examOverview = (params: any) => {
  return http({
    url: '/v1/exam/overview/',
    method: 'get',
    params: params,
  })
}

//学生作业详情 v1/take_part_exam/62/
export const examDetail = (params: any) => {
  return http({
    url: `/v1/take_part_exam/${params}/`,
    method: 'get',
  })
}

//发布作业试题v1/feedback/publish_mark/
export const publishExamQuestion = (params: any) => {
  return http({
    url: '/v1/feedback/publish_mark/',
    method: 'post',
    data: params,
  })
}

//学生端作业列表v1/exam/student_list/?course=33
export const examListStu = (params: any) => {
  return http({
    url: `/v1/exam/student_list/`,
    method: 'get',
    params,
  })
}
