import http from '@/services/http'

const baseUrl = 'v1'
//班级列表
// api/v1/class/?course_semester=2&page=1&page_size=1
export const courseClassList = (params: any) => {
  return http({
    url: `${baseUrl}/class/`,
    method: 'get',
    params: params,
  })
}

//新建班级
export const addCourseClass = (params: any) => {
  return http({
    url: `${baseUrl}/class/`,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    method: 'post',
    data: params,
  })
}

// 班级设置
export const classSettings = (params: any) => {
  return http({
    url: `${baseUrl}/class/${params.id}/`,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    method: 'put',
    data: params,
  })
}

// 班级删除
export const classDelete = (params: any) => {
  return http({
    url: `${baseUrl}/class/batch_destroy/`,
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'post',
    data: params,
  })
}

// 班级学生列表
export const classStudentList = (params: any) => {
  return http({
    url: `${baseUrl}/class_member/`,
    method: 'get',
    params: params,
  })
}

// 班级学生添加（批量）
export const classStudentAdd = (params: any) => {
  return http({
    url: `${baseUrl}/class_member/`,
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'post',
    data: params,
  })
}

// 班级学生删除（批量）,单个删除也用这个接口
export const classStudentDelete = (params: any) => {
  return http({
    url: `${baseUrl}/class_member/batch_destroy/`,
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'post',
    data: params,
  })
}
