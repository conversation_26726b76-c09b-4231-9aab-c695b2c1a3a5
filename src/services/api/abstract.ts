import http from '@/services/http'
import axios from 'axios'
import { env } from '@/../env'
import { getAccessToken, getCSRFToken } from '@/utils/auth'

//解析文件
export const fileAnalysis = (params: any) => {
  return http({
    url: 'v1/analysis/',
    // 全局请求头为application/json; 上传文件需手动覆盖请求头类型为multipart/form-data
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    method: 'post',
    data: params,
  })
}

// const csrfToken = document.cookie.split('=')[1];

// 摘要提取(流式)
// export const createAbstract = (params: any) =>{
//     return fetch(`/api/v1/analysis/ai/`, {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json',
//         'X-CSRFToken': csrfToken
//         },
//       body: JSON.stringify(params),
//     })
// }

// 添加 CSRF Token
const csrfToken = getCSRFToken()

// 摘要提取(流式)
export const createAbstract = async (params: any) => {
  const controller = new AbortController()
  const timeout = 300000 // 300s
  const timeoutId = setTimeout(() => {
    controller.abort()
  }, timeout)

  try {
    const response = await fetch(`/api/v1/analysis/ai/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrfToken,
      },
      body: JSON.stringify(params),
      signal: controller.signal,
    })
    return response
  } catch (error: any) {
    if (error.name === 'AbortError') {
      throw new Error('请求超时')
    }
    throw error
  } finally {
    clearTimeout(timeoutId)
  }
}
