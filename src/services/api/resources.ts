import http from '@/services/http'

//获取筛选条件
export const getFilterOption = () => {
  return http({
    url: `v1/retrieve/filter_options/`,
    method: 'get',
  })
}

//综合检索
export const localSearch = (params: any) => {
  return http({
    url: `v1/retrieve/synthetical_search/`,
    method: 'get',
    params: params,
  })
}

//外部检索
export const webSearch = (params: any) => {
  return http({
    url: `v1/external/external_search/`,
    method: 'post',
    data: params,
  })
}
