import http from '@/services/http'

//登录
export const courseLogin = (params: any) => {
  return http({
    url: `v1/cas/login/`,
    method: 'post',
    data: params,
  })
}

//添加课程
export const courseAdd = (params: any) => {
  return http({
    url: `v1/course/`,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    method: 'post',
    data: params,
  })
}
//修改课程
export const courseEdit = (params: any) => {
  return http({
    url: `v1/course/${params.get('id')}/`,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    method: 'put',
    data: params,
  })
}
//获取课程列表
export const courseListapi = (params: any) => {
  return http({
    url: `v1/course/`,
    method: 'get',
    params: params,
  })
}
//删除课程
export const courseDelete = (id: any) => {
  return http({
    url: `v1/course/${id}/`,
    method: 'delete',
  })
}

//学期列表 v1/semester/
export const semesterList = () => {
  return http({
    url: `v1/semester/`,
    method: 'get',
    // params: params
  })
}

//课程详情api/v1/course_statistical/?course_id=41
export const courseDetail = (params: any) => {
  return http({
    url: `v1/course_statistical/?course_id=${params}`,
    method: 'get',
  })
}

//新增章节api/v1/chapter/
export const chapterAdd = (params: any) => {
  return http({
    url: `v1/chapter/`,
    method: 'post',
    data: params,
  })
}
//章节列表v1/chapter/?course_id=28
export const chapterList = (params: any) => {
  return http({
    url: `v1/chapter/`,
    method: 'get',
    params: params,
  })
}
// 课程章节指定目录 v1/chapter/tables/?chapter_id=2
export const chapterTables = (params: any) => {
  return http({
    url: `v1/chapter/tables/?chapter_id=${params}`,
    method: 'get',
  })
}

//课程章节目录
export const courseTables = (params: any) => {
  return http({
    url: `v1/chapter/?course_id=${params}`,
    method: 'get',
  })
}

// chapterList(params: any): Promise<any>{
//     return axios(`${SERVER_URL}/v1/chapter/?course_id=${params}`,
//     {
//       headers: { 'Content-Type': 'multipart/form-data','X-CSRFToken': csrfToken },
//       method: 'GET',
//     })
//   },

//章节列表详情 v1/chapter/19/
export const chapterDetail = (params: any) => {
  return http({
    url: `v1/chapter/${params}/`,
    method: 'get',
  })
}
//章节修改
export const chapterEdit = (params: any) => {
  return http({
    url: `v1/chapter/${params.id}/`,
    method: 'put',
    data: params,
  })
}
//章节删除v1/chapter/24/、
export const chapterDelete = (params: any) => {
  return http({
    url: `v1/chapter/${params}/`,
    method: 'delete',
  })
}

//知识点添加v1/knowledge_point/
export const knowledgeAdd = (params: any) => {
  return http({
    url: `v1/knowledge_point/`,
    method: 'post',
    data: params,
  })
}
//知识点修改v1/knowledge_point/1/
export const knowledgeEdit = (params: any) => {
  return http({
    url: `v1/knowledge_point/${params.id}/`,
    method: 'put',
    data: params,
  })
}

//知识点删除 v1/knowledge_point/1/
export const knowledgeDelete = (params: any) => {
  return http({
    url: `v1/knowledge_point/${params}/`,
    method: 'delete',
  })
}

//知识点列表v1/knowledge_point/
export const knowledgeList = (params: any) => {
  return http({
    url: `v1/knowledge_point/`,
    method: 'get',
    params: params,
  })
}

//教案取消绑定 api/v1/teachplan/batch_unbind/
export const teachplanUnbind = (params: any) => {
  return http({
    url: `v1/teachplan/batch_unbind/`,
    method: 'post',
    data: params,
  })
}
//PPT取消绑定v1/ppt/batch_unlink/
export const pptUnbind = (params: any) => {
  return http({
    url: `v1/ppt/batch_unlink/`,
    method: 'post',
    data: params,
  })
}

//个人空间 api/v1/course/owner/
export const courseOwner = () => {
  return http({
    url: `v1/course/owner/`,
    method: 'get',
  })
}

//教师列表 v1/course_teacher/?course_id=19
export const courseTeacher = (params: any) => {
  return http({
    url: `v1/course_teacher/`,
    method: 'get',
    params: params,
  })
}

//添加教师v1/course_teacher/
export const courseTeacherAdd = (params: any) => {
  return http({
    url: `v1/course_teacher/`,
    method: 'post',
    data: params,
  })
}
//删除教师 v1/course_teacher/batch_destroy/
export const courseTeacherDelete = (params: any) => {
  return http({
    url: `v1/course_teacher/batch_destroy/`,
    method: 'delete',
    data: params,
  })
}

//课程复制v1/course/copy/
export const courseCopy = (params: any) => {
  return http({
    url: `v1/course/copy/`,
    method: 'post',
    data: params,
  })
}
//课程重开v1/course/reopen/
export const courseReopen = (params: any) => {
  return http({
    url: `v1/course/reopen/`,
    method: 'post',
    data: params,
  })
}

//个人空间课程章节列表 v1/chapter/owner/
export const chapterOwner = () => {
  return http({
    url: `v1/course/chapter/`,
    method: 'get',
  })
}
