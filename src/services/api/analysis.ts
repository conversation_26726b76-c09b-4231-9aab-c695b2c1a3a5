import http from '@/services/http'
//获取作业学情分析列表
export const examAnalysis = (params: any) => {
  return http({
    url: 'v1/exam_analysis/',
    method: 'get',
    params: params,
  })
}

//获取作业整体学情分析
export const summaryAnalysis = (params: any) => {
  return http({
    url: 'v1/exam_analysis/whole/',
    method: 'get',
    params: params,
  })
}

//获取课程学情分析列表
export const courseAnalysis = (params: any) => {
  return http({
    url: 'v1/course_analysis/',
    method: 'get',
    params: params,
  })
}

//获取学生课程学情分析详情
export const studentAnalysis = (id: any) => {
  return http({
    url: `v1/course_analysis/${id}/`,
    method: 'get',
  })
}

//获取作业学情分析班级列表
export const examClassList = (params: any) => {
  return http({
    url: 'v1/exam_analysis/class_list/',
    method: 'get',
    params: params,
  })
}

//获取课程学情分析班级列表
export const courseClassList = (params: any) => {
  return http({
    url: 'v1/course_analysis/class_list/',
    method: 'get',
    params: params,
  })
}
