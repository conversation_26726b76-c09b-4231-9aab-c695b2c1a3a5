// api/v1/ppt/?page=1&page_size=10&is_deleted=true
import http from '@/services/http'

//题目列表
export const pptList = (params: any) => {
  return http({
    url: `v1/ppt/?${params}`,
    method: 'get',
    // params: params
  })
}
//ppt软删除
// v1/teachplan/soft_delete/
export const ppt_softDelete = (params: any) => {
  return http({
    url: `v1/ppt/soft_delete/`,
    method: 'post',
    data: params,
  })
}

//ppt硬删除
// v1/teachplan/hard_delete/
export const ppt_hardDelete = (params: any) => {
  return http({
    url: `v1/ppt/hard_delete/`,
    method: 'post',
    data: params,
  })
}

//上传PPT
// v1/ppt/
export const pptUpload = (params: any) => {
  return http({
    url: `v1/ppt/`,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    method: 'post',
    data: params,
  })
}

//修改PPT
// api/v1/ppt/6/
export const pptEdit = (id: any, params: any) => {
  return http({
    url: `v1/ppt/${id}/`,
    method: 'put',
    data: params,
  })
}

//保存PPT模板v1/ppt_tmp/
export const pptTmpSave = (params: any) => {
  return http({
    url: `v1/ppt_tmp/`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: params,
  })
}

//模板列表/v1/ppt_tmp
export const pptTmpList = (params: any) => {
  return http({
    url: `v1/ppt_tmp/`,
    method: 'get',
    params: params,
  })
}

//批量软删除模板 v1/ppt_tmp/soft_delete/
export const pptTmpSoftDelete = (params: any) => {
  return http({
    url: `v1/ppt_tmp/soft_delete/`,
    method: 'post',
    data: params,
  })
}

//硬删除模板v1/ppt_tmp/hard_delete/
export const pptTmpHardDelete = (params: any) => {
  return http({
    url: `v1/ppt_tmp/hard_delete/`,
    method: 'post',
    data: params,
  })
}
