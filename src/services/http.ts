import axios from 'axios'
import { ResultEnum } from '@/enums/ResultEnum'
import { getAccessToken, getCSRFToken } from '@/utils/auth'
import { message } from 'ant-design-vue'
import { authAxiosInterceptor } from '@/utils/api-auth'

import type { AxiosError } from 'axios'

import { env } from '@/../env'

// console.log(import.meta.env,'ss');

const http = axios.create({
  baseURL: env.VITE_API_BASE_URL,
  timeout: 3600 * 1000,
  headers: {
    'Content-Type': 'application/json; charset=utf-8',
  },
})

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    const accessToken = getAccessToken()
    // 如果 Authorization 设置为 no-auth，则不携带 Token，用于登录、刷新 Token 等接口
    if (config.headers.Authorization !== 'no-auth' && accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`
    } else {
      delete config.headers.Authorization
    }
    // 添加 CSRF Token
    const csrfToken = getCSRFToken() // 你需要实现这个函数来获取 CSRF Token
    // console.log(csrfToken,'csrfToken');
    if (csrfToken) {
      config.headers['X-CSRFToken'] = csrfToken
    }
    return config
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error)
  },
)

http.interceptors.response.use(
  authAxiosInterceptor.responseSuccess,
  authAxiosInterceptor.responseError,
)

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 如果响应是二进制流，则直接返回，用于下载文件、Excel 导出等
    if (response.config.responseType === 'blob') {
      return response
    }
    const { code, data, message: serverMessage } = response.data
    if (code == ResultEnum.SUCCESS) {
      return response.data
    }

    message.error(serverMessage || '请求失败！')
    console.log(message || '接口格式错误')
    return Promise.reject(message || 'Error')
  },
  async (error: AxiosError) => {
    console.error('request error', error) // for debug
    // 非 2xx 状态码处理 401、403、500 等
    const { response } = error
    if (!response) {
      message.error('系统出错')
      // message.
      throw error
    }

    const [, msg] = (function () {
      if (response.data) {
        //@ts-expect-error untyped
        return [response.data.code, response.data.msg]
      }
      return [undefined, undefined]
    })()
  },
)

export default http
