import { ElMessage, ElMessageBox } from 'element-plus'
import type { Action } from 'element-plus'

export const openMessage = (message: string, type: string) => {
    ElMessage({
        message: message as string,
        type: type as 'success' | 'warning' | 'info' | 'error',
    })
}

export const openMessageBox = (message: string, title: string) => {
    return new Promise((resolve, reject) => {
        ElMessageBox.confirm(message, title, {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
        }).then(() => {
            resolve('confirmed');
        }).catch(() => {
            reject('canceled');
        });
    });
}

//题目难度等级转换难度:
// (0，'简单')，(1，'中等')，(2，'困难')，默认难度简单
export const levelTransform = (level: number) => {
    switch (level) {
        case 0:
            return '简单';
        case 1:
            return '中等';
        case 2:
            return '困难';
        default:
            return '简单';
    }
}

const Ens = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"];
// 根据索引获取字母
export const getEn = (index: number) => {
    return Ens[index] || ""; // 如果索引超出范围，返回空字符串
};

export const questionTypeData = [
    {
      value: '所有题型',
      label: '所有题型',
      
    },
    {
      value: '单选题',
      label: '单选题',
      
    },{
      value: '多选题',
      label: '多选题',
      
    },{
      value: '判断题',
      label: '判断题',
    },{
      value: '填空题',
      label: '填空题',
    },{
      value: '问答题',
      label: '问答题',
    }
  ]


export function formatDate(date: Date | string, format = 'YYYY-MM-DD HH:mm:ss') {
  const d = new Date(date);

  const pad = (n: number) => n.toString().padStart(2, '0');

  const year = d.getFullYear().toString();
  const month = pad(d.getMonth() + 1);
  const day = pad(d.getDate());
  const hours = pad(d.getHours());
  const minutes = pad(d.getMinutes());
  const seconds = pad(d.getSeconds());

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

export function formatDateTime(timestamp: number): string {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}


//扁平化数据
export function flattenChildren(data:any) {
    const result = {
        ...data,
        children: [] // 初始化空 children 数组
    };

    // 递归遍历原始数据，提取所有子节点
    function extractChildren(node:any, parentId:any) {
        if (node.children && node.children.length > 0) {
            node.children.forEach((child:any) => {
                // 把子节点添加到 result.children
                result.children.push({
                    ...child,
                    parent: parentId,
                    children: undefined // 移除嵌套的 children
                });
                // 递归处理子节点的子节点
                extractChildren(child, child.id);
            });
        }
    }

    // 从根节点开始处理
    extractChildren(data, data.id);
    return result;
}


type ChapterNode = {
  key: string | number;
  title: string;
  children?: ChapterNode[];
};

export const extractKeysAndTitles = (chapters: any[]): ChapterNode[] => {
  return chapters.map((chapter: any) => {
    const { id, title, children } = chapter;
    const key = id;

    // 如果有 children，则递归处理 children，保留其嵌套结构
    if (children && children.length > 0) {
      return {
        key,
        title,
        children: extractKeysAndTitles(children), // 递归处理并保留 children 结构
      };
    }

    // 没有 children 则只返回当前节点的 key 和 title
    return { key, title };
  });
};

//文件大小校验
export const fileSizeCheck = (file: File, maxSize: number) => {
  // const maxSize = 10 * 1024 * 1024; // 10MB（单位：字节）
    if (file.size > maxSize) {
      return true
    } else {
      return false
    }
}