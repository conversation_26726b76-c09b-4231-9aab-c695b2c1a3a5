import type { AxiosResponse, AxiosError } from 'axios'
import type { FetchContext } from 'ofetch'
import { message } from 'ant-design-vue'

export const authAxiosInterceptor = {
  responseSuccess: (response: AxiosResponse) => {
    return response
  },
  responseError: async (error: AxiosError) => {
    const response = error.response
    if(response?.status == 403) {
      message.error('权限不足')
      return
    }
    if (response?.status !== 401) {
      return
    }

    const authStore = useAuthStore()

    message.error('请登录')
    await new Promise((resolve) => setTimeout(resolve, 1500))
    authStore.attemptAuth()
  },
}

export const authOfetchInterceptor = {
  responseSuccess: () => {},
  responseError: async ({ response }: FetchContext) => {
    if (response?.status !== 401) {
      return
    }

    const authStore = useAuthStore()

    message.error('请登录')
    await new Promise((resolve) => setTimeout(resolve, 1500))
    authStore.attemptAuth()
  },
}
