
import MarkdownIt from 'markdown-it'
import { Document, Packer, Paragraph, TextRun } from "docx";
import type { ISectionOptions } from "docx";
import { saveAs } from 'file-saver';
// @ts-ignore
import html2pdf from 'html2pdf.js';

async function downloadPDF(content: string, name: string = 'document.pdf') {
            const md = new MarkdownIt()
            const html = md.render(content)
            const opt = {
                margin:       10,
                filename:     name,
                image:        { type: 'jpeg', quality: 0.98 },
                html2canvas:  { scale: 2 },
                jsPDF:        { unit: 'mm', format: 'a4', orientation: 'portrait' }
            };
            html2pdf().from(html).set(opt).save();
}

async function downloadWord(content: string, filename: string = 'document.doc') {
  const md = new MarkdownIt()
  const html = md.render(content)

  const sections = htmlToDocxSections(html);
  console.log(sections,'textContent内容');
  // return
  const doc = new Document({
      sections: [
          {
              children: sections
              // [
              //     new Paragraph(textContent)
              // ]
          } as ISectionOptions
      ]
  });
  Packer.toBlob(doc).then((blob) => {
      saveAs(blob, filename);
  });
}

// 将 HTML 转换为 docx 段落和文本运行
const htmlToDocxSections = (html: string): any[] => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const sections: any[] = [];

    const traverse = (node: Node) => {
        if (node.nodeType === Node.TEXT_NODE) {
            const text = node.textContent?.trim();
            if (text) {
                sections.push(
                    new Paragraph({
                        children: [new TextRun(text)]
                    })
                );
            }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement;

            if (element.tagName === 'BR') {
                sections.push(new Paragraph({}));
            } else if (element.tagName === 'P') {
                const text = element.textContent?.trim();
                if (text) {
                    sections.push(new Paragraph({ children: [new TextRun(text)] }));
                }
            } else if (element.tagName === 'STRONG') {
                const text = element.textContent?.trim();
                if (text) {
                    sections.push(
                        new Paragraph({
                            children: [new TextRun({ text, bold: true })]
                        })
                    );
                }
            } else if (element.tagName === 'H3' || element.tagName === 'H4') {
                const text = element.textContent?.trim();
                if (text) {
                    sections.push(
                        new Paragraph({
                            children: [
                                new TextRun({
                                    text: text,
                                    bold: true, // 可选：标题常用加粗
                                    size: element.tagName === 'H3' ? 32 : 20, // 单位1/2pt, 16pt = 32, 14pt = 28
                                    color: "000000" // 黑色
                                })
                            ]
                        })
                    );
                }
            } else if (element.tagName === 'UL') {
                const listItems = Array.from(element.querySelectorAll('li'));
                listItems.forEach(item => {
                    const text = item.textContent?.trim();
                    if (text) {
                        sections.push(
                            new Paragraph({
                                text,
                                bullet: { level: 0 }
                            })
                        );
                    }
                });
            } else {
                const children = element.childNodes;
                for (let i = 0; i < children.length; i++) {
                    traverse(children[i]);
                }
            }
        }
    };

    traverse(doc.body);
    return sections;
};


export { downloadWord, downloadPDF };