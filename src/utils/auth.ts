import { useCookies } from '@vueuse/integrations/useCookies'

import { env } from '@/../env'
// 访问 token 缓存的 key
const ACCESS_TOKEN_KEY = 'access_token'
// 刷新 token 缓存的 key
const REFRESH_TOKEN_KEY = 'refresh_token'

function getAccessToken(): string {
  return localStorage.getItem(ACCESS_TOKEN_KEY) || ''
}

function setAccessToken(token: string) {
  localStorage.setItem(ACCESS_TOKEN_KEY, token)
}

function getRefreshToken(): string {
  return localStorage.getItem(REFRESH_TOKEN_KEY) || ''
}

function setRefreshToken(token: string) {
  localStorage.setItem(REFRESH_TOKEN_KEY, token)
}

function clearToken() {
  localStorage.removeItem(ACCESS_TOKEN_KEY)
  localStorage.removeItem(REFRESH_TOKEN_KEY)
}

const COOKIE_NAME = 'csrftoken'

function getCSRFToken(): string {
  const { get } = useCookies()
  return get(COOKIE_NAME)
}



function clearAllCookies() {
  // const cookies = document.cookie.split(';');
  // for (const cookie of cookies) {
  //     const [name] = cookie.trim().split('=');
  //     document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  // }
  // 设置登录成功后想跳回的前端页面
  const frontendRedirectPath = '/' // 你想跳转的前端路径
  const currentHost = window.location.origin // 当前前端服务地址
  const nextUrl = `${currentHost}${frontendRedirectPath}`
  // 后端 CAS 登录地址
  const casLoginUrl = `${env.VITE_API_BASE_URL}/v1/cas/login/?next=${encodeURIComponent(nextUrl)}`
  console.log(casLoginUrl, nextUrl, 'casLoginUrl')
  // 跳转浏览器到 CAS 登录（注意是整页跳转）
  window.location.href = casLoginUrl
  console.log(document.cookie, 'cookie') // 查看所有cookie
}

function getCourse() {
  const courseData = localStorage.getItem('course');
  const course = courseData ? JSON.parse(courseData) : null;
  return course
}
function getSemester() {
  const semesterData = localStorage.getItem('semester');
  const semester = semesterData ? JSON.parse(semesterData) : null;
  return semester
}







export {
  getAccessToken, setAccessToken, clearToken, getRefreshToken,getCSRFToken,
  setRefreshToken, clearAllCookies, getCourse, getSemester
}
