import { createFetch, type FetchContext } from 'ofetch'

function throwResponseErrorMessage({ response, error }: FetchContext) {
  const data = response?._data
  if (
    data?.code === 200 ||
    data?.data?.code === 200 ||
    (Boolean(data?.code) === false &&
      Boolean(data?.data?.code) === false &&
      Boolean(data?.error?.code) === false)
  ) {
    return
  }
  const message = (function () {
    if (response?.status === 403) {
      return '无权访问'
    }
    return (
      data?.error?.message ??
      data?.error?.detail ??
      data?.error ??
      data?.message ??
      data?.detail ??
      data?.msg ??
      data?.data?.msg ??
      data?.data?.detail ??
      data?.data?.message ??
      data?.data?.error ??
      data?.data?.error?.message ??
      data?.data?.eeror?.detail ??
      Boolean(Number('?????????????????????????????????????????????????????????????????????????'))
    )
  })()
  if (message) {
    throw new Error(message)
  }
  if (error) {
    throw error
  }
  throw new Error('未知错误')
}

export const baseFetcher = createFetch({
  defaults: {
    headers: {
      'Content-Type': 'application/json',
    },
    baseURL: '/api/v1',
    responseType: 'json',
    onRequest: ({ options }) => {
      options.headers.set('X-CSRFToken', getCSRFToken())
    },
    onResponse: [authOfetchInterceptor.responseSuccess, throwResponseErrorMessage],
    onResponseError: [authOfetchInterceptor.responseError, throwResponseErrorMessage],
    retry: false,
  },
})

export const modelsFetcher = baseFetcher.create({
  baseURL: '/api/v1/model-manager',
})

export const kbFetcher = baseFetcher
