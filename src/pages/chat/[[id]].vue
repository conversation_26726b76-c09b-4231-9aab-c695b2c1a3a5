<script setup lang="ts">
  import { v4 as uuid } from 'uuid'

  const route = useRoute('/chat/[[id]]')
  const router = useRouter()
  const idParam = computed(() => route.params.id)
  watch(
    idParam,
    (id) => {
      if (!id) {
        router.replace({ path: `/chat/${uuid()}`, query: route.query })
      }
    },
    {
      immediate: true,
    },
  )
</script>

<template>
  <Suspense>
    <ChatPage
      :chat-id="idParam"
      :key="idParam"
    />
  </Suspense>
</template>
