<template>
  <div class="course-class-page">
    <!-- 返回按钮 -->
    <back :url="'-1'" />
    <!-- 标题 -->
    <!-- <div class="title">
      {{ course.title }}-班级管理
    </div> -->
    <div class="top">
      <div class="font-bold text-[24px] max-w-[40%] flex title">
        <ATooltip :title="getCourse().title" placement="top" :mouseEnterDelay="0.3">
          <div class="truncate cursor-pointer">{{ course.title }}</div>
        </ATooltip>
        <div class="shrink-0">-班级管理</div>
      </div>
    </div>
    <!-- 班级管理 -->
    <ClassManager />
  </div>
</template>

<script lang="ts" setup>

import { getCourse } from '@/utils/auth'
const course = getCourse()
//引入组件
import ClassManager from '@/components/courseClass/classManager.vue'
import back from '@/components/ui/back.vue'
//引入pinia
import { useCourseStore } from '@/stores/course'



</script>

<style scoped>
.course-class-page {
  background-image: url('@/assets/image/bg1.png');
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 100vh;
  padding: 0 40px 0 40px;
  overflow: auto;
  min-width: 900px;
  width: 100%;

  .title {
      /* font: 20px bold;
      font-weight: 700; */
      line-height: 24px;
      margin: 20px 0;
  }
}
</style>
