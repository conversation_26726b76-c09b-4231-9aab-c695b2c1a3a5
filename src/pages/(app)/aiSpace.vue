<template>
  <div class="layout-container">
    <div class="sidebar">
      <Sidebar />
    </div>
    <main class="main-content">
      <RouterView v-slot="{ Component }">
        <Suspense suspensible>
          <component :is="Component" />
        </Suspense>
      </RouterView>
    </main>
  </div>
</template>

<script setup lang="ts">
  import Sidebar from '@/components/aiSpace/sidebar.vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  console.log(router.getRoutes())
</script>

<style scoped>
  .layout-container {
    background-image: url('@/assets/image/img/aispacebg.webp');
    background-repeat: no-repeat;
    background-size: cover;
    min-height: 100vh;
    display: flex;
    contain: size;
    width: 100%;
  }
  .sidebar {
    flex-shrink: 0;
  }
  .main-content {
    flex: 1;
    contain: inline-size;
  }
</style>
