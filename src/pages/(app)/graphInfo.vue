<template>

      <div class="space">

        <div class="space-right">

              <div class="page">
                  <div class="page-header">
                    <div class="left">
                      <div @click="handleBack" class="back-btn">
                        <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg"/>
                        <span>返回</span>
                      </div>
                    </div>
                      <div class="center">                      
                        <a-tooltip :title="title" placement="bottom">
                          <div v-show="!editing" @click="startEditing" class="topTitle">{{ title }}</div>
                        </a-tooltip>
                        <a-input v-show="editing" v-model:value="teachPlanTitle" @change="handleChange"  @blur="editing=false" ref="inputRef" class="topTitle-input" style="max-width: 300px;" >
                          <template #suffix>
                            <CheckCircleOutlined  @mousedown.prevent style="color:#C4C4C4;margin-left:10px;" @click="saveTitle"/>
                            <CloseCircleOutlined  @mousedown.prevent style="color:#C4C4C4;margin-left:5px;" @click="cancleSaveTitle"/>
                          </template> 
                        </a-input></div>
                  </div>

                  <div class="box-container">
                    <div class="box">
                        <div class="graph-wrapper" :class="{ fullscreen: isenlarge }">
                            <!-- 控制面板，包含全屏切换按钮和2D/3D切换按钮 -->
                            <div class="control-panel">
                                <span class="enlarge-button" :class="{'enlarge-button_full_screen':isenlarge}" @click="toggleEnlarge">
                                    <el-icon><Rank /></el-icon>
                                </span>
                                <!-- <span class="control-button" :class="{'control-button-fullscreen': isenlarge}" @click="toggle2D3D">
                                {{ is3D ? '2D' : '3D' }}
                                </span> -->
                            </div>
                              <!-- 根据 is3D 状态切换显示 2D 或 3D 组件 -->
                            <Graph2D 
                              v-if=" !is3D && (staticNodes.length != 0 || staticLinks.length !=0)" 
                              :nodes="staticNodes" 
                              :links="staticLinks" 
                              :getGraphList="getGraphList"
                              :taskID="taskID"
                              @deleteNode="handleDeleteNode" 
                              @addNode="addNode"
                              @updateNode="updateNode"
                              @updateEdge="updateEdge"
                            />
                            <!-- <Graph3D v-else :nodes="staticNodes" :links="staticLinks" :getGraphList="getGraphList"/> -->
                        </div>
                      
                    </div>
                  </div>

                  <a-modal v-model:open="dialogVisible" :title=dialogTitle :width=500 :mask="false" :wrap-class-name="'custom-right-modal'">
                      <a-form :model="form">
                        <a-form-item v-show="showName" label="名称">
                          <a-input v-model:value="form.name"  />
                        </a-form-item>
                        <a-form-item v-show="showLabel" label="标签">
                          <a-input v-model:value="form.label"  />
                        </a-form-item>
                        <a-form-item v-show="showContent" label="内容">
                          <a-textarea v-model:value="form.content" :rows="6" />
                        </a-form-item>
                      </a-form>
                      <template #footer>
                          <div class="dialog-footer">
                            <a-button @click="dialogVisible = false">取消</a-button>
                            <a-button type="primary" @click="handleConfirm">
                              确定
                            </a-button>
                          </div>
                      </template>
                  </a-modal>


              </div>

        </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter,useRoute } from 'vue-router'
import { ref, onMounted, provide } from "vue";
import { graphList } from "@/services/api/graph";
import Graph2D from '@/components/graph/Graph2D.vue';
import Graph3D from '@/components/graph/Graph3D.vue';
import { CaretDownOutlined,SearchOutlined,CheckCircleOutlined,CloseCircleOutlined } from "@ant-design/icons-vue"
import { addGraphNode, updateGraphNode, updateGraphEdge, watchGraph, watchCourseGraph } from "@/services/api/graph";
import { message } from 'ant-design-vue';

const route = useRoute() //获取路由参数
const taskID = Number(route.query.taskID) 
const courseID = route.query.courseID

const title = ref('这是一个可编辑标题')
title.value = route.query.title as string || title.value // 从路由查询参数获取标题，如果没有则使用默认值

const teachPlanTitle = ref('')
const editing = ref(false)
const inputRef = ref<HTMLInputElement | null>(null)
const nodata = ref(true)
const MAX_LENGTH = 50;

const handleChange = () => {
  if (teachPlanTitle.value.length > MAX_LENGTH) {
    message.error(`输入不能超过 ${MAX_LENGTH} 个字符`);
    // 截断超长字符（防止后续逻辑异常）
    teachPlanTitle.value = teachPlanTitle.value.slice(0, MAX_LENGTH);
  }
};

function startEditing() {
  teachPlanTitle.value = title.value
  editing.value = true
  // 等 DOM 渲染完成后聚焦
  nextTick(() => {
    inputRef.value?.focus()
  })
}

function saveTitle() {
  title.value = teachPlanTitle.value.trim() || title.value
  editing.value = false

}

function cancleSaveTitle() {
  teachPlanTitle.value = title.value
  editing.value = false
}

const dialogVisible = ref(false)
const dialogTitle =ref("")

const router = useRouter()

const handleBack = () => {
  router.back()
}

// 控制是否全屏显示的状态
const isenlarge = ref(false);
// 控制是否显示3D渲染的状态
const is3D = ref(false);

// 存储从 API 获取的节点和链接数据
const staticNodes = ref<any[]>([]);
const staticLinks = ref<any[]>([]);

// 获取知识图谱数据列表
const getGraphList = async () => {
    let params : any
    let res: any

    try {
      if(taskID){
        params = {
          taskId: taskID
        }
        res= await watchGraph(params); // 调用 API
        console.log('获取知识图谱列表成功', res.data);
      }else{
        // console.log( 'type!!',typeof(courseID) )
        params = {
          course_id: courseID
        }
        res= await watchCourseGraph(params); // 调用 API
        console.log('获取知识图谱列表成功', res.data);
      }

        // res= await watchGraph(params); // 调用 API
        // console.log('获取知识图谱列表成功', res.data);
        if (res.data && res.data.nodes && res.data.edges) {
          console.log('Nodes:', res.data.nodes);
          console.log('Edges:', res.data.edges);
          staticNodes.value = res.data.nodes;
          staticLinks.value = res.data.edges.map((edge: any) => ({
              ...edge,
              source: edge.source.toString(), // 确保 source 是字符串
              target: edge.target.toString()  // 确保 target 是字符串
          }));
          // 打印转换后的数据进行调试
          console.log('转换后的 staticNodes:', staticNodes.value);
          console.log('转换后的 staticLinks:', staticLinks.value);
        }

    } catch (error) {
        console.error('获取知识图谱列表失败:', error);
    }
}

// 切换全屏显示
const toggleEnlarge = () => {
    isenlarge.value = !isenlarge.value;
};

// 切换2D/3D显示
const toggle2D3D = () => {
    is3D.value = !is3D.value;
    console.log('切换到 3D:', is3D.value); // 打印状态，用于调试
};

const form = reactive({
    label: '',
    name: '',
    content: '',
})
const emitMode = ref('');
const emitId = ref(0);

const handleConfirm = async() =>{
  dialogVisible.value = false
  if ( emitMode.value === "添加节点" ){
    let params = {
      label: form.label,
      task_id: taskID,
      properties: {
          name: form.name,
          content: form.content
      }
    };
    const response = await addGraphNode(params);
    console.log('添加节点成功', response);
    message.success('添加节点成功！');
    getGraphList()
  }
  else if ( emitMode.value === "编辑节点" ){
    let params = {
      vertex_id: emitId.value,
      properties: {
          name: form.name,
          content: form.content,
          task_id: taskID,
      }
    };
    console.log(params,"params");
    
    const response = await updateGraphNode(params);
    console.log('编辑节点成功', response);
    message.success('编辑节点成功！');
    getGraphList()
  }
  else if ( emitMode.value === "编辑边" ){
    let params = {
      edge_id: emitId.value,
      label: form.label,
    };
    const response = await updateGraphEdge(params);
    console.log('编辑边成功', response);
    message.success('编辑边成功！');
    getGraphList()
  }
}

const showName = ref(true);
const showLabel = ref(true);
const showContent = ref(true);

// 处理子组件添加节点事件
const addNode = ( { mode }: { mode: string }) => {
    showName.value = true;  
    showLabel.value = true;
    showContent.value = true;

    form.label = '';
    form.name = '';
    form.content = '';

    emitMode.value = mode;
    dialogVisible.value = true;
    dialogTitle.value = "添加节点";
};


// 处理子组件编辑节点事件
const updateNode = ( { mode, id, name, label, content }: { mode: string, id: number, name: string, label:string, content:string } ) => {
    showName.value = true;
    showLabel.value = true;
    showContent.value = true;

    form.name = name;
    form.label = label;
    form.content = content;

    emitMode.value = mode;
    emitId.value = id;
    dialogVisible.value = true;
    dialogTitle.value = "编辑节点";
};

// 处理子组件编辑边事件
const updateEdge = ( { mode, id, label}: { mode: string, id: number, label:string } ) => {
    showName.value = false;
    showLabel.value = true;
    showContent.value = false;
    form.label = label;

    emitMode.value = mode;
    emitId.value = id;
    dialogVisible.value = true;
    dialogTitle.value = "编辑边";
};

// 处理子组件删除节点或边事件
const handleDeleteNode = async ({ nodes, links }: { nodes: any[], links: any[] }) => {
    // 更新本地数据
    // staticNodes.value = nodes;
    // staticLinks.value = links;
    getGraphList()
};

//定义增加节点事件
provide('refreshGraphData', getGraphList);

onMounted(async () => {
    console.log("组件挂载完成");
    await getGraphList(); // 等待数据加载完成
});



</script>


<style scoped lang="scss">
:deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item){
  bottom: 65px;
  right: 0vw;
  width: 200px;
}
:deep(.ant-upload-list-item-container){
  width: 0;
  height: 0;
}

.custom-right-modal{
  display: flex;
  justify-content: flex-end;

    /* 控制 Modal 本体样式 */
  .ant-modal {
    margin: 0;
    height: 100vh;
    right: 0;
    top: 0;
    position: absolute;
    border-radius: 0;
    max-width: 400px; /* 控制宽度 */
  }
}



.space {
    // background-image: url('@/assets/image/img/aispacebg.png');
    // background-repeat: no-repeat;
    // background-size: cover;
    min-height: 100vh;
    display: flex;
    overflow-x: auto;

    .space-left {
        position: relative;
        transition: width 0.3s ease; // 宽度变化动画
        overflow: hidden;
        width: 169px;
        // min-width: 169px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
        border-left: 1px solid rgba(229, 229, 229, 1);
        flex-shrink: 0;

        &.collapsed {
            width: 0px; // 收起后的宽度
        }

        .left1 {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 28px 0 35px 0;
        }


        .left_item {
            width: 129px;
            height: 30px;
            opacity: 1;
            border-radius: 71px;
            margin: 0 auto 9px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 13px;
            cursor: pointer;

            &.active {
                background: rgba(63, 140, 255, 0.1);
                color: rgba(63, 140, 255, 1);
            }
        }

        .flex {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .toggle-btn {
        margin: auto;
        cursor: pointer;
        background-image: url('@/assets/image/img/ce.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        min-width: 24px;
        height: 67px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: black;
        font-weight: bold;
    }

    .space-right {
        display: flex;
        align-items: center;
        flex-direction: column;
        flex: 1;

        .title {
            font-weight: 600;
            margin-top: 270px;
            font-size: 30px;
            letter-spacing: 0px;
            line-height: 20px;
        }

        .ftitle {
            font-size: 16px;
            font-weight: 400;
            letter-spacing: 0px;
            line-height: 20px;
            margin-top: 19px
        }

        .item-container {
            width: 100%;
            display: flex;
            justify-content: center;
        }
    }

}

.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; 
  background-size: 100% 100%;
  box-sizing: border-box;
  // padding: 56px 40px;
  .page-header{
    position: relative; /* 创建层叠上下文 */
    z-index: 1; /* 确保阴影在上层 */
    min-width: 800px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    box-shadow: 0px 2px 10px  rgba(67, 143, 254, 0.1);
    padding: 0 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .back-btn {
      cursor: pointer;
      // margin-right: 20px;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .center{
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }

    .topTitle {
      // margin-left:20px;
      color: #333;
      // width: 300px;
      font-size: 20px;
      font-weight: 700;
      max-width: 300px;
      white-space: nowrap;      /* 不换行 */
      overflow-x: hidden;         /* 超出时显示横向滚动条 */  
    }

    .topTitle-input{
      padding-left:10px;
      // margin-left:20px;

      border-radius: 5px;
      border: 1px solid #3F8CFF;
      box-shadow: 0px 1px 4px  #3F8CFF;

      color: #333;
      // width: 300px;
      font-size: 20px;
      font-weight: 700;
      white-space: nowrap;      /* 不换行 */
      overflow-x: auto;         /* 超出时显示横向滚动条 */  
    }

  }
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.toolbar-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}

.toolbar-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  .addbut {
    width: 100px;
    color: #fff;
    background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
  }
}


.box-container {
  width:100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  // padding: 20px 0 30px 0;
  gap:20px;
}

.box {
  flex: 1;
  height: 100%;
  border-radius: 0.2vw;
  background-color: white;
}

.textarea {
  height: 100%;
  background: transparent;
  padding: 12px;
}

/* 图形包装器样式 */
.graph-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

/* 确保全屏模式下的尺寸正确 */
.fullscreen {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 1; 
}

/* 控制面板样式 */
.control-panel {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 1; /* 确保控制面板始终在最上层 */
}

/* 放大按钮样式 */
.enlarge-button {
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5); /* 添加半透明背景 */
    padding: 5px;
    border-radius: 4px;
}

/* 全屏模式下的放大按钮样式 */
.enlarge-button_full_screen {
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5); /* 添加半透明背景 */
    padding: 5px;
    border-radius: 4px;
}

/* 控制按钮样式 */
.control-button {
    width: 3rem;
    height: 3rem;
    text-align: center;
    line-height: 2rem;
    color: #bbb;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px;
    border-radius: 4px;
    display: inline-block;
    margin-left: 10px;
}

/* 全屏模式下的控制按钮样式 */
.control-button-fullscreen {
    color: #fff;
    /* margin-top: 8vh; */
    /* margin-right: 20vw; */
}
</style>