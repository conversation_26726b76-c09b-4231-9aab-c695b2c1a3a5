<script lang="ts" setup>
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import { courseTeacher, courseTeacherAdd, courseTeacherDelete } from '@/services/api/course';
import { getCourse,getSemester } from '@/utils/auth';
import teamTable from '@/components/teachteam/teamTable.vue';
import courseManger from '@/components/teachteam/courseManger.vue';
import back from '@/components/ui/back.vue';
import IconTrash from '~icons/lucide/trash-2'
import IconSearch from '~icons/ant-design/search-outlined'
import { message } from 'ant-design-vue';
import type { RuleObject } from 'ant-design-vue/es/form'
const isTeam = ref('1');
const teacherNameS = ref('');
const teamList = ref<any[]>([]);

const spinning = ref(false)
const total = ref(0)
function getcourseTeacher(pagination?: any) {
    spinning.value = true
    const params = {
        page:  pagination ? pagination.current : 1,
        page_size: 10,
        course_semester_id: getCourse().course_semester_id,
        // course_semester: getSemester().value,
        fuzzy: teacherNameS.value
    }
    courseTeacher(params).then((res) => {
        teamList.value = res.data.results
        spinning.value = false
        total.value = res.data.count
    }).catch(() => { 

    })
}
onMounted(() => { 
  getcourseTeacher()
})

//添加教师
const dialogImportFormVisible = ref(false)
const formImport = ref({
    teacherIds: '',
})
const importRules: { [key: string]: RuleObject[] } = {
    teacherIds: [
        { required: true, message: '请输入工号', trigger: 'blur' },
        {
            validator: (_rule, value) => {
                if (value.length > 1000) return Promise.reject('输入内容不能超过1000个字符')
                if (/[^0-9,\n\r]/g.test(value)) return Promise.reject('包含非法字符')
                return Promise.resolve()
            },
            trigger: 'blur'
        }
    ]
}

const sumbitLoading = ref(false)
async function handleImporTeachers() {
    const teacherId = formImport.value.teacherIds.split(/\n|\r|,+/).map(id => id.trim()).filter(id => id)
    console.log('导入的学号:', teacherId)
    sumbitLoading.value = true
    const params = {
        course_semester:getCourse().course_semester_id,
        // "course":getCourse().id,   // 课程ID
        "teachers":teacherId ,  // 用户ID
        "roles":teacherId.map(id => 'teacher')  // 角色
    }
    try {
        const res = await courseTeacherAdd(params)
        message.success('导入成功')
        formImport.value.teacherIds = ''
        sumbitLoading.value = false
        dialogImportFormVisible.value = false
        getcourseTeacher()
    }catch (error) {
        message.error('导入失败')
        dialogImportFormVisible.value = false
        sumbitLoading.value = false
    }
    
}

//删除教师
const chosenTeachers = ref<any>([])
async function handleSelectionChange(data: any) { 
    console.log('选择数据:', typeof data)
    if(typeof data == 'number'){
        // const confirmed = await showDeleteConfirm('确定要删除吗？删除后无法恢复！');
        // if (confirmed) {
        //     chosenTeachers.value = [data]
        //     deleteTeacher()
        // }
        chosenTeachers.value = [data]
        deleteTeacher()
        return
    }
    chosenTeachers.value = data
}
async function deleteTeacher() { 
    const confirmed = await showDeleteConfirm('确定要删除吗？删除后无法恢复！');
    if (confirmed) {
        const params = {
            ids: chosenTeachers.value,
            course_semester_id: getCourse().course_semester_id
        }
        const res = await courseTeacherDelete(params)
        message.success('删除成功')
        getcourseTeacher()
    }
    
}

</script>
<template>
    <div class="mian">
        <back :url="'-1'" />
        <div class="title flex items-center leading-[24px] mt-[20px]">
            <div class="font-bold text-[24px] max-w-[40%] flex">
                <ATooltip 
                    :title="getCourse().title" 
                    placement="top"
                    :mouseEnterDelay="0.3"
                    >
                    <div class="truncate cursor-pointer">{{ getCourse().title }}</div>
                </ATooltip>
                <div class="shrink-0">-教学团队</div>
            </div>
            <div
                class="flex ml-[24px] rounded-[94] bg-[rgba(63,140,255,0.15)] h-[25px] items-center cursor-pointer text-[14px]">
                <div class="w-[80px] text-center rounded-[94] h-[25px] leading-[25px]"
                    @click="[isTeam = '1', getcourseTeacher()]"
                    :class="isTeam == '1' ? 'bg-gradient-to-r from-[#219FFF] to-[#0066FF] font-bold text-white' : 'text-[#3F8CFF]'">
                    教学团队
                </div>
                <div class="w-[80px] text-center rounded-[94] h-[25px] leading-[25px]"
                    @click="[isTeam = '2']"
                    :class="isTeam == '2' ? 'bg-gradient-to-r from-[#219FFF] to-[#0066FF] font-bold text-white' : 'text-[#3F8CFF]'">
                    课程管理
                </div>
            </div>
        </div>
        <div class="flex items-center justify-between flex-wrap my-[20px] gap-y-4" v-if="isTeam == '1'">
            <div class="flex items-center gap-[17px] flex-wrap">
                <div class="w-[302px]">
                    <AInput class="rounded-full!" placeholder="输入教师姓名或工号搜索..." v-model:value="teacherNameS"
                    @change="getcourseTeacher">
                        <template #suffix>
                            <IconSearch class="text-foreground-4" @click="getcourseTeacher" />
                        </template>
                    </AInput>
                </div>
                <div class="text-[14px] color-[#666]">共有<span class="text-[#3F8CFF]">{{ teamList.length || 0 }}</span>条结果</div>
            </div>
            <div class="flex">
                <AButton type="primary" :disabled="chosenTeachers.length == 0" ghost class="outline-a-button  flex! items-center gap-2 mr-[15px]"
                    @click="deleteTeacher">
                    <IconTrash />
                    移除
                </AButton>
                <AButton type="primary" class="gradient-a-button w-[82px] h-[32px]"
                    @click=" dialogImportFormVisible = true">
                    添加教师
                </AButton>
            </div>
        </div>

        <a-spin :spinning="spinning">
            <teamTable v-if="isTeam == '1'" :teamList="teamList" :total="total" @selection-change="handleSelectionChange" @pagination-change="getcourseTeacher" />
        </a-spin>

        <courseManger v-if="isTeam == '2'" />


        <AModal v-model:open="dialogImportFormVisible" title="手动导入教师" width="500px" :footer="null" centered>
            <AForm :model="formImport" :rules="importRules" layout="horizontal">
                <AFormItem label="导入工号" name="teacherIds">
                    <ATextarea :rows="10" v-model:value="formImport.teacherIds" autocomplete="off" />
                    <span style="display: flex; align-items: center; margin-top: 8px;">
                        <img src="@/assets/icon/tips-black.svg" alt="自定义图标"
                            style="width: 1em; height: 1em; fill: currentColor;" />
                        <span style="margin-left: 5px;">每个工号一行，可多行输入</span>
                    </span>
                </AFormItem>
            </AForm>
            <div class="flex justify-end gap-2 w-full">
                <AButton @click="dialogImportFormVisible = false">取 消</AButton>
                <AButton type="primary" @click="handleImporTeachers" :loading="sumbitLoading">确 定</AButton>
            </div>
        </AModal>
    </div>


</template>
<style scoped lang="scss">
.mian {
    min-height: 100vh;
    background: #F0F9FF;
    display: flex;
    flex-direction: column;
    padding: 0 40px;
    background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
    background-repeat: no-repeat;
    background-size: 100% 50%;
    background-position: center bottom;
    overflow: auto;
}

:deep(.el-input__wrapper) {
    border-radius: 50px !important;
}
</style>