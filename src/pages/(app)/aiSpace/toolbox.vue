<script lang="ts">
  type Tool = {
    imgPath: string
    name: string
    description: string
    code: {
      language: string
      content: string
    }
  }

  const TOOLS: Tool[] = [
    {
      imgPath: ImgStructuredOutput,
      name: '结构化数据输出工具',
      description: `结构化数据输出 AI 工具的作用是将非结构化信息（如文档、文本、图像等）通过 AI 技术转化为符合预设格式的结构化数据，提升信息提取效率、准确性及可复用性。结构化数据输出 AI 工具的作用是将非结构化信息（如文档、文本、图像等）通过 AI 技术转化为符合预设格式的结构化数据，提升信息提取效率、准确性及可复用性。结构化数据输出 AI 工具的作用是将非结构化信息（如文档、文本、图像等）通过 AI 技术转化为符合预设格式的结构化数据，提升信息提取效率、准确性及可复用性。
结构化数据输出 AI 工具的作用是将非结构化信息（如文档、文本、图像等）通过 AI 技术转化为符合预设格式的结构`,
      code: {
        language: 'js',
        content: `function Name9（）{  return   <AutoLayout    name="Name"    overflow ="visible"    spacing={10}    padding={10}    width={815}    name="Name"    height={290}   >    <Frame    name="Frame 1!    width ="fill-parent"    height="fill-parent"   />   <Text   name="This is a text layer"   fiiil="#FFF"    fontSize={40}    fontWeight=(600}   >    This is a text layer    </Text> </AutoLayout>}`,
      },
    },
    {
      imgPath: ImgMarkdownConverter,
      name: 'Markdown 转换工具',
      description: `Markdown 转换工具的作用是将 Markdown 格式的文本转换为其他格式（如 HTML、PDF 等），以便于在不同平台和应用中使用。它可以帮助用户将 Markdown 文档转换为更易于阅读和分享的格式，同时保留原有的格式和样式。`,
      code: {
        language: 'bash',
        content: `curl -X POST https://api.example.com/v1/upload \\
  -H "Authorization: Bearer randomToken12345" \\
  -H "Content-Type: application/json" \\
  -d '{
    "userId": "abc123",
    "fileName": "example.md",
    "fileContent": "# Sample Markdown\\n\\nThis is a test file.",
    "metadata": {
      "tags": ["markdown", "test", "example"],
      "createdAt": "2023-10-01T12:00:00Z",
      "updatedAt": "2023-10-01T12:00:00Z"
    },
    "options": {
      "convertTo": "html",
      "includeStyles": true,
      "optimize": false
    }
  }' \\
  --retry 3 \\
  --retry-delay 5 \\
  --connect-timeout 10 \\
  --max-time 30 \\
  --verbose \\
  --output response.json`,
      },
    },
  ]
</script>

<script setup lang="ts">
  import ImgStructuredOutput from '@/assets/img/tools/structured-output.svg'
  import ImgMarkdownConverter from '@/assets/img/tools/markdown-converter.svg'
</script>

<template>
  <div class="h-full overflow-y-auto [--page-px:40px]">
    <div class="text-foreground-2 px-(--page-px) pt-[90px] text-xl font-bold">工具箱</div>

    <div class="@container px-(--page-px) py-6">
      <div
        class="grid grid-cols-1 gap-5 @min-[820px]:grid-cols-2 @min-[1340px]:grid-cols-3 @min-[1860px]:grid-cols-4"
      >
        <ToolboxCard
          v-for="tool in TOOLS"
          :key="tool.name"
          :img-path="tool.imgPath"
          :name="tool.name"
          :description="tool.description"
          :code="tool.code"
        />
      </div>
    </div>
  </div>
</template>
