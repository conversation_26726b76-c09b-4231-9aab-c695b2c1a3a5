<script lang="ts">
  import type { ShallowRef } from 'vue'

  const kbDetailContextInjectionKey = Symbol('kb-detail') as InjectionKey<{
    rawKb: Readonly<ShallowRef<API.Kb.Kb | undefined>>
  }>

  export function injectKbDetailContext() {
    const context = inject(kbDetailContextInjectionKey)
    if (!context) {
      throw new Error('kbDetailContext is not provided')
    }
    return context
  }

  const TABS = ['files', 'settings', 'retrieval', 'qa']
</script>

<script setup lang="ts">
  import { TabsRoot, TabsContent } from 'reka-ui'
  import { useRouteQuery } from '@vueuse/router'

  import IconLeft from '~icons/lucide/arrow-left'

  const persistedTab = useRouteQuery<string | undefined>('tab')
  const modelValue = ref<string>('files')
  watch(
    persistedTab,
    (newVal, oldVal) => {
      if (TABS.includes(newVal as string)) {
        modelValue.value = newVal as string
      } else {
        modelValue.value = oldVal ?? 'files'
      }
    },
    { immediate: true },
  )
  watch(modelValue, (newVal) => {
    persistedTab.value = newVal
  })

  const route = useRoute('/(app)/aiSpace/kb/[id]')

  const { queryOptions } = useKbDetail({ kbId: route.params.id })
  const { data: rawKb, suspense } = useQuery(queryOptions)

  await useNavigationSuspense({ delay: 500, suspense })

  provide(kbDetailContextInjectionKey, {
    rawKb,
  })
</script>

<template>
  <TabsRoot
    class="h-full overflow-y-auto"
    default-value="files"
    v-model="modelValue"
    :unmount-on-hide="false"
  >
    <div class="px-(--page-px) pt-[50px] pb-5">
      <button
        class="text-foreground-2 flex cursor-pointer items-center gap-1.5"
        @click="$router.go(-1)"
      >
        <IconLeft />
        返回
      </button>
    </div>

    <div class="flex items-center space-x-5 px-(--page-px)">
      <TextEllipsis
        :text="rawKb?.name ?? ''"
        :lines="1"
        :tooltip="{ title: rawKb?.name, placement: 'top' }"
        class="text-foreground-2 max-w-2/5 text-xl font-bold"
      />

      <TabsList
        :items="[
          { label: '文件', value: 'files' },
          { label: '设置', value: 'settings' },
          { label: '智能检索', value: 'retrieval' },
          { label: '问答对', value: 'qa' },
        ]"
        :item-width="94"
      />
    </div>

    <TabsContent
      value="files"
      tabindex="-1"
    >
      <KbFilesPage :kb-id="route.params.id" />
    </TabsContent>

    <TabsContent
      value="settings"
      tabindex="-1"
    >
      <KbSettingsPage>
        <template #basic-settings>
          <KbBasicSettingsFormItems />
        </template>

        <template #chunk-settings>
          <KbChunkSettingsFormItems />
        </template>
      </KbSettingsPage>
    </TabsContent>

    <TabsContent
      value="retrieval"
      tabindex="-1"
    >
      <KbRetrievalPage :kb-id="route.params.id" />
    </TabsContent>

    <TabsContent
      value="qa"
      tabindex="-1"
    >
      <KbQaPage :kb-id="route.params.id" />
    </TabsContent>
  </TabsRoot>
</template>
