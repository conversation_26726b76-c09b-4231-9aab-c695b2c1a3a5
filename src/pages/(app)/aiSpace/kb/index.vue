<script setup lang="ts">
  import { useFuse } from '@vueuse/integrations/useFuse'
  import type { MutationStatus } from '@tanstack/vue-query'

  import type { KbCard } from '@/components/kb/KbCard.vue'

  import IconSearch from '~icons/ant-design/search-outlined'

  const isCreateModalOpen = ref(false)

  const searchString = ref('')
  const searchStringDebounced = refDebounced(searchString, 200)

  const { queryOptions } = useKbList()
  const { data: rawKbs, suspense } = useQuery(queryOptions)

  await useNavigationSuspense({ delay: 500, suspense })

  const kbsTransformed = computed<KbCard[]>(
    () =>
      rawKbs.value?.map((kb) => ({
        id: kb.id,
        name: kb.name,
        fileNum: kb.document_count,
        description: kb.description,
        courses: kb.course_names,
      })) ?? [],
  )

  const { results } = useFuse(searchStringDebounced, kbsTransformed, {
    fuseOptions: { keys: ['name'], threshold: 0.3 },
  })

  const kbs = computed(() => {
    if (!searchStringDebounced.value) {
      return kbsTransformed.value
    }
    return results.value.map((result) => result.item)
  })

  function createKb() {
    isCreateModalOpen.value = true
  }

  const createKbStatus = ref<MutationStatus>('idle')
  watchEffect(() => {
    if (createKbStatus.value === 'success') {
      isCreateModalOpen.value = false
    }
  })
</script>

<template>
  <div class="flex h-full flex-col pt-22.5">
    <div class="text-foreground-2 px-(--page-px) text-xl font-bold">知识库</div>
    <div class="flex flex-wrap items-center gap-y-4 px-(--page-px) pt-7.5 pb-6.5">
      <AInput
        class="max-w-[300px] rounded-full!"
        placeholder="搜索知识库名称..."
        v-model:value="searchString"
      >
        <template #suffix>
          <IconSearch class="text-foreground-4" />
        </template>
      </AInput>

      <div class="text-foreground-3 ml-4">
        共有
        <span class="text-primary">{{ kbs.length }}</span>
        个筛选结果
      </div>

      <div class="ml-auto flex min-w-max items-center">
        <AButton
          class="gradient-a-button"
          type="primary"
          @click="createKb"
        >
          创建知识库
        </AButton>
      </div>
    </div>

    <div class="@container grow overflow-y-auto contain-size">
      <Empty
        v-if="kbs.length === 0"
        class="mx-auto mt-20 w-[335px]"
      >
        <template #message> 暂无数据 </template>
      </Empty>

      <div
        v-else
        class="grid grid-cols-1 gap-5 px-(--page-px) pt-1 pb-7.5 @min-[820px]:grid-cols-2 @min-[1340px]:grid-cols-3 @min-[1860px]:grid-cols-4"
      >
        <KbCard
          v-for="kb in kbs"
          v-bind="kb"
          :key="kb.id"
          class="flex-1"
        />
      </div>
    </div>

    <AModal
      v-model:open="isCreateModalOpen"
      title="创建知识库"
      :ok-button-props="{
        htmlType: 'submit',
        // @ts-expect-error antdv poor typing
        form: 'kb-create-form',
        loading: createKbStatus === 'pending',
      }"
      centered
      destroy-on-close
      wrap-class-name="reset-ant-modal"
    >
      <KbCreateForm
        id="kb-create-form"
        @update:status="createKbStatus = $event"
      />
    </AModal>
  </div>
</template>
