<template>
    <div class="space">
        <div class="space-right">
            <div class="title mt-[20px]">欢迎来AI空间</div>
            <div class="ftitle text-base"></div>
             <!-- grid-cols-1 gap-[22px] lg:gap-[162px]! md:gap-[100px]! lg:grid-cols-3 -->
               <!-- lg:gap-[162px]! md:gap-[30px]! lg:grid-cols-3  mt-[63px] -->
            <div class="grid grid-cols-1 gap-[22px] md:gap-[30px] 2xl:gap-[162px] lg:grid-cols-3 mt-[63px]">
                <img @click="interTeachPlan()" class="h-[227px] w-[300px] cursor-pointer" src="@/assets/image/img/aiteachplan.png" />
                <img @click="interPptSquare()" class="h-[227px] w-[300px] cursor-pointer" src="@/assets/image/img/aippt.png" />
                <img @click="interQuestion()" class="h-[227px] w-[300px] cursor-pointer" src="@/assets/image/img/aiquetion.png" />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router'

const router = useRouter()
function interTeachPlan() {
    router.push('/lessonPlan')
};

function interPptSquare() {
    router.push('/pptSquare')
};
const interQuestion = () => {
    router.push({
        path: '/quetionList',
        query: {
            type: 'space'
        }
    })
};


</script>
<style scoped lang="less">
.space {
    // background-image: url('@/assets/image/img/aispacebg.png');
    // background-repeat: no-repeat;
    // background-size: cover;
    min-height: 100vh;
    display: flex;
    overflow-x: auto;


    .toggle-btn {
        margin: auto;
        cursor: pointer;
        background-image: url('@/assets/image/img/ce.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        min-width: 24px;
        height: 67px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: black;
        font-weight: bold;
    }

    .space-right {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        flex: 1;

        .title {
            font-weight: 600;
            // margin-top: 270px;
            font-size: 30px;
            letter-spacing: 0px;
            line-height: 20px;
        }

        .ftitle {
            font-size: 16px;
            font-weight: 400;
            letter-spacing: 0px;
            line-height: 20px;
            margin-top: 19px
        }

        .item-container {
            width: 100%;
            display: flex;
            justify-content: center;
        }
    }

}
</style>
