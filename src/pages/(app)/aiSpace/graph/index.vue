<template>
  <div class="page">
      <!-- <div @click="handleBack" class="back-btn">
        <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
        <span style="font-size: 14px;line-height: 14px;">返回</span>
      </div> -->
      <div class="text">知识图谱</div>
      <div class="toolbar gap-y-2">
        <div class="toolbar-left">
          <!-- <el-select v-model="valueSelect" placeholder="课程选择" style="width: 100px;min-height: 32px;"
            suffixIcon="CaretBottom">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select> -->
          <a-input v-model:value="searchValue" style="border-radius: 94px;width: 302px;min-height: 32px;" placeholder="输入标题查询" @pressEnter="getGraphList">
            <template #suffix>
              <SearchOutlined style="color:#C4C4C4" @click="getGraphList"/>
            </template>
          </a-input>
          <div class="res-text">共有<text style="color: rgba(63, 140, 255, 1);">{{ total }}</text>个筛选结果</div>
        </div>
        <div class="toolbar-right">
            <AButton
                type="primary"
                class="gradient-a-button w-[82px]"
                @click="openCreate = true"
                >
                新建
            </AButton>
            <a-button 
                type="primary" 
                style="display: flex;justify-content: center;align-items: center;" 
                ghost 
                :icon="h(DeleteOutlined)"
                @click="delGraph(selectedIds)" 
                >
                删除
            </a-button>

        </div>
      </div>
      <a-spin :spinning="spinning">
        <div class="table">
          <div class="table-head height" style="font-weight:600">
            <div class="item" style="max-width: 56px;">
              <a-checkbox v-model:checked="state.checkAll" :indeterminate="state.indeterminate"
                @change="onCheckAllChange" />
            </div>
            <div class="item" style="width: 400px;">知识图谱名称</div>
            <div class="item" style="width: 130px;">知识点数量</div>
            <div class="item" style="width: 140px;text-align: center;">状态</div>
            <div class="item" style="max-width: 118px;text-align: center;">操作</div>
          </div>

          <div class="center" v-if="files?.length === 0">
            <img src="@/assets/image/zanwu/nodata.png" style="width: 385px;height: 379px;" />
          </div>
          <div v-else>
            <div class="table-head height1" v-for="item in files" :key="item.id">
              <div class="item" style="max-width: 58px;">
                <a-checkbox :checked="selectedIds.includes(item.id)"
                  @change="(e: any) => handleCheckboxChange(item.id, e.target.checked)" />
              </div>
              <div class="item file-name titlew">
                <div class="file-name-text">
                  {{ item.name }}
                </div>
                <!-- <span class="edit-icon" style="">
                  <a-tooltip placement="bottom">
                    <template #title>
                      <span>编辑</span>
                    </template>
                    <img style="cursor: pointer;width: 14px;height: 14px;" src="@/assets/image/img/edittable.png" @click="editGraph(item)" />
                  </a-tooltip>
                </span> -->
              </div>
              <div class="item" style="width: 130px;">
                {{ item.node_count === 'null' || item.node_count == null ? '无' : item.node_count}}
              </div>
              <div class="item" style="width: 140px; display:flex; justify-content:center;">
                  <div style="" class="stutas" :class="{
                      'stutas1': item.status === 'failed',
                      'stutas2': item.status === 'completed',
                      'stutas3': item.status === 'processing' || item.status === 'pending', 
                  }">
                      {{ item.status === 'failed' ? '失败' : item.status === 'completed' ? '已完成' : item.status === 'pending' || 'processing' ? '解析中' : '暂无' }}
                  </div>
              </div>
              <div class="item" style="max-width:118px; display:flex; justify-content:center; gap:30px">
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>关联</span>
                  </template>
                  <img style="cursor: pointer;" src="@/assets/image/graph/relation.png" @click="relation(item.id, item.course_title)"/>
                </a-tooltip>
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>删除</span>
                  </template>
                  <img style="cursor: pointer;" src="@/assets/image/graph/delete.png"  @click="delGraph([item.id])"/>
                </a-tooltip>
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>查看</span>
                  </template>
                  <img style="cursor: pointer;" src="@/assets/image/graph/eye.png" @click="watch(item.id,item.name)"/>
                </a-tooltip>

              </div>
            </div>
          </div>

          <div class="pagination">
            <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
              layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </div>
      </a-spin>

        <a-modal v-model:open="openCreate" title="创建知识图谱" :footer="null" width="700px">
            <div style="margin: 50px 0 0 0;">
                <a-form :model="graphForm" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }"
                    autocomplete="off">
                    <a-form-item
                        label="课程名称" 
                        name="title" 
                        v-bind="validateInfos.title"
                        :rules="[{ required: true, message: '请输入课程名称!' }]"
                    >
                        <a-input v-model:value="graphForm.title" placeholder="请输入课程名称" />
                    </a-form-item>


                    <a-form-item
                        label="上传附件" 
                        name="file" 
                        v-bind="validateInfos.file"
                        :rules="[{ required: true, message: '请上传附件!' }]"
                    >

  
                    <a-upload 
                      :file-list="fileList" 
                      :maxCount="1" accept=".pdf,.doc,.docx,.txt" 
                      @remove="handleRemove" 
                      :before-upload="beforeUpload"
                    >
                      <div style="display: flex; align-items: center; gap:8px">
                          <a-button style="width: 79px;height:32px;color: #666; display: flex; justify-content: center; align-items: center;">
                              <UploadOutlined />文件            
                          </a-button>

                          <div style="flex:1; height: 17px; display: flex; font-size: 12px; line-height: 16.64px; gap:5px">
                              <div style="display: inline-flex;justify-content: center;">
                                  <ExclamationCircleOutlined/>
                              </div>
                              <span>
                                  支持PDF、DOC、DOCX、TXT,单个文件大小不超过5MB
                              </span>
                          </div>
                      </div>
                    </a-upload>


                    </a-form-item>

                    <div style="display: flex;justify-content: flex-end">
                        <a-form-item>
                            <a-space>
                                <a-button @click="openCreate = false">取消</a-button>
                                <a-button type="primary" html-type="submit" @click="createNewGraph">确定</a-button>
                            </a-space>
                        </a-form-item>
                    </div>
                </a-form>
            </div>
        </a-modal>

        <a-modal v-model:open="openRelation" title="关联课程" :footer="null" width="700px">
            <div style="margin: 50px 0 0 0;">
                <a-form :model="courseForm" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }"
                    autocomplete="off">

                    <a-form-item
                        label="已绑定" 
                        v-bind="validateInfos.file"
                    >

                    <div style="display: flex; align-items: center; gap:8px">

                        <text>{{ course_title == null ? '未绑定课程' : course_title }}</text>
                        <a-tooltip placement="right">
                          <template #title>
                            <span>选择新课程后，旧课程自动解绑</span>
                          </template>
                          <ExclamationCircleOutlined style="cursor: pointer;"/>
                        </a-tooltip>

                    </div>
                    </a-form-item>

                    <a-form-item
                        label="关联课程" 
                        name="course" 
                        v-bind="validateInfos.course"
                        :rules="[{ required: true, message: '请输入课程名称!' }]"
                    >
                          <a-select
                              v-model:value="courseValue"
                              show-search
                              placeholder="选择课程"
                              :options="courseOptions"
                              :filter-option="filterOption"
                              @focus="handleFocus"
                              @blur="handleBlur"
                              @change="handleChange"
                          ></a-select>
                    </a-form-item>

                    <div style="display: flex;justify-content: flex-end">
                        <a-form-item>
                            <a-space>
                                <a-button @click="openRelation = false">取消</a-button>
                                <a-button type="primary" html-type="submit" @click="relationCourse">确定</a-button>
                            </a-space>
                        </a-form-item>
                    </div>
                </a-form>
            </div>
        </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { formatDate } from '@/utils/util'
//引入接口
import { usePagination } from '@/hooks/usePagination'; //解构分页参数
import { ref, computed, onMounted, reactive, h } from 'vue'
import { Search,} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import { graphList, createGraph, deleteGraph, associateCourse } from "@/services/api/graph";
import { Form } from 'ant-design-vue';
import { DeleteOutlined,SearchOutlined,UploadOutlined,ExclamationCircleOutlined} from '@ant-design/icons-vue';
import type { UploadProps, SelectProps } from 'ant-design-vue';
import { courseOwner } from '@/services/api/course';


//创建图谱弹窗开关
const openCreate = ref<boolean>(false);
const graphForm = reactive({
    title: '',
    file: null as File | null
});

const useForm = Form.useForm
const { validateInfos } = useForm(graphForm);//验证表单提交

const fileList = ref<UploadProps['fileList']>([])

const handleRemove: UploadProps['onRemove'] = file => {
  fileList.value = [];
};
const beforeUpload: UploadProps['beforeUpload'] = file => {
  if (fileSizeCheck(file, 5 * 1024 * 1024)) {
    message.error('文件大小不能超过5M');
    return
  }
  fileList.value = [file];
  graphForm.file = file;
  return false;
};

const task_ID = ref('')
const course_title = ref('')
//关联课程弹窗开关
const openRelation = ref<boolean>(false);
const relation = (itemID:any,courseTitle:string) => {
  openRelation.value = true
  task_ID.value = itemID
  course_title.value = courseTitle
}
const courseValue = ref();

const courseForm = reactive({
    course: '',
});
const courseOptions = ref([{
    value: 0,
    label: '未关联课程'
}])

const handleChange = (value: any) => {
  console.log(`selected ${value}`);
  courseForm.course = value
};
const handleBlur = () => {
  console.log('blur');
};
const handleFocus = () => {
  console.log('focus');
};
const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 图谱关联课程
async function relationCourse () {
  const params = {
      course_id: courseForm.course,
  }
  const id = parseInt(task_ID.value)
  associateCourse(id, params).then(res => {
      message.success('关联成功')
      getGraphList()
      openRelation.value = false
  })        
  .catch(error => {
      message.error(`关联失败: ${error.message || '未知错误'}`);
  });
}

function getcourseOwner() {
    // const data = [{ id: 1, title: '建筑学' }]
    courseOwner().then((res:any) => {
      // console.log(res,"datadatadata")
        courseOptions.value = []
        res.data.forEach((item: any) => {
            courseOptions.value.push({
                value: item.id,
                label: item.title
            })
        })
    })
}

const router = useRouter()

//当前页所有id
const currentPageIds = computed(() => files.value?.map(item => item.id) || []);
// 已选中的id
const selectedIds = ref<number[]>([]);
// 处理单个复选框的选择变化
const handleCheckboxChange = (id: number, checked: boolean) => {
  if (checked) {
    selectedIds.value.push(id);
  } else {
    selectedIds.value = selectedIds.value.filter((item) => item !== id);
  }
};
// 全选操作
const onCheckAllChange = (e: any) => {
  const isChecked = e.target.checked;
  if (isChecked) {
    selectedIds.value = [...new Set([...selectedIds.value, ...currentPageIds.value])]; // 合并去重
  } else {
    selectedIds.value = selectedIds.value.filter(id => !currentPageIds.value.includes(id));
  }
  state.indeterminate = false;
};
const state = reactive({
  indeterminate: false,
  checkAll: false,
  checkedList: [],
});


const files = ref<any[]>()
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getGraphList);
const spinning = ref(false)

const searchValue = ref('')

onMounted(() => {
  getGraphList() //  获取图谱列表
  getcourseOwner() // 获取课程列表
})

// 获取图谱列表
async function getGraphList () {
  const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      name: searchValue.value
  }
  try {
    spinning.value = true
    console.log(params)
    const res = await graphList(params)
    files.value = res.data.results
    console.log(files.value)
    total.value = res.data.count
    spinning.value = false

  } catch (error) {
    console.error('获取知识图谱列表失败:', error)
  }
}

// 创建图谱列表
async function createNewGraph () {
  const params = {
      name: graphForm.title,
      file: graphForm.file
  }
  createGraph(params).then(res => {
      message.success('创建成功')
      getGraphList()
      openCreate.value = false
  })        
  .catch(error => {
      message.error(`创建失败: ${error.message || '未知错误'}`);
  });
}

const handleBack = () => {
  //需要动态配置课程id
  router.back()
}

const watch = (id:number,name:string) => {
  // 跳转到查看页面
  router.push({
    path: `/graphInfo`,
    query: { taskID: id ,title: name }
  })
}


//删除知识图谱
async function delGraph(list:any[]){
    const confirmed = await showDeleteConfirm('确定删除本记录吗?删除后将自动解除绑定!');
    const params = {
      ids: list,
    }
    console.log(params, 'params');
    if (confirmed) {
        deleteGraph(params).then(res => {
            message.success('删除成功')
            getGraphList()
        })
        .catch(error => {
          message.error(`删除失败: ${error.message || '未知错误'}`);
        });
    }
}


</script>

<style scoped lang="scss">
:deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item){
  width: 200px;
}

:deep(.el-input__suffix-inner) {
    cursor: pointer;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;

  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 90px 40px 50px 40px;

  .back-btn {
    cursor: pointer;
    width: 60px;
    margin-top: 50px;
    color: #333;
    display: flex;
    align-items: center;
  }

}

.text {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0px;
  // line-height: 20px;
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  min-width: 900px;
  flex-wrap: wrap;
  
  .toolbar-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
    .res-text{
        margin-left: 15px;
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 0px;
        color: rgba(102, 102, 102, 1);
    }

  }

  .toolbar-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;

    .addbut {
      width: 90px;
      color: #fff;
      background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
    }
  }
}

:deep(.el-select__wrapper) {
  border-radius: 95px;
}


:deep(.el-input__wrapper) {
  border-radius: 94px;
}


.table {
  background: #fff;
  border-radius: 4.79px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 0 20px 20px;
  min-width: 900px;
  width: 100%;


  .table-head {
    // display: flex;
    align-items: center;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    /* 禁止换行 */
    overflow-x: auto;
    /* 允许水平滚动（如果需要） */
    font-size: 14px;
    padding-left: 12px;

    .item {
      flex: 1 0 auto;
      white-space: nowrap;
      /* 防止文字换行 */
    }

    .flex {
      display: flex;
      align-items: center;
    }

    .titlew {
      width: 400px;
      position: relative;
      height: 56px;
      line-height: 56px;
    }

    .file-name-text {
      max-width: 330px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden
    }

    .file-name .edit-icon {
      opacity: 0;
      transition: opacity 0.3s ease;
      position: absolute;
      left: 345px;
      top: 50%;
      transform: translateY(-50%);
    }

    .file-name:hover .edit-icon {
      opacity: 1;
    }

  }


  .stutas {
    width: 60px;
    height: 22px;
    line-height: 22px;
    font-size: 12px;
    font-weight: 700;
    opacity: 1;
    border-radius: 2px;
    text-align: center;
  }
  .stutas1{
      border: 1px solid #FFA39E;
      background: #FFF1F0;
      color: #FF4D4F;
  }
  .stutas2{
      border-radius: 2px;
      background: rgba(0, 0, 0, 0.04);
      border: 1px solid rgba(0, 0, 0, 0.15);
      color: rgba(0, 0, 0, 0.65);
  }
  .stutas3{
      background: #F6FFED;
      border: 1px solid #B7EB8F;
      color: #52C41A;
  }

  .height {
    height: 56px;
    border-bottom: 1px solid #E5E5E5;
  }

  .height1 {
    height: 58px;
    border-bottom: 1px solid #E5E5E5;
    color: #666666;

    &:hover {
      background: rgba(63, 140, 255, 0.05);
    }
  }
}

.pagination {
  margin: 20px 0 12px 0;
  text-align: center;
}

:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}
</style>
