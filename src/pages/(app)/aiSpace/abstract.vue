<template>
  <div class="page">
      <div class="toolbar">
        <div class="toolbar-text">
            <span>摘要提取</span>
        </div>
      </div>
      
      <div class="box-container">
        <div class="box-left">
            <div class="top">
              <a-select class="but" v-model:value="countSelect" placeholder="摘要长度">
                  <a-select-option value = '50'>摘要长度-短</a-select-option>
                  <a-select-option value = '250'>摘要长度-中</a-select-option>
                  <a-select-option value = '500'>摘要长度-长</a-select-option>
              </a-select>
              <a-upload :file-list="fileList" :maxCount="1" accept=".pdf,.doc,.docx,.txt"  @remove="handleRemove" :before-upload="beforeUpload">
                <a-button class="but" style="height: 50px;width: 120px; border: 1.5px solid rgba(230, 239, 255, 1);display: inline-flex;align-items: center;">
                  上传附件<PaperClipOutlined />
                </a-button>
              </a-upload>
            </div>
            <div class="center">
              <div class="textarea" v-html="parseMarkdown(content)"></div>
            </div>
            <div class="bottom">
              <a-button type="primary" class="addbut" style="padding: 2px;" @click="getAbstract">
                <img src="@/assets/image/abstract/abstract.png"/>
                摘要提取
              </a-button>
            </div>
            
        </div>
        <div class="box-right">
            <div class="text">
              <span>生成结果</span>
            </div>
            <div class="content">
              <a-spin :spinning="spinning">
                <div class="textarea" v-html="parseMarkdown(streamingContent)"></div>
              </a-spin>
            </div>
        </div>
      </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { ref, onMounted, provide } from "vue";
import { PaperClipOutlined } from '@ant-design/icons-vue';
import { fileAnalysis, createAbstract, } from "@/services/api/abstract";
import type { UploadProps } from 'ant-design-vue';
import { parseMarkdown } from "@/utils/markdownParser";
import { message } from 'ant-design-vue';

const spinning = ref(false);
const router = useRouter()

const handleBack = () => {
  router.back()
}

const countSelect = ref()

const fileList = ref<UploadProps['fileList']>([])
const fileValue = ref<File>();
const handleRemove: UploadProps['onRemove'] = file => {
  fileList.value = [];
};

const beforeUpload: UploadProps['beforeUpload'] = file => {
  if (fileSizeCheck(file, 50 * 1024 * 1024)) {
    message.error('文件大小不能超过50M');
    return
  }
  fileList.value = [file];
  fileValue.value = file;
  return false;
};

const content = ref('')
const abstractRes =ref('')

watch(fileValue, (newValue, oldValue) => {
  if (newValue){
    console.log(`count changed from ${oldValue} to ${newValue}`)
    getAnalysis()
  }
})

const getAnalysis = async () => {
  const params = {
    file: fileValue.value
  }
  try {
    const res = await fileAnalysis(params)
    // console.log(res.data.content_preview,"resresresres");
    content.value = res.data.content_preview
    message.success('文件解析成功！');
  } catch (error) {
    message.error('文件解析失败');
    console.error('文件解析失败:', error);
  }
}


const streamingContent = ref('')

const getAbstract = async () => {
  if(!countSelect.value){
    message.error('请选择提取长度!');
  }
  else if (!fileValue.value){
    message.error('请上传附件!');
  }
  else {
    spinning.value = true
    const params = {
      count: countSelect.value,
      document_content: content.value,
    }
    try {
      // 发起 POST 请求，将文件和主题一起发送到后端
      const res = await createAbstract(params)

      if (!res.ok) {
        throw new Error('请求失败');
      }
      if (!res.body) {
        console.error('响应体为空');
        return;
      }
      const reader = res.body.getReader();
      const decoder = new TextDecoder();
      let done = false;
      let chunk = '';

      // 读取流式数据并逐步更新页面内容
      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        chunk += decoder.decode(value, { stream: true });
        streamingContent.value = chunk; 
      }
      if (done){
        spinning.value = false;
        const obj = JSON.parse(streamingContent.value);
        streamingContent.value = obj.summary
        message.success('摘要提取成功！');
      }
    } catch (error) {
      message.error('流式请求失败');
    }
  }
}

// onMounted(async () => {
//     const res = await axios.get('http://************:8000/v1/analysis/markify/', {
//       headers: {
//         'Content-Type': 'application/json'
//       }
//     })
//     content.value = res.data
// });


</script>


<style scoped lang="scss">
:deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item){
  bottom: 43px;
  left: 130px;
  width: 200px;
}


.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;

  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 90px 40px 50px 40px;
  overflow: auto;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.toolbar-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}


.addbut {
  width: 120px;
  height: 32px;
  border-radius: 150px;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 9px 20px 9px 20px;
  gap:10px;
  background: linear-gradient(90deg, rgba(63, 140, 255, 1) 0%, rgba(21, 192, 230, 1) 100%);
}

.box-container {
  width:100%;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px 0;
  gap:20px;
}

.box-left {
  flex: 7;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  background-color: white;
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05); 
  padding: 30px;
  display: flex;
  flex-direction: column;
  
  .top {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap:16px;
  }

  .center {
    flex: 1;
    border-radius: 5px;
    margin: 20px 0;
    border: 1.5px solid rgba(230, 239, 255, 1);

  }

  .bottom {
    display: flex;
    justify-content: right;
  }
}

.but{
  :deep(.ant-select-selector){
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 360px; 
    height: 50px; 
    border: 1.5px solid rgba(230, 239, 255, 1);
  }

}

.box-right {
  flex: 3;
  height: 100%;
  border-radius: 5px;
  background-color: white;
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05); 
  padding: 20px;
  display: flex;
  flex-direction: column;


  .text {
    height: 60px;
    padding-top: 30px;
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 16px;
    color: rgba(51, 51, 51, 1);
    display: flex;
    justify-content: center;
  }

  .content{
    flex: 1;
    border-radius: 5px;
    margin-top: 20px;
    border: 1.5px solid rgba(230, 239, 255, 1);
    background: rgba(230, 239, 255, 0.2);
  }

}
  .textarea {
    width: 100%;
    overflow: auto;
    max-height: 680px;
    background: transparent;
    padding: 12px;

    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-all;
  }


</style>