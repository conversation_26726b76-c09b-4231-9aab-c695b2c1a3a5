<template>
  <div class="page">
    <div class="page-content">
      <back :url="'-1'"></back>
      <h1 class="text">
        <span style="color: rgba(153, 153, 153, 1);cursor: pointer;" @click="() => $router.go(-1)">PPT管理 </span>
        <span>/ 回收站</span>
      </h1>

      <div class="toolbar">
        <div class="toolbar-left">
          <div style="display: flex;">
            <a-button type="primary" :disabled="selectedIds.length == 0" ghost class="but" style="width: 80px;height: 30px;" @click="restorePPT(selectedIds)">
              <backIcon></backIcon>
              还原
            </a-button>
            <a-button type="primary" 
            :disabled="selectedIds.length == 0"
            class="but" style="width: 80px;height: 30px;margin-left: 10px;" @click="delMessage(selectedIds)" ghost>
              <IconTrash />
              删除
            </a-button>
          </div>
        </div>
        <div class="toolbar-right">
          <AInput class="rounded-full!" placeholder="输入文件名称..." v-model:value="searchValue" @change="getPptList">
            <template #suffix>
              <IconSearch class="text-foreground-4" @click="getPptList" />
            </template>
          </AInput>
        </div>
      </div>

      <a-spin :spinning="spinning">
        <pptTableRecyle 
          :pptTemTableList="files" 
          :total="total" 
          @pagination-change="getPptList"
          @selection-back="selectionBack" 
          @selection-change="selectionChange" 
          @selection-check="selectionCheck"
        />
      </a-spin>

    </div>
  </div>
</template>

<script lang="ts" setup>
import qs from 'qs'; // 需安装 qs 库：npm install qs
import backIcon from '~icons/ic/baseline-settings-backup-restore'
import IconSearch from '~icons/ant-design/search-outlined'
import pptTableRecyle from '@/components/ppt/pptTableRecyle.vue';
import IconTrash from '~icons/lucide/trash-2'
import { formatDate } from '@/utils/util'
import Back from '@/components/ui/back.vue'
import { DeleteOutlined, DownloadOutlined, UndoOutlined } from '@ant-design/icons-vue';
import { ref, markRaw, onMounted, computed, reactive, h } from 'vue'
import { Search, WarningFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { pptList, ppt_softDelete, ppt_hardDelete } from '@/services/api/ppt'
import { usePagination } from '@/hooks/usePagination'; //解构分页参数

import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import { message } from 'ant-design-vue';

const router = useRouter()
const searchValue = ref('')

//定义列表数据结构
// 模拟数据
const files = ref([])
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getPptList);
const spinning = ref(false)

onMounted(() => {
  getPptList()
})

//还原
function selectionBack(data: any) {
  restorePPT(data)
}
//删除
function selectionChange(data: any) {
  delMessage(data)
}
const selectedIds = ref([]);
//选中
function selectionCheck(data: any) {
  selectedIds.value = data
}

// 获取PPT列表
async function getPptList(pagination?: any) {
  const params = {
    // page: currentPage.value,
    page: pagination ? pagination.current : 1,
    pageSize: 10,//pageSize.value,
    is_deleted: 'true',
    title: searchValue.value
  }
  const queryString = qs.stringify(params, {
      arrayFormat: 'repeat', // 生成 key=value&key=value
      skipNulls: true,      // 跳过空值
  });
  try {
    spinning.value = true
    const res = await pptList(queryString)
    files.value = res.data.results
    total.value = res.data.count
    spinning.value = false
  } catch (error) {
    console.error('获取PPT列表失败:', error)
  }
}

const delMessage = async (params?: any) => {
  const ids = Array.isArray(params) ? params : [params]
  const confirmed = await showDeleteConfirm('确定删除本记录吗？删除后无法恢复！');
  if (confirmed) {
    const params = {
      "ids": ids,  //传单个及单个删除
      "action": "delete"  //restore:恢复，delete：软删除
    }
    const res = await ppt_hardDelete(params)
    getPptList()
  }
  console.log(confirmed);
}


async function restorePPT(data: any) {
  const ids = Array.isArray(data) ? data : [data]
  const params = {
    "ids": ids,  //传单个及单个删除
    "action": "restore"  //restore:恢复，delete：软删除
  }
  try {
    const res = await ppt_softDelete(params)
    getPptList()
  } catch (error) {
    message.error('删除失败')
  }
}

</script>

<style scoped lang="scss">
:deep(.el-input__suffix-inner) {
  cursor: pointer;
}

:deep(.el-select__wrapper) {
  border-radius: 95px;
}


:deep(.el-input__wrapper) {
  border-radius: 94px;
}

.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  display: flex;
  align-items: center;
  height: 6vh;
  background-color: #ffffff;
  box-sizing: border-box;
}


.page-content {
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0 40px;
  flex: 1;
  overflow: auto;
  /* 关键属性 - 填充剩余空间 */
}

.text {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 12px;
  margin: 20px 0;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  max-width: 1720px;
  min-width: 900px;

  .toolbar-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 36px;

    .addbut {
      width: 82px;
      color: #fff;
      background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
    }

    .but {
      display: flex;
      justify-content: center;
      align-items: center;
    }

  }

  .toolbar-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
}

</style>
