<script setup lang="ts">
import IconTrash from '~icons/lucide/trash-2'
import backIcon from '~icons/ic/baseline-settings-backup-restore'
import { CloudUploadOutlined } from '@ant-design/icons-vue';
import IconSearch from '~icons/ant-design/search-outlined'
import pptTemTable from '@/components/ppt/pptTemTableRecycle.vue';
import { message } from 'ant-design-vue';
import { pptTmpList, pptTmpHardDelete,pptTmpSoftDelete } from '@/services/api/ppt';
import { fileSizeCheck } from '@/utils/util';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
const pptTemTableList = ref([])
const spinning = ref(false)
//获取模板列表
const total = ref(0)
const pptemTitle = ref('')
function getpptTmpList(pagination?: any) {
    console.log(pagination, 'pagination');
    spinning.value = true
    const params = {
        title:pptemTitle.value,
        page: pagination ? pagination.current : 1,
        page_size: 10,
        is_deleted: true
    }
    pptTmpList(params).then(res => {
        pptTemTableList.value = res.data.results
        total.value = res.data.count
        spinning.value = false
    })
}
//selectionBack还原
async function selectionBack(data?: any) {
    const params = {
        "ids": Array.isArray(data) ? data : [data],  //传入需要删除的id列表
        action:'restore'
    }
    try {
        const res = await pptTmpSoftDelete(params);
        message.success('还原成功');
        getpptTmpList()
    } catch (err: any) {
        message.error(err?.message || '删除失败');
    }
}

//删除模板
async function selectionChange(data?: any) {
    const isDeletc = await showDeleteConfirm('确定删除本记录吗？删除后无法恢复！')
    if (isDeletc) {
        const params = {
            "ids": Array.isArray(data) ? data : [data],  //传入需要删除的id列表
            // action:'delete'
        }
        try {
            const res = await pptTmpHardDelete(params);
            message.success('删除成功');
            getpptTmpList()
        } catch (err: any) {
            message.error(err?.message || '删除失败');
        }
    }
}
//选中的模板
const selectedTemplate = ref([]);
function selectionCheck(data: any) {
    selectedTemplate.value = data
}

//还原按钮模板
function reback() { 
    selectionBack(selectedTemplate.value)
}
//删除按钮模板
function deleteTem() { 
    selectionChange(selectedTemplate.value)
}

onMounted(() => {
    getpptTmpList()
})
</script>
<template>
    <div class="mian">
        <back :url="'-1'" />
        <div class="title flex items-center leading-[12px] mt-[20px]">
            <div class="font-bold text-[24px] cursor-pointer">
                <span class="text-[#999999]" @click="()=> $router.go(-1)">PPT模板管理</span>
                /回收站</div>
        </div>
        <div class="flex items-center justify-between my-[20px]">
            <div class="flex items-center">
                <AButton type="primary" :disabled="selectedTemplate.length==0" ghost class="outline-a-button flex! w-[82px] items-center gap-2" @click="reback">
                     <backIcon/>
                    还原
                </AButton>
                <AButton type="primary" :disabled="selectedTemplate.length==0" ghost class="outline-a-button ml-2.5 flex! w-[82px] items-center gap-2" @click="deleteTem">
                    <IconTrash />
                    删除
                </AButton>
                

            </div>
            <div class="flex gap-[15px]">
                <AInput class="rounded-full!" placeholder="输入文件名称..." v-model:value="pptemTitle"
                @change="getpptTmpList">
                    <template #suffix>
                        <IconSearch class="text-foreground-4" @click="getpptTmpList" />
                    </template>
                </AInput>
            </div>
        </div>

        <a-spin :spinning="spinning">
            <pptTemTable 
                :pptTemTableList="pptTemTableList" 
                :total="total"
                @pagination-change="getpptTmpList" 
                @selection-check="selectionCheck"
                @selection-change="selectionChange"
                @selection-back="selectionBack"
            />
        </a-spin>
        


    </div>
</template>

<style scoped>
.mian {
    min-height: 100vh;
    background: #F0F9FF;
    display: flex;
    flex-direction: column;
    padding: 0 40px;
    background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
    background-repeat: no-repeat;
    background-size: 100% 50%;
    background-position: center bottom;
    overflow: auto;
}
</style>