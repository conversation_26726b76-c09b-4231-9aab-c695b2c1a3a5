<template>
  <div class="page px-[40px] 2xl:px-[260px]">

    <div class="flex justify-between mx-auto gap-[120px]">
      <div class="">
        <back :url="'-1'" class="mb-[120px]"></back>
        <h1 class="name leading-[40px] mt-[16px] mb-[30px]">PPT广场</h1>
        <div class="desc">
          三步轻松搞定专业PPT：输入核心知识点→选择教学风格模板（如学科主题、互动游戏、案例分析等）→自动生成结构清晰、视觉精美的课件，支持一键导出PPT/PDF格式。
        </div>
        <button class="primary-btn mt-[20px] 2xl:mt-[59px]" @click="creatPPT">
          <img style="width: 22px;" src="@/assets/image/pptSquare/svg.svg" />
          <span>生成PPT</span>
        </button>
      </div>
      <img style="width: 523px;height: 483px;" class="hidden lg:block" src="@/assets/image/pptSquare/big.png" />
    </div>
    <div class="page-cards grid grid-cols-1 gap-[52px] px-(--page-px)  lg:grid-cols-2 mt-[20px] 2xl:mt-[70px]">
        <div class="ppt-card gap-[48px] sm:flex items-center justify-center">
            <div>
                <img src="@/assets/image/pptSquare/png1.png" class=""/>
            </div>
            <div class="text-wrapper">
                <h2 class="title">PPT管理</h2>
                <p class="subtitle">PowerPoint Management</p>
                <button class="enter-btn" @click="interPPT">立即进入</button>
            </div>
        </div>
        <div class="ppt-card gap-[48px] sm:flex items-center justify-center">
            <div>
                <img src="@/assets/image/pptSquare/png2.png" class=""/>
            </div>
            <div class="text-wrapper">
                <h2 class="title">模板管理</h2>
                <p class="subtitle">Teaching Plan Management</p>
                <button class="enter-btn" @click="interModule">立即进入</button>
            </div>
        </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { env } from '@/../env'

const router = useRouter()

function creatPPT() {
  window.open(`${env.VITE_PPT_FRONTEND_URL}?type=pptSquare/pptManage`, '_blank');
};
function interPPT() {
  console.log('interPPT')
  router.push('/pptSquare/pptManage')
};
function interModule() {
  router.push('/pptSquare/pptTem')
};
function handleBack() {
  router.push('/aiSpace')
}

</script>

<style scoped lang="scss">
.page {
  min-height: 100vh;
  background: url(@/assets/image/pptSquare/square.png) no-repeat center;
  background-size: cover;
  width: 100%;
  overflow: auto;


  .name {
    font-size: 40px;
    font-weight: 700;
  }

  .desc {
    // width: 762px;?
    // height: 120px;
    font-size: 20px;
    font-weight: 500;
    line-height: 40px;
    // margin: 0 0 60px 0;
    color: #333;
  }

  .primary-btn {
    width: 154px;
    height: 52px;
    display: flex;
    align-items: center;
    gap: 14px;
    cursor: pointer;
    border: none;
    background: linear-gradient(135.84deg, rgba(140, 246, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
    color: white;
    padding: 20px;
    border-radius: 30px;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0px;
    line-height: 14px;
  }

  .primary-btn:hover {
    opacity: 0.8;
  }

  .page-cards {
    width: 100%;
    // display: flex;
    // justify-content: center;

    .ppt-card {
      background-color: #fff;
      border-radius: 10px;
      box-sizing: border-box;
      padding: 30px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
    }

    

    .text-wrapper {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;

      .title {
        font-size: 24px;
        font-weight: 700;
        background-image: linear-gradient(90deg, rgb(0, 102, 255) 0%, rgba(27, 148, 255) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 10px;
      }

      .subtitle {
        // min-width: 271px;
        font-size: 20px;
        font-weight: 400;
        background-image: linear-gradient(90deg, rgba(194, 194, 194, 0.3) 0%, rgba(27, 148, 255, 0.3) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 50px;
      }

      .enter-btn {
        width: 90px;
        height: 32px;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;
        line-height: 14px;
        color: #1677ff;
        border: 1px solid #1677ff;
        border-radius: 100px;
        background: transparent;
        cursor: pointer;
        transition: all 0.3s;
      }

      .enter-btn:hover {
        background: #1677ff;
        color: white;
      }
    }
  }
}
</style>
