<template>
  <div class="page">
    <el-main class="page-content">
      <div @click="()=> $router.back()" class="back-btn">
        <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
        <span style="font-size: 14px;line-height: 14px;">返回</span>
      </div>
      <h1 class="text">PPT管理</h1>
      <div class="toolbar gap-y-2">
        <div class="toolbar-left">
          <div style="display: flex;" class="flex-wrap gap-2">
            <AButton type="primary" class="gradient-a-button w-[82px]" @click="handleAddPPT">
              新建
            </AButton>
            <a-button type="primary" ghost style="width: 82px;" @click="uploadChapterOpen = true">上传</a-button>
 
            <a-button type="primary" ghost style="width: 82px;" @click="pptRecycle">回收站</a-button>
            <a-button type="primary" class="but gap-2" @click="delMessage(selectedIds)"
              ghost :disabled="selectedIds.length == 0">
              <IconTrash></IconTrash>
              删除
            </a-button>
          </div>
        </div>
        <div class="toolbar-right">
          <a-dropdown placement="top" :trigger="['click']" v-model:open="chapterVisible">
            <div
              class="border-solid border-[1px] border-[#E5E5E5] rounded-[148px] w-[140px]! h-[30px] 
              flex items-center justify-center text-[#666666] font-[400] bg-[#fff]">
              课程章节
              <CaretDownOutlined />
            </div>
            <template #overlay>
              <div style="width: 185px;">
                <chapter-selector :tree-data="treeData" @selectedChildIds="selectedChildIds"
                  @selectedChange="selectedChange" />
              </div>
            </template>
          </a-dropdown>

          <AInput class="rounded-full!" placeholder="输入文件名称..." v-model:value="searchValue" @change="getPptList">
            <template #suffix>
              <IconSearch class="text-foreground-4" @click="getPptList" />
            </template>
          </AInput>


        </div>
      </div>
      <a-spin :spinning="spinning">

        <pptTable :pptTemTableList="files" :total="total"
          @pagination-change="getPptList"
          @change-title="changeTitle"
          @selection-check="selectionCheck"
          @selection-change="selectionChange"
          @edit-change="editChange">
        </pptTable>

      </a-spin>
    </el-main>


    <uploadChapter 
      v-if="treeData.length > 0"
      :open="uploadChapterOpen" 
      :uploadloading="uploadloading"
      :treeData="treeData" 
      :accept="'.ppt,.pptx'"
      @update:open="val => uploadChapterOpen = val" 
      @getpptFile="uploadPPT"
    />

  </div>

</template>

<script lang="ts" setup>
import uploadChapter from '@/components/ui/uploadChapter.vue'
import qs from 'qs'; // 需安装 qs 库：npm install qs
import IconSearch from '~icons/ant-design/search-outlined'
import IconTrash from '~icons/lucide/trash-2'
import pptTable from '@/components/ppt/pptTable.vue';
import { formatDate, extractKeysAndTitles, fileSizeCheck } from '@/utils/util'
import { DeleteOutlined, DownloadOutlined, CheckCircleOutlined, CloseCircleOutlined, CaretDownOutlined } from '@ant-design/icons-vue';
import { getCourse } from '@/utils/auth';
import { courseTables } from '@/services/api/course';
import axios from "axios";
import ChapterSelector from '@/components/ui/ChapterSelector.vue';
//引入接口
import { pptList, ppt_softDelete, ppt_hardDelete, pptUpload, pptEdit } from '@/services/api/ppt'
import { usePagination } from '@/hooks/usePagination'; //解构分页参数
import { ref, computed, markRaw, onMounted, reactive, watch, h, createVNode } from 'vue'
import { Search, WarningFilled, } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import type { UploadProps } from 'ant-design-vue';
import { env } from '@/../env'

const uploadChapterOpen = ref(false)

//点击删除图标
function selectionChange(data: any){
  delMessage(data)
}
// 已选中的id
const selectedIds = ref<number[]>([]);
function selectionCheck(data: any){
  selectedIds.value = data
}

//编辑标题
async function editChange(data: any){
  const params = {
    title: data.title, // 教案模板标题
  }
  const res = await pptEdit(data.id, params)
  message.success('标题修改成功！')
  getPptList()
}

const router = useRouter()
const chapterVisible = ref(false)
const uploadloading = ref(false)

const pptFile = ref()

const uploadPPT = (data:any) => {
  uploadloading.value = true
  const params = new FormData();
  params.append('file', data.file);
  params.append('title', data.file.name);
  params.append('is_ai_generated','false')
  data.selectedKeys.forEach(item=>{
    params.append('chapter',item)
  })
  pptUpload(params).then(res => {
    message.success('上传成功');
    uploadloading.value = false
    uploadChapterOpen.value = false
    getPptList()
  }).catch(err => {
    message.error('上传失败');
    uploadloading.value = false
  })
  return false;
};


const handleAddPPT = () => {
  window.open(`${env.VITE_PPT_FRONTEND_URL}?type=pptSquare/ `, '_blank');
}


// 模拟数据
const files = ref([])
const total = ref(0)
const spinning = ref(false)


const searchValue = ref()
const checkedCharptIds = ref()



// 获取PPT列表
async function getPptList(pagination?: any) {
  const params = {
    // page: currentPage.value,
    page: pagination ? pagination.current : 1,
    page_size: 10,//pageSize.value,
    is_deleted: 'false',
    title: searchValue.value,
    chapter:checkedCharptIds.value
  }
  const queryString = qs.stringify(params, {
      arrayFormat: 'repeat', // 生成 key=value&key=value
      skipNulls: true,      // 跳过空值
  });
  try {
    spinning.value = true
    const res = await pptList(queryString)
    files.value = res.data.results
    total.value = res.data.count
    spinning.value = false
  } catch (error) {
    console.error('获取PPT列表失败:', error)
  }
}



//删除PPT
const delMessage = async (params: any) => {
  const ids = Array.isArray(params) ? params : [params]
  const confirmed = await showDeleteConfirm('确定要删除这条记录吗？');
  if (confirmed) {
    softDelete(ids)
  }
  console.log(confirmed);
}
async function softDelete(id: number[]) {
  const params = {
    "ids": id,  //传单个及单个删除
    "action": "delete"  //restore:恢复，delete：软删除
  }
  try {
    const res = await ppt_softDelete(params)
    getPptList()
  } catch (error) {
    message.error('删除失败')
  }
}

function pptRecycle() {
  router.push('/pptSquare/pptRecycle')
};
function changeTitle(data: any){
  console.log(data)
  editPPT(data)
}
const editPPT = (item: any) => {
  console.log(item.file);
  if (item.file != null) {
    message.error('自上次的PPT不支持编辑!')
    return
  }
  const url = `${env.VITE_PPT_FRONTEND_URL}?type=pptSquare/pptManage&id=${item.id}`
  window.open(url, '_blank');
}



//课程章节treeData
type ChapterNode = {
  key: string | number;
  title: string;
  children?: ChapterNode[];
};
const treeData = ref<ChapterNode[]>([])
//选中的章节
function selectedChildIds(value: any) {
  console.log(value, '选中的章节')
  checkedCharptIds.value = value.checkedKeys
  getPptList()
}
//搜索章节
function selectedChange(value: any) {
  // console.log(value, 'value')
  // treeData.value = []
  // fuzzy.value = value
  // updateKonledge()
}

onMounted(async () => {
  getPptList() //  获取PPT列表
  const chapter = await courseTables(getCourse().id)

  treeData.value = extractKeysAndTitles(chapter.data)
  console.log(JSON.stringify(treeData.value),'treeData.value')
})

</script>

<style scoped lang="scss">
:deep(.el-input__suffix-inner) {
  cursor: pointer;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .page-content {
    width: 100%;
    height: 100vh;
    overflow: auto;
    background: url(@/assets/image/bg1.png) no-repeat center top;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 0 40px;

    .back-btn {
      cursor: pointer;
      width: 60px;
      margin-top: 50px;
      color: #333;
      display: flex;
      align-items: center;
    }
  }
}





.text {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 12px;
  margin: 20px 0 20px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  flex-wrap:wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  // max-width: 1720px;
  // min-width: 900px;?

  .toolbar-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 36px;

    .addbut {
      width: 82px;
      color: #fff;
      background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
    }

    .but {
      display: flex;
      justify-content: center;
      align-items: center;
    }

  }

  .toolbar-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
}




:deep(.el-select__wrapper) {
  border-radius: 95px;
}


:deep(.el-input__wrapper) {
  border-radius: 94px;
}


</style>
