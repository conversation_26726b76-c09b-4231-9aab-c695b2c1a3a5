<script setup lang="ts">
import IconTrash from '~icons/lucide/trash-2'
import { CloudUploadOutlined } from '@ant-design/icons-vue';
import IconSearch from '~icons/ant-design/search-outlined'
import pptTemTable from '@/components/ppt/pptTemTable.vue';
import { message } from 'ant-design-vue';
import { pptTmpList, pptTmpSave, pptTmpSoftDelete } from '@/services/api/ppt';
import { fileSizeCheck } from '@/utils/util';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
const pptTemTableList = ref([])
const spinning = ref(false)
//上传
function handleAddPPT() {
    console.log('上传');
}
import type { UploadProps } from 'ant-design-vue';
const fileList = ref<UploadProps['fileList']>([]);
const uploadloading = ref(false)
const beforeUpload: UploadProps['beforeUpload'] = file => {

    uploadloading.value = true
    fileList.value = [...(fileList.value || []), file];
    const params = new FormData();
    if (fileSizeCheck(file, 3 * 1024 * 1024)) {
        message.error('文件大小不能超过3M');
        uploadloading.value = false
        return
    }
    params.append('tmp_json_file', file);
    params.append('title', fileList.value[0].name);
    pptTmpSave(params).then(res => {
        message.success('上传成功');
        uploadloading.value = false
        getpptTmpList()
    }).catch(err => {
        message.error('上传失败');
        uploadloading.value = false
    })
    return false;
};
//获取模板列表
const total = ref(0)
const pptemTitle = ref('')
function getpptTmpList(pagination?: any) {
    console.log(pagination, 'pagination');
    spinning.value = true
    const params = {
        title:pptemTitle.value,
        page: pagination ? pagination.current : 1,
        page_size: 10,
        is_deleted: false
    }
    pptTmpList(params).then(res => {
        pptTemTableList.value = res.data.results
        total.value = res.data.count
        spinning.value = false
    })
}

//删除模板
async function selectionChange(data?: any) {
    const isDeletc = await showDeleteConfirm('确定删除本记录到回收站吗！')
    if (isDeletc) {
        const params = {
            "ids": Array.isArray(data) ? data : [data],  //传入需要删除的id列表
            action:'delete'
        }
        try {
            const res = await pptTmpSoftDelete(params);
            message.success('删除成功');
            getpptTmpList()
        } catch (err: any) {
            message.error(err?.message || '删除失败');
        }
    }
}
//选中的模板
const selectedTemplate = ref([]);
function selectionCheck(data: any) {
    selectedTemplate.value = data
}
//删除按钮模板
function deleteTem() { 
    selectionChange(selectedTemplate.value)
}

onMounted(() => {
    getpptTmpList()
})
</script>
<template>
    <div class="mian">
        <back :url="'-1'" />
        <div class="title flex items-center leading-[12px] mt-[20px]">
            <div class="font-bold text-[24px]">模板管理</div>
        </div>
        <div class="flex items-center justify-between my-[20px] flex-wrap gap-y-2">
            <div class="flex items-center">
                <a-upload :file-list="fileList" :before-upload="beforeUpload" accept=".ppt,.pptx"
                    :showUploadList="false">
                    <AButton :loading="uploadloading" type="primary" ghost class="outline-a-button w-[82px]"
                        @click="handleAddPPT">
                        上传
                    </AButton>
                </a-upload>
                <!-- <AButton :loading="uploadloading" type="primary" ghost class="outline-a-button w-[82px] ml-2.5"
                    @click="handleAddPPT">
                    下载
                </AButton> -->
                <AButton type="primary" ghost class="outline-a-button ml-2.5 flex! w-[82px] items-center justify-center"
                @click="()=> $router.push('/pptSquare/pptTemRecycle')">
                    回收站
                </AButton>
                <AButton type="primary" :disabled="selectedTemplate.length==0" ghost class="outline-a-button ml-2.5 flex! w-[82px] items-center gap-2" @click="deleteTem">
                    <IconTrash />
                    删除
                </AButton>
            </div>
            <div class="flex gap-[15px]">
                <AInput class="rounded-full!" placeholder="输入文件名称..." v-model:value="pptemTitle"
                @change="getpptTmpList">
                    <template #suffix>
                        <IconSearch class="text-foreground-4" @click="getpptTmpList" />
                    </template>
                </AInput>
            </div>
        </div>
        <a-spin :spinning="spinning">
            <pptTemTable 
                :pptTemTableList="pptTemTableList" 
                :total="total"
                @pagination-change="getpptTmpList" 
                @selection-check="selectionCheck"
                @selection-change="selectionChange"
            />
        </a-spin>
       


    </div>
</template>

<style scoped>
.mian {
    min-height: 100vh;
    background: #F0F9FF;
    display: flex;
    flex-direction: column;
    padding: 0 40px;
    background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
    background-repeat: no-repeat;
    background-size: 100% 50%;
    background-position: center bottom;
    overflow: auto;
}
</style>