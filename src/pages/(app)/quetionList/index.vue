<template>
    <div class="page-content hide-scrollbar">
        <div class="">
            <back :url="'-1'"></back>
            <div class=" flex justify-between items-center">
                <div class="flex items-center text-[24px] leading-[24px] font-bold  max-w-[40%]" v-if="route.query.type != 'space'">
                    <ATooltip 
                        :title="getCourse().title" 
                        placement="top"
                        :mouseEnterDelay="0.3"
                        >
                    <div class=" my-[20px] truncate">{{ getCourse().title }}</div>
                    </ATooltip>
                    
                    <div class="shrink-0 ">-我的题库</div>
                </div>
                <div class="flex items-center text-[24px] leading-[24px] font-bold  max-w-[40%]" v-else>
                    <div class="shrink-0 my-[20px]">个人空间-我的题库</div>
                </div>
                <div class="flex items-center gap-[14px]">
                    <AButton type="primary" class="gradient-a-button w-[82px]" @click="addhandleQuetion">
                        添加试题
                    </AButton>
                    <AButton type="primary" class="gradient-a-button w-[82px]" @click="addAIQuetion">
                        AI生题
                    </AButton>
                </div>
            </div>
            <div class="quetion">
                <div class="search mt-[20px] mx-[20px]">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-5 flex-wrap">
                            <a-select v-model:value="course" style="width: 237px;" size="small" :options="optionsCourse" allow-clear
                                placeholder="关联课程" clearIcon @change="changeCourse" />
                            <a-select v-model:value="params.author" style="width: 237px;" size="small" :options="optionsUser" allow-clear
                                placeholder="归属人" :disabled="route.query.type == 'space'" />
                                <!-- examUser -->
                            <ADatePicker show-time v-model:value="params.created_at" placeholder="创建时间" :locale="zh"
                                style="width: 237px;" size="small" format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss" />

                            <a-select :disabled="course == 0" v-model:value="konwledge" mode="multiple"
                                style="width: 237px" placeholder="知识点" size="small" :options="konwledgeList" clearIcon
                                @change="changeKonwledge" />

                            <a-input placeholder="输入关键字" v-model:value="params.stem" size="small" style="width: 237px;">
                                <template #suffix>
                                    <SearchOutlined style="color: #C4C4C4;" />
                                </template>
                            </a-input>
                        </div>

                    </div>
                    <div class="flex justify-between items-end flex-wrap">
                        <div class="flex align-center mt-[17px] ">
                            <div class="w-[42px] text-[#333333] shrink-0">题型：</div>
                            <div>
                                <a-radio-group v-model:value="params.type" @change="changeRadio">
                                    <a-radio :value="''">全选</a-radio>
                                    <a-radio :value="'单选题'">单选题</a-radio>
                                    <a-radio :value="'多选题'">多选题</a-radio>
                                    <a-radio :value="'填空题'">填空题</a-radio>
                                    <a-radio :value="'判断题'">判断题</a-radio>
                                    <a-radio :value="'问答题'">问答题</a-radio>
                                </a-radio-group>
                            </div>
                        </div>
                        <div class="text-[14px] text-[#666666]">
                            共有<span class="text-[#3F8CFF]">{{ questionLists.length }}</span>个筛选结果
                        </div>

                    </div>
                </div>
                <div class="flex justify-between items-center mx-[20px] mb-[20px]">
                    <div class="flex gap-[20px] items-center">
                        <a-checkbox v-model:checked="checkAll">
                            全选
                        </a-checkbox>
                        <AButton type="primary" ghost class="outline-a-button" @click="awayQuetion">
                            {{ isOpen ? '收起题目详情' : '展开题目详情' }}
                        </AButton>
                    </div>
                    <div class="flex gap-[16px]">
                        <AButton type="primary" :disabled="plainOptions.length==0" ghost class="outline-a-button" @click="updateKonledge">
                            <div class="flex items-center">
                                <img src="@/assets/icon/knowledgeicon.svg" alt="" class="w-[14px] h-[14px]"
                                :class="plainOptions.length==0 ? 'grayscale opacity-50' : ''">
                                <div class="ml-[8]">修改关联知识点</div>
                            </div>
                        </AButton>
                        <AButton type="primary" :disabled="plainOptions.length==0" ghost class="outline-a-button w-[80px]" @click="deleteQuestion">
                            <div class="flex items-center">
                                <IconTrash />
                                <div class="ml-[3]">删除</div>
                            </div>
                        </AButton>
                    </div>
                </div>

                <div v-if="questionLists.length > 0" class="questionContent custom-scrollbar pl-[20px] pr-[12px]" ref="itemQuetion">
                    <!-- 试题组件 -->
                    <div v-for="(item, index) in questionLists">
                        <Item :key="item.id" :isOpen="isOpen" :index="index + 1" :isChecked="item.checked"
                            :questionLists="item" @deleteQue="refetch" @editQue="editQuetion"
                            @updateKonwledge="updateKonwledge"
                            @chooseQue="chooseQuetion" :chooseType="'quetion'" />
                    </div>
                </div>
                <div v-else-if="questionLists.length == 0&&!isFetching" class="flex items-center justify-center h-[calc(100vh-330px)]">
                    <img src="@/assets/image/zanwu/nodata.png" style="width: 309px;height: 301px;margin: auto;" />
                </div>
                
                <!-- <div v-if="!hasNextPage && !isFetching" class="flex justify-center items-center text-[#999999] text-[14px]">
                    加载完成~
                </div> -->
                <div v-if="isFetching" class="flex justify-center items-center text-[#999999] text-[14px]">
                    <a-spin tip="Loading...">
                        
                    </a-spin>
                </div>

                <div v-if="isError" class="error flex justify-center items-center text-[#999999] text-[14px]">
                    加载失败: {{ error?.message }}
                </div>
            </div>
        </div>

        <!-- 修改知识点弹窗 -->
        <a-modal v-model:open="openChapter" title="修改—关联知识点" width="700px" @ok="updateKonledgeOk">
            <chapter-selector :tree-data="treeData" @selectedChildIds="selectedChildIds" @selectedChange="selectedChange" />
        </a-modal> 

        <!-- 新增试题弹窗 -->
        <addQution :open="openAddQution" @update:open="val => openAddQution = val" @update:success="refetch" />
        <!-- 编辑试题弹窗 -->
        <EditQuetion :editType="'handleEdit'" :open="openEditQution" :quetionDetails="quetionDetails"
            @update:open="editOpenStatus" @update:success="refetch" />
    </div>
</template>

<script lang="ts" setup>
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
const zh = locale
import qs from 'qs'; // 需安装 qs 库：npm install qs
import { getCourse} from '@/utils/auth'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()


import IconTrash from '~icons/lucide/trash-2'
const plainOptions = ref<any[]>([]);
const checkAll = ref(false);
watch(() => checkAll.value, (checked) => {
    if (checked) {
        plainOptions.value = questionLists.value.map((item: any) => item.id);
    } else {
        plainOptions.value = [];
    }
    questionLists.value.forEach((item, index) => {
        questionLists.value[index] = { ...item, checked };
    })
})
const questionIds = ref<any[]>([]);

import { useInfiniteQuery } from '@tanstack/vue-query';
import { useInfiniteScroll } from '@vueuse/core';

import addQution from '@/components/question/addQution.vue';
import EditQuetion from '@/components/question/EditQuetion.vue';
import Item from '@/components/question/Item.vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import Back from '@/components/ui/back.vue'
import { reactive, ref, h, onMounted, watch } from 'vue';
import { questionList, deleteQuestionsBatch, questionUserList, questionKnowledgePoints, knowledgeListQue } from '@/services/api/question';
import { courseOwner, knowledgeList } from '@/services/api/course';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import { message } from 'ant-design-vue';


//知识点
import ChapterSelector from '@/components/ui/ChapterSelector.vue'
const openChapter = ref(false)

const treeData = ref<any[]>([])

// 获取知识点列表 打开弹窗时调用
const fuzzy = ref('')
function updateKonledge() {
    const param = {
        course_id: params.course_id || getCourse().id,
        fuzzy: fuzzy.value
    }
    treeData.value = []
    knowledgeListQue(param).then((data: any) => {
        data.data.forEach((item: any) => {
            item.chapters.forEach((i: any) => {
                if (i.knowledge_points && i.knowledge_points.length > 0) {
                    treeData.value.push({
                        key: i.id,
                        title: i.title,
                        children: i.knowledge_points.map((j: any) => {
                            return {
                                key: j.id,
                                title: j.name,
                            };
                        }),
                    });
                }
            })
        })
    })
    console.log(treeData.value, 'treeData')
    openChapter.value = true//!openChapter.value
}
const knowledge_point_ids = ref()
async function updateKonledgeOk() {
    const params = {
        "question_ids": plainOptions.value,
        "knowledge_point_ids": knowledge_point_ids.value
    }
    try {
        const res = await questionKnowledgePoints(params)
        openChapter.value = false
        message.success('修改成功')
    }catch (error) {
        console.log(error)
    }
}
//选中的知识点
function selectedChildIds(value: any) {
    knowledge_point_ids.value = value.selectedChildIds
}
//搜索章节
function selectedChange(value: any) {
    console.log(value, 'value')
    treeData.value = []
    fuzzy.value = value
    updateKonledge()
}

interface Option {
    value: number;
    label: string;
}
//关联课程
const course = route.query.type != 'space'? ref(getCourse().id) : ref(0)
const optionsCourse = ref([{
    value: 0,
    label: '未关联课程'
}])

//归属人
const examUser = ref()
const optionsUser = ref<Option[]>([]);
function getquestionUserList() {
    const params = {
        semester_id:getSemester().value,
        course_id:getCourse().id
    }
    questionUserList(params).then((data:any) => {
        data.data.forEach((item: any) => {
            optionsUser.value.push({
                value: item.id,
                label: item.username
            })
        })
    })
}
//知识点
const handleChange = (value: string[]) => {
    console.log(`selected ${value}`);
};
const konwledge = ref<string[]>([]);
const konwledgeList = ref<Option[]>([])
//控制选项是否展开isOpen
const isOpen = ref(false)
function awayQuetion() {
    isOpen.value = !isOpen.value
}


//删除题目单一
async function deleteQuestion() {
    const confirmed = await showDeleteConfirm('确定删除多条记录吗？删除后无法恢复！');
    if (confirmed) {
        const params = {
            "question_ids": plainOptions.value  //传入需要删除的id列表
        }
        try {
            const res = await deleteQuestionsBatch(params);
            message.success('删除成功');
            // state.indeterminate = false;

            refetch(); 
        } catch (err: any) {
            message.error(err?.message || '删除失败');
        }
    }
}

//选择试题
function chooseQuetion(data: any) {
    if (plainOptions.value.includes(data.id)) {
        plainOptions.value = plainOptions.value.filter(item => item !== data.id)
        questionLists.value = questionLists.value.map((item, index) => {
            if (item.id == data.id) {
                return { ...item, checked: false };
            }
            return item;
        });
    } else {
        plainOptions.value.push(data.id)
        questionLists.value = questionLists.value.map((item, index) => {
            if (item.id == data.id) {
                return { ...item, checked: true };
            }
            return item;
        });
    }
}


const quetionDetails = ref() //编辑试题的数据
//更新知识点关联
function updateKonwledge(data: any) {
    plainOptions.value = [data.id]
    // openChapter.value = true
    updateKonledge()
}

//子组件传过来编辑的数据
function editQuetion(data: any) {
    openEditQution.value = true
    quetionDetails.value = data
}

const openAddQution = ref(false)
const openEditQution = ref(false)
//手动添加试题
function addhandleQuetion() {
    openAddQution.value = true
}

//编辑痰喘
function editOpenStatus(val:boolean){
    openEditQution.value = val
    if(!val){
        quetionDetails.value = {}
    }
}

function addAIQuetion() {
    router.push({
        path: '/question/ailist',
        query: {
            type: 1//从题库进入
        }
    })
}

const params = reactive({
    stem: '',
    type: '',
    created_at: '',
    author: null,
    course_id: course.value as number | string,
    knowledge_point_ids: konwledge.value,
})
interface QuestionItem {
    id: number;
    type: string;
    stem: string;
    updated_at: string;
    author: string;
    difficulty: number;
    answer: string | string[];
    options: Array<{
        key: string;
        value: string;
    }>;
    explanation: string;
    checked: boolean;
    score: number;//分数
}
const questionLists = ref<QuestionItem[]>([]) //题目列表

// API 请求函数
const fetchItems = async ({ pageParam = 1 }) => {
    try {
        const param = {
            page: pageParam,
            page_size: 10,
            ...params,
        };
        param.course_id = params.course_id == 0 ? '' : params.course_id;
        param.knowledge_point_ids = params.course_id == 0 ? [] : params.knowledge_point_ids;
        const queryString = qs.stringify(param, {
            arrayFormat: 'repeat', // 生成 key=value&key=value
            skipNulls: true,      // 跳过空值
        });
        const response = await questionList(queryString);
        const responseData = response.data.results.map((item: any) => {
            return {
                ...item,
                checked: false
            }
        })
        checkAll.value = false;
        return {
            data: responseData, // 实际列表数据
            nextPage: response.data.count > 10*pageParam ? pageParam + 1 : undefined// 如果一页10条，返回了10条，说明可能有下一页
        };
    } catch (error: any) {
        throw new Error(error.response?.results?.message || '请求失败');
    }
};

// 使用 TanStack Query 的无限查询
const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isError,
    error,
    refetch
} = useInfiniteQuery({
    queryKey: ['questions', params], // params变化会触发重新查询
    queryFn: fetchItems,
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 1,
});

// 扁平化所有页面的数据
watch(data, (newData) => {
    if (newData) {
        questionLists.value = newData.pages.flatMap(page => page.data);
    }
}, { immediate: true });

// 获取滚动容器引用
const itemQuetion = ref(null);

// 使用 VueUse 的无限滚动
useInfiniteScroll(
    itemQuetion,
    async () => {
        if (!isFetching.value) {
            await fetchNextPage();
        }
    },
    {
        distance: 20,
    }
);
function changeRadio(e: any) {
    console.log('changeRadio', e)
}

function getcourseOwner() {
    // const data = [{ id: 1, title: '建筑学' }]
    courseOwner().then((data: any) => {
        data.data.forEach((item: any) => {
            optionsCourse.value.push({
                value: item.id,
                label: item.title
            })
        })
    })
}
function changeCourse(e: any) {
    if (e == 0 || e == undefined) {
        konwledge.value = []
        params.course_id = '';
    } else {
        params.course_id = e
        getknowledgeList(e)
    }
}
function getknowledgeList(e: any) {
    konwledgeList.value = []
    const params = {
        course_id: e==0?'':e
    }
    // console.log(params, 'params')
    knowledgeList(params).then((data: any) => {
        data.data.forEach((item: any) => {
            konwledgeList.value.push({
                value: item.id,
                label: item.name
            })
        })
    })
}
function changeKonwledge(e: any) {
    params.knowledge_point_ids = e
}

getcourseOwner()//课程列表
if(route.query.type == 'space'){
    
}else{  
    getquestionUserList()//归属人列表
}
</script>

<style scoped lang="scss">
:deep(.ant-radio-wrapper) {
    color: rgba(0, 0, 0, 0.65) !important;
    margin-right: 27px !important;
}

:deep(.ant-picker) {
    border-radius: 4px !important;
}


.page-content {
    background: url(@/assets/image/bg1.png) no-repeat center top;
    background-size: cover;
    box-sizing: border-box;
    padding: 0 40px;
    overflow: auto;
    height: 100vh;
}

.quetion {
    // min-width: 880px;
    // margin: 20px 0 0;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
    height: calc(100vh - 128px);
    // height: 500px;
    // padding: 20px 20px 20px 20px;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
}

.search {
    // height: 97px;
    opacity: 1;
    border-radius: 5px;
    background: rgba(63, 140, 255, 0.03);
    padding: 20px 20px 20px 20px;
    margin-bottom: 12px;
}
.questionContent{
    // border: 1px solid rgba(0, 0, 0, 1);
    overflow-y: auto;
    // height: 200px;
    // height: calc(100vh - 128px - 147px - 12px - 20px - 20px);
    // height:  calc(100vh - 128px);
    // padding-bottom: 20px;
}

</style>
