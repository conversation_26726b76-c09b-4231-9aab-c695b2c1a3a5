<template>
  <div class="ai-container">
    <div class="response">
      <h3>AI 回答：</h3>
      <p>{{ answer }}</p>
    </div>
    <textarea
      v-model="prompt"
      placeholder="请输入你的问题"
      class="input"
    />
    <button @click="askQuestion" :disabled="loading" class="btn">
      {{ loading ? "回答中..." : "发送问题" }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { createOpenAI } from '@ai-sdk/openai'
import { generateText, streamText } from 'ai'

const openai = createOpenAI({
  baseURL: 'https://api.302.ai/v1',
  apiKey: 'sk-YWjZXIKe39GK6H7wo7Lt3ZjRuIKYeDZcZdFEYDJ25plsk7vA',
})

const model = openai('gpt-4o-mini')

const prompt = ref('')
const answer = ref('')
const loading = ref(false)

const askQuestion = async () => {
  if (!prompt.value.trim()) return

  answer.value = ''
  loading.value = true

  const { textStream } = streamText({
    model,
    prompt: prompt.value,
  })

  for await (const text of textStream) {
    answer.value += text;
  }

  loading.value = false
}
</script>

<style scoped>
.ai-container {
  width: 85vw;
  height: 100vh;
  margin: 1rem auto;
}
.input {
  width: 100%;
  font-size: 20px;
}
.btn {
  margin-top: 0.5rem;
  padding: 8px 16px;
}
.response {
  height: 80vh;
  margin-top: 1rem;
  color: aliceblue;
  background: #060606;
  padding: 10px;
  border-radius: 6px;
}
</style>