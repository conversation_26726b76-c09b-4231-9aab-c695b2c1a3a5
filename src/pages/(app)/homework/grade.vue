<template>
    <div class="page">
        <div class="page-content">
            <back :url="'-1'"></back>
            <h1 class="text">
                 <span>{{ `${getCourse().title} -作业- ${route.query.examname}` }}</span>
            </h1>

            <div class="toolbar gap-y-4">
                <div class="toolbar-left flex-wrap">
                    <div>
                        <a-select v-model:value="valueSelect" style="width: 142px" :allowClear="true"  placeholder="选择班级"
                        :options="options" @change="getHandleExam" />
                    </div>
                    <div>
                        <a-select v-model:value="valueStatus" style="width: 142px" :allowClear="true"  placeholder="提交状态"
                        :options="optionStutas" @change="getHandleExam" />
                    </div>

                    <div style="display: flex;">
                        <AInput
                            class="rounded-full! w-[302px]!"
                            placeholder="请输入..."
                            v-model:value="searchValue"
                            @change="getHandleExam"
                            allow-clear
                        >
                            <template #suffix>
                                <IconSearch class="text-foreground-4"  @click="getHandleExam"/>
                            </template>
                        </AInput>
                    </div>
                    <div style="font-size: 14px;color: #666666;">
                        共有<span style="color: rgba(63, 140, 255, 1);"> {{ handleExamList.length }}</span>条结果
                    </div>
                </div>
                <div class="toolbar-right">
                    <div class="">
                        已提交/全部：
                        <span>{{ submittedCount }}/{{ handleExamList.length }}</span>
                    </div>
                    <a-button type="primary" class="gradient-a-button w-[82px]">发布</a-button>
                </div>
            </div>

            <a-spin :spinning="spinning">
                <gradeTable :handleExamList="handleExamList" @go-to-grading="goToGrading" @go-to-publish="goToPublish"></gradeTable>
            </a-spin>

        </div>
    </div>
</template>

<script lang="ts" setup>
import IconSearch from '~icons/ant-design/search-outlined'
import {getCourse} from '@/utils/auth'
import gradeTable from '@/components/homework/gradeTable.vue';
import { courseClassList } from '@/services/api/courseClass'
import { CloseCircleOutlined } from '@ant-design/icons-vue';
import { formatDate } from '@/utils/util'
import Back from '@/components/ui/back.vue'
import { DeleteOutlined, DownloadOutlined, UndoOutlined } from '@ant-design/icons-vue';
import { ref, markRaw, onMounted, computed, reactive, h } from 'vue'
import { Search, WarningFilled } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
const route = useRoute()
import { examOverview } from '@/services/api/exam'
import { usePagination } from '@/hooks/usePagination'; //解构分页参数

import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import { message } from 'ant-design-vue';

const router = useRouter()

const searchValue = ref()

const valueSelect = ref()//班级选择
interface OptionType {
  label: string;
  value: string;
}
const options = reactive<OptionType[]>([{ value: '全部', label: '全部' }])

const valueStatus = ref()
const optionStutas = reactive([
    { value: '全部', label: '全部' },
    { value: '未提交', label: '未提交' },
    { value: '阅卷中', label: '阅卷中' },
    { value: '已阅卷', label: '已阅卷' },
    { value: '已发布', label: '已发布' },
])
const submittedCount = computed(() => {
  return handleExamList.value.filter(item => item.take_part_status != '未提交').length;
});

//定义列表数据结构
interface fileList {
    id: number;
    student_name: string;
    student_id: string;
    end_time: string;
    take_part_status: string;
    total_score: number;
}
// 模拟数据
const handleExamList = ref<fileList[]>([
    {
        id: 1,
        student_name: '文件1',
        student_id: 'card1',
        end_time: '2023-05-01 10:00:00',
        take_part_status: '已发布',
        total_score: 90,
    }
])
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getHandleExam);
const spinning = ref(false)

onMounted(() => {
    // 先注释的
    const params = {
        course_semester: getCourse().course_semester_id,
        page: '',
        page_size: '99999',
    }
    courseClassList(params).then((res: any) => {
        console.log(res,'====')
        if (res.code == 200) {
        options.push(...res.data.results.map((item: any) => {
            return {
            label: item.name,
            value: item.id
            }
        }))
        }
    })
})

//当前页所有id
const currentPageIds = computed(() => handleExamList.value?.map(item => item.id) || []);
// 已选中的id
const selectedIds = ref<number[]>([]);
// 处理单个复选框的选择变化
const handleCheckboxChange = (id: number, checked: boolean) => {
    if (checked) {
        selectedIds.value.push(id);
    } else {
        selectedIds.value = selectedIds.value.filter((item) => item !== id);
    }
};
// 全选操作
const onCheckAllChange = (e: any) => {
    const isChecked = e.target.checked;
    if (isChecked) {
        selectedIds.value = [...new Set([...selectedIds.value, ...currentPageIds.value])]; // 合并去重
    } else {
        selectedIds.value = selectedIds.value.filter(id => !currentPageIds.value.includes(id));
    }
    state.indeterminate = false;
};
const state = reactive({
    indeterminate: false,
    checkAll: false,
    checkedList: [],
});

async function getHandleExam() {
    const params = {
        page: currentPage.value,
        pageSize: pageSize.value,
        exam_id: route.query.examid,
        status: valueStatus.value == '全部'?'':valueStatus.value,
        student_name: searchValue.value,
        class_id: valueSelect.value == '全部'?'':valueSelect.value,
    }
    spinning.value = true
    
    examOverview(params).then((res: any) => {
        console.log(res);
        handleExamList.value = res.data.results
        spinning.value = false
    });
}
// 先注释的
// import { emitter } from '@/utils/mitter'
onMounted(() => {
    getHandleExam()

    // emitter.on('refreshHomeworkList', () => {
    //     getHandleExam()
    // })
})

// onUnmounted(() => {
//   emitter.off('refreshHomeworkList')
// })


function goToGrading(item:any) {
    if(item.take_part_status === '未提交'){
        return message.error('学生未提交试卷，无法阅卷')
    }
    window.open(`/exam/paperGrading?id=`+item.id, '_blank');
}
function goToPublish(item:any) {
    console.log(item);
}

</script>

<style scoped lang="scss">
:deep( .ant-select-selector){
    border-radius: 148px;
}
:deep(.el-input__suffix-inner) {
    cursor: pointer;
}

:deep(.el-select__wrapper) {
    border-radius: 95px;
}


:deep(.el-input__wrapper) {
    border-radius: 94px;
}

.addbut {
    width: 82px;
    height: 32px;
    color: #fff;
    background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
}

.page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.page-header {
    display: flex;
    align-items: center;
    height: 6vh;
    background-color: #ffffff;
    box-sizing: border-box;
}


.page-content {
    background: url(@/assets/image/bg1.png) no-repeat center top;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 0 40px;
    flex: 1;
    overflow: auto;
    /* 关键属性 - 填充剩余空间 */
}

.text {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 12px;
    margin: 20px 0;
}

.toolbar {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    // max-width: 1720px;
    // min-width: 900px;

    .toolbar-left {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 10px;

        // .addbut {
        //     width: 82px;
        //     color: #fff;
        //     background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
        // }
    }

    .toolbar-right {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 17px;
        // color: ;
        font-size: 14px;

        span {
            font-weight: 600;
        }
    }
}

.table {
    background: #fff;
    border-radius: 4.79px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
    padding: 0 20px 20px;
    // min-width: 900px;
    // max-width: 1720px;
    min-width: 900px;
    width: 100%;

    .table-head {
        // display: flex;
        align-items: center;
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
        /* 禁止换行 */
        overflow-x: auto;
        /* 允许水平滚动（如果需要） */
        font-size: 14px;
        padding-left: 12px;

        .item {
            flex: 1 0 auto;
            white-space: nowrap;
            text-align: center;
            /* 防止文字换行 */
        }

        .item1 {
            flex: 1 0 auto;
            white-space: nowrap;
            text-align: left;
            padding-left: 60px;
            /* 防止文字换行 */
        }

        .item2 {
            flex: 1 0 auto;
            white-space: nowrap;
            text-align: left;
            padding-left: 68px;
            /* 防止文字换行 */
        }

        .flex {
            display: flex;
            align-items: center;
        }
    }

    .titlew {
        // width: 630px;
        position: relative;
        height: 56px;
        line-height: 56px;
    }

    .stutas {
        width: 60px;
        height: 22px;
        line-height: 22px;
        font-size: 12px;
        font-weight: 700;
        opacity: 1;
        border-radius: 2px;
    }
    .stutas1{
        border: 1px solid #FFA39E;
        background: #FFF1F0;
        color: #FF4D4F;
    }
    .stutas2{
        background: #E6F7FF;
        border: 1px solid #91D5FF;
        color: #1890FF;
    }
    .stutas3{
        background: #F6FFED;
        border: 1px solid #B7EB8F;
        color: #52C41A;
    }
    .stutas4{
        background: #FFFBE6;
        border: 1px solid #FFE58F;
        color: #FAAD14;
    }

    .file-name-text {
        // min-width: 620px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden
    }

    .file-name .edit-icon {
        opacity: 0;
        transition: opacity 0.3s ease;
        position: absolute;
        // left: 580px;
        margin-left: 21px;
        top: 46%;
        transform: translateY(-50%);
    }

    .file-name:hover .edit-icon {
        opacity: 1;
    }

    .height {
        height: 56px;
        border-bottom: 1px solid #E5E5E5;
    }

    .height1 {
        height: 58px;
        border-bottom: 1px solid #E5E5E5;
        color: #666666;

        &:hover {
            background: rgba(63, 140, 255, 0.05);
        }
    }
}

</style>
