<template>
  <div class="page">

      <div @click="handleBack" class="back-btn">
        <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
        <span style="font-size: 14px;line-height: 14px;">返回</span>
      </div>
      <h1 class="text">{{`计算机网络-作业${homework_id}-作业学情分析`}}</h1>

      <a-select
          ref="select"
          v-model:value="valueCalss"
          style="width: 100px; margin-bottom: 20px;"
          :options="classOption"
          placeholder="班级选择"
          allowClear
          @change="handleChange"
      ></a-select>

      <!-- <div class="toolbar">
        <div class="toolbar-left">
          <el-select v-model="valueSelect" placeholder="班级" style="width: 100px;min-height: 32px;"
            suffixIcon="CaretBottom">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div> -->

      <div class="summary">
        <div style="flex:1">
            <div class="summary_header">
              <div class="summary_title"></div>
              <div style="display: flex; justify-content: space-between; gap:15px">
                <a-button type="primary" class="btn" @click="downPDF">导出pdf</a-button>
                <a-button type="primary" class="btn" @click="downWord">导出word</a-button>
              </div>
            </div>
            <div class="summary_content">
                {{ summary }}
            </div>
        </div>
        <div style="position: relative; width: 366px;">
          <img src="@/assets/image/course/ai.png" style="position: absolute;z-index:1;height: 366px; width: 366px;top:-130px"/>
        </div>
      </div>

      <a-spin :spinning="spinning">
        <div class="table">

          <div style="display: flex; justify-content: space-between; padding: 20px 0;">
            <div style="font-size: 16px;font-weight: 700;">作业每个学生的分析情况</div>
            <a-input v-model:value="searchValue" style="border-radius: 94px;width: 302px;min-height: 32px;" placeholder="输入学生姓名查询" @pressEnter="getList">
              <template #suffix>
                <SearchOutlined style="color:#C4C4C4" @click="getList"/>
              </template>
            </a-input>
          </div>

          <div class="table-head height" style="font-weight:600">
            <!-- <div class="item" style="max-width: 56px;">
              <a-checkbox v-model:checked="state.checkAll" :indeterminate="state.indeterminate"
                @change="onCheckAllChange" />
            </div> -->
            <div class="item" style="width: 100px;">学生姓名</div>
            <div class="item" style="width: 100px;">得分</div>
            <div class="item" style="width: 500px;">学情分析</div>

          </div>

          <div class="center" v-if="files?.length === 0">
            <img src="@/assets/image/zanwu/nodata.png" style="width: 385px;height: 379px;" />
          </div>
          <div v-else>
            <div class="table-head height1" v-for="item in files" :key="item.id">
              <!-- <div class="item" style="max-width: 58px;">
                <a-checkbox :checked="selectedIds.includes(item.id)"
                  @change="(e: any) => handleCheckboxChange(item.id, e.target.checked)" />
              </div> -->
              <div class="item file-name titlew">
                <div style="width: 100px" class="file-name-text">
                  {{ item.student_name }}
                </div>
                <span class="edit-icon" style="">
                  <a-tooltip placement="bottom">
                    <template #title>
                      <span>编辑</span>
                    </template>
                    <img style="cursor: pointer;width: 14px;height: 14px;" src="@/assets/image/img/edittable.png" />
                  </a-tooltip>
                </span>
              </div>
              <div class="item" style="width: 100px;">
                {{ item.analysis_score || '无' }}
              </div>
              <div class="item" style="width: 500px;">
                {{ item.analysis_exam || '无'}}
              </div>

            </div>
          </div>

          <div class="pagination">
            <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
              layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </div>
      </a-spin>

  </div>
</template>

<script lang="ts" setup>
import { formatDate } from '@/utils/util'
import { SearchOutlined } from '@ant-design/icons-vue';
//引入接口
import { examAnalysis,summaryAnalysis,examClassList} from '@/services/api/analysis'
import { usePagination } from '@/hooks/usePagination'; //解构分页参数
import { ref, computed, markRaw, onMounted, reactive, watch, h , createVNode} from 'vue'
import { message, Modal} from 'ant-design-vue';
import type { SelectProps } from 'ant-design-vue';
import { useRouter,useRoute } from 'vue-router'
import { downloadWord, downloadPDF } from '@/utils/downloadFile'

const route = useRoute() //获取路由参数
const homework_id = route.query.homework_id

const router = useRouter()


//定义列表数据结构
interface fileList {
  id: number;
  student_name: string;
  analysis_score: number;
  analysis_exam: string;
}
// 模拟数据
const files = ref<fileList[]>()
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getList);
const spinning = ref(false)

const valueCalss = ref();  //班级选择
const classOption = ref<{value: any; label: string}[]>([])
// 监听 classOption 的变化，设置默认值为第一个班级
watch(classOption, (newVal) => {
  if (newVal.length > 0) {
    valueCalss.value = newVal[0].value
    getList()
    getSummary()
  }
})

const handleChange = (value: any) => {
  console.log(`selected ${value}`);
  getList()
  getSummary()
};

// 获取作业学情分析班级列表
async function getClassList() {
  const params = {
    exam_id: homework_id
  }
  try {
    const res = await examClassList(params)
    classOption.value = []
    res.data.forEach((item: any) => {
        classOption.value.push({
            value: item.id,
            label: item.name
        })
    })
  } catch (error) {
    console.error('获取作业学情分析列表失败:', error)
  }
}

const searchValue = ref('')

onMounted(() => {
  getClassList()
  getList() //  获取列表
  getSummary() //获取summary
})

// 获取作业学情分析列表
async function getList() {
  const params = {
    page: currentPage.value,
    page_size: pageSize.value,
    class_id: valueCalss.value,
    student_name: searchValue.value,
    exam_id: homework_id
  }
  try {
    spinning.value = true
    const res = await examAnalysis(params)
    files.value = res.data.results
    total.value = res.data.count
    spinning.value = false
  } catch (error) {
    console.error('获取作业学情分析列表失败:', error)
  }
}


const summary = ref('')
// 获取作业整体学情分析
async function getSummary() {
  if(valueCalss.value){
    const params = {
      class_id: valueCalss.value,
      exam_id: homework_id
    }
    try {
      const res = await summaryAnalysis(params)
      summary.value = res.data.results
    } catch (error) {
      console.error('获取作业学情分析列表失败:', error)
    }
  }else{
     summary.value = '选择班级查看'
  }

}

const handleBack = () => {
  router.back()
}

async function downPDF() {
  if(summary.value){
    downloadPDF(summary.value, `计算机网络-作业${homework_id}-作业学情分析.pdf`);
  }else{
    console.log('暂无总结')
  }
}

async function downWord() {
  if(summary.value){
    downloadWord(summary.value, `计算机网络-作业${homework_id}-作业学情分析.doc`);
  }else{
    console.log('暂无总结')
  }
}


</script>

<style scoped lang="scss">
:deep(.ant-select-selector){
    border-radius: 20px;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0 40px;

  .back-btn {
    cursor: pointer;
    width: 60px;
    margin-top: 50px;
    color: #333;
    display: flex;
    align-items: center;
  }

}

.summary{
  min-height: 217px;
  margin-bottom: 20px;
  background: url(@/assets/image/course/bg.jpg) center / cover no-repeat;
  border-radius: 4.79px;
  padding: 20px;
  display: flex;
    .summary_header{
      display: flex; 
      align-items: center;
      justify-content: space-between; 
      margin-bottom: 20px;
      .summary_title{
        font-size: 16px;
        font-weight: 700;
        letter-spacing: 0px;
        line-height: 16px;
        color: rgba(51, 51, 51, 1);
        text-align: left;
        margin-bottom: 30px;
      }
    }

    .summary_content {
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0px;
        line-height: 20px;
        color: rgba(102, 102, 102, 1);
        text-align: left;
        max-height: 100px;
        overflow: auto;
    }
    .btn{
        background: rgba(63, 140, 255, 1); 
        width: 71px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.text {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 12px;
  margin: 20px 0 20px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .toolbar-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 36px;

    .addbut {
      width: 82px;
      color: #fff;
      background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
    }
  }

  .toolbar-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
}




:deep(.el-select__wrapper) {
  border-radius: 95px;
}


:deep(.el-input__wrapper) {
  border-radius: 94px;
}


.table {
  background: #fff;
  border-radius: 4.79px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 0 20px 20px;
  min-width: 900px;
  width: 100%;
  height: 606px;

  .table-head {
    // display: flex;
    align-items: center;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    /* 禁止换行 */
    overflow-x: auto;
    /* 允许水平滚动（如果需要） */
    font-size: 14px;
    padding-left: 12px;

    .item {
      flex: 1 0 auto;
      white-space: nowrap;
      /* 防止文字换行 */
    }

    .flex {
      display: flex;
      align-items: center;
    }

    .titlew {
      width: 100px;
      position: relative;
      height: 56px;
      line-height: 56px;
    }

    .file-name-text {
      max-width: 100px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden
    }

    .file-name .edit-icon {
      opacity: 0;
      transition: opacity 0.3s ease;
      position: absolute;
      left: 345px;
      top: 50%;
      transform: translateY(-50%);
    }

    .file-name:hover .edit-icon {
      opacity: 1;
    }
  }

  .height {
    height: 60px;
    border-bottom: 1px solid #E5E5E5;
  }

  .height1 {
    height: 58px;
    border-bottom: 1px solid #E5E5E5;
    color: #666666;

    &:hover {
      background: rgba(63, 140, 255, 0.05);
    }
  }
}

.pagination {
  margin: 20px 0 12px 0;
  text-align: center;
}

:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}
</style>
