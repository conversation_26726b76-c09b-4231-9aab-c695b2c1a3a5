<template>
  <div class="handquestion overflow-y-hidden!">
    <back :url="'-1'" />
    <div class="top">
      <div class="font-bold text-[24px] max-w-[40%] flex title">
        <ATooltip :title="getCourse().title" placement="top" :mouseEnterDelay="0.3">
          <div class="truncate cursor-pointer">{{ getCourse().title }}</div>
        </ATooltip>
        <div class="shrink-0">-作业-{{ homeworkDetails.name }}</div>
      </div>
    </div>



    <div class="content flex items-center justify-between">
      <div class="name">
        <div style="margin-bottom: 20px;">作业编号：{{ homeworkDetails.id }}</div>
        <div class="setting">
          <span>乱序设置：</span>
          <a-radio-group v-model:value="orderValue">
            <a-radio :value="1" style="color: rgba(0, 0, 0, 0.65);margin-right: 28px;">试题乱序</a-radio>
            <a-radio :value="2" style="color: rgba(0, 0, 0, 0.65);">选项乱序</a-radio>
          </a-radio-group>
        </div>
      </div>
      <div>
        <a-button type="text" :loading="saveLoading" class="fabu flex items-center justify-center" @click="publishExam('save')">保存作业</a-button>
        <a-button type="text" class="fabu flex items-center justify-center ml-[20px]" @click="publishExam">发布作业</a-button>
      </div>
    </div>

    <div class="contentItem">
      <div class="flex items-center justify-between mt-[20px]  border-b-[1px] border-[#E8E8E8]  pb-[15px]">
        <div class="num">题量{{ questionLists.length }}道，总分{{ tatolScore }}分</div>
        <div class="flex items-center justify-between">
          <a-button type="text" class="choosebut flex items-center justify-center" @click="question">题库选题</a-button>
          <a-button type="text" class="choosebut flex items-center justify-center" style="margin-left: 20px;"
            @click="aiquestion">AI生成</a-button>
        </div>
      </div>


      <div v-if="questionLists.length > 0" class="overflow-y-auto hide-scrollbar  h-[calc(100vh-318px)]">
        <Item :type="'view'" :questionLists="questionLists" @deleteHomework="deleteHomeworkQue"
          @handle-score="handleScoreChange"></Item>
      </div>
      <div class="flex justify-center items-center h-[calc(100vh-343px)]" v-else>
        <img src="@/assets/image/zanwu/questionNodata.png" alt="" style="width: 309px;height: 231px;" />
      </div>
    </div>

    <a-modal v-model:open="openPublish" title="发布作业" :footer="null" width="700px">
      <div style="margin: 4vh 0 0 0;">
        <a-form :model="homework" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" autocomplete="off">
          <a-form-item label="作业名称" v-bind="validateInfos.name">
            <a-input v-model:value="homework.name" placeholder="请输入作业名称" />
          </a-form-item>
          <a-form-item label="作业描述" v-bind="validateInfos.introduction">
            <a-input v-model:value="homework.introduction" placeholder="请输入作业描述" />
          </a-form-item>

          <a-form-item label="有效时间" v-bind="validateInfos.time">
            <a-range-picker v-model:value="homework.time" show-time style="width: 100%;" format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss" />
          </a-form-item>
          <a-form-item label="考试时长" v-bind="validateInfos.duration">
            <div style="display: flex;align-items: center;">
              <a-input-number id="inputNumber" v-model:value="homework.duration" :min="1" :max="99999"
                placeholder="考试时长" />
              <div style="margin-left: 4px;">分钟</div>
            </div>
          </a-form-item>
          <a-form-item label="答题班级" name="class_ids" v-bind="validateInfos.class_ids">
            <a-select v-model:value="homework.class_ids" mode="multiple" style="width: 100%" placeholder="请选择班级"
              :options="options" />
          </a-form-item>

          <div style="display: flex;justify-content: flex-end">
            <a-form-item>
              <a-space>
                <a-button @click=" openPublish = false">取消</a-button>
                <a-button type="primary" html-type="submit" @click="onSubmit">确定</a-button>
              </a-space>
            </a-form-item>
          </div>
        </a-form>
      </div>
    </a-modal>

    
  </div>
</template>

<script lang="ts" setup>
import { Form, message } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
type RangeValue = [Dayjs, Dayjs];
const useForm = Form.useForm
const openPublish = ref(false)
const homework = reactive({
  id: '',//作业id
  name: '',//作业名称
  class_ids: [],//班级
  time: [] as unknown as RangeValue,//有效时间
  duration: '',//答题时间
  introduction: '',//描述
  status: '',//状态
  pass_rate: '',//及格率
});
const homeworkRules = reactive({
  name: [{ required: true, message: '请输入作业名称', trigger: 'change' }],
  introduction: [{ required: true, message: '请输入作业描述', trigger: 'change' }],
  time: [{ required: true, message: '请选择有效时间', trigger: 'change' }],
  duration: [{ required: true, message: '请输入考试时长', trigger: 'change' }],
  class_ids: [{ required: true, message: '请选择答题班级', trigger: ['change', 'blur'] }],
})

interface OptionType {
  label: string;
  value: string;
}
const options = reactive<OptionType[]>([])
import { courseClassList } from '@/services/api/courseClass'
import { getSemester, getCourse } from '@/utils/auth'

onMounted(() => {
  const params = {
    course_semester: getCourse().course_semester_id,
    page: '',
    page_size: '9999',
  }
  courseClassList(params).then((res: any) => {
    if (res.code == 200) {
      options.push(...res.data.results.map((item: any) => {
        return {
          label: item.name,
          value: item.id
        }
      }))
    }
  }).catch((err: any) => {
  })
})

const { resetFields, validate, validateInfos } = useForm(homework, homeworkRules);//验证课程表单提交
const onSubmit = () => {
  validate().then(async () => {
    console.log('submit', homework);
    const params = {
      "exam_id": homeworkDetails.value.id,  //考试/作业id，必传
      "status": homework.status,  // 考试/作业状态，需保持为已发布，如不传默认更新为已发布
      "name": homework.name,
      // "course": 7,
      "class_ids": homework.class_ids,
      "duration": homework.duration,
      "start_time": homework.time[0],
      "end_time": homework.time[1],
      // "pass_rate": "",
      "introduction": homework.introduction,
    }
    const res = await examPublish(params)
    message.success('发布成功！');
    openPublish.value = false
    questionStore.setSelectedQuestionIds(new Set([]))
    questionStore.setSelectedQuestions([])
    router.push('/homework')
  }).catch(err => {
    console.log('error', err);
  });
};


import { joinExam, examPublish } from '@/services/api/exam'
import { useCourseStore } from '@/stores/course'
const courseStore = useCourseStore()
const { homeworkDetails } = toRefs(courseStore)

import Item from '@/components/question/Itemq.vue'
import { twMerge } from 'tailwind-merge'
import back from '@/components/ui/back.vue';
import { ref, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router'
const router = useRouter() //路由跳转
const orderValue = ref(1)
import { useQuestionStore } from '@/stores/question'

const questionStore = useQuestionStore()
const questionLists = ref([])
const tatolScore = ref(0)
onMounted(() => {
  questionLists.value = questionStore.selectedQuestions.map((item: any) => {
    return {
      score: 1,
      ...item
    }
  })
  tatolScore.value = questionLists.value.reduce((pre: number, cur: any) => pre + cur.score, 0)
  console.log(questionLists.value, '题目列表')
})


function question() {
  router.push('/question/list')
}
function aiquestion() {
  // router.push('/question/ailist?type=')？
  router.push({
    path: '/question/ailist',
    query: {
      type: 2//作业进入AI生成
    }
  })
}


// 删除试题
function deleteHomeworkQue(item: any) {
  questionLists.value = questionLists.value.filter((i: any) => i.id != item.id)
  questionStore.setSelectedQuestionIds(new Set(questionLists.value.map((item: any) => item.id)))
  questionStore.setSelectedQuestions(questionLists.value)
  handleScoreChange(item)
}

const saveLoading = ref(false)
function publishExam(type?:any) {
  if(type == 'save'){
    saveLoading.value = true
    homework.status = '未发布'
  }else{
    openPublish.value = true
    homework.status = '已发布'
  }
  const param = {
    name: homeworkDetails.value.name,
    exam_id: homeworkDetails.value.id,
    questions: questionLists.value.map((item: any) => {
      return {
        id: item.id,
        score: item.score,
      }
    })
  }
  joinExam(param).then((res: any) => {
    console.log(res, '发布作业返回')
    if (res.code == 200) {
      saveLoading.value = false
      if(type == 'save'){
        handleSave()
      }
      // questionStore.setSelectedQuestionIds(new Set([]))
      // questionStore.setSelectedQuestions([])
      // router.push('/homework')
    }

  })
  console.log(param, '发布作业参数')
}

//未发布作业保存
const handleSave = async () => { 
  const params = {
      "exam_id": homeworkDetails.value.id,  
      "status": homework.status,  
      "name": homeworkDetails.value.name,
      // "class_ids": homework.class_ids,
      // "duration": homework.duration,
      // "start_time": homework.time[0],
      // "end_time": homework.time[1],
      // "introduction": homework.introduction,
    }
    const res = await examPublish(params)
    message.success('保存成功！');
    openPublish.value = false
    questionStore.setSelectedQuestionIds(new Set([]))
    questionStore.setSelectedQuestions([])
    router.push('/homework')
}


const handleScoreChange = (updatedItem: any) => {
  tatolScore.value = questionLists.value.reduce((pre: number, cur: any) => pre + cur.score, 0)
  // 实时计算总分等逻辑
};

</script>
<style lang="scss" scoped>
.handquestion {
  height: 100vh;
  background: #F0F9FF;
  display: flex;
  flex-direction: column;
  padding: 0 40px;
  background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center bottom;
 overflow: hidden;
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title {
    font-size: 24px;
    font-weight: 700;
    // line-height: 12px;
    margin: 10px 0;
  }
}

.content {
  height: 99px;
  opacity: 1;
  border-radius: 5px;
  background: #FFFFFF;
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 20px;

  .name {
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 16px;
  }

  .fabu {
    width: 82px;
    height: 32px;
    opacity: 1;
    border-radius: 150px;
    background: linear-gradient(90deg, #3F8CFF 0%, #15C0E6 100%);
    font-size: 14px;
    color: #fff
  }

  .setting {
    /** 文本1 */
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(51, 51, 51, 1);

    span {
      width: 78.77px;
    }
  }
}

.contentItem {
  // height: 596px;
  opacity: 1;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  background: #FFFFFF;
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 0 20px 20px 20px;
  margin-top: 20px;

  .num {
    font-size: 14px;
    font-weight: 400;
    line-height: 14px;
  }

  .choosebut {
    background-color: #3F8CFF;
    border-radius: 3px;
    font-size: 14px;
    font-weight: 500;
    line-height: 14px;
    color: #fff;
    height: 24px;
    // width: 76px;
    text-align: center;
  }
}
</style>