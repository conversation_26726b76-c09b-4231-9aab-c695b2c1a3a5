<template>
  <div class="mian">
    <back :url="'-1'" />
    <div class="flex items-center justify-between flex-wrap gap-y-4 my-[20px]">
      <div class="flex items-center leading-[24px] max-w-[45%]">
        <div class="font-bold truncate" style="font-size: 24px;">{{getCourse().title}}-作业</div>
        <div
          class="flex ml-[10px] rounded-[94] bg-[rgba(63,140,255,0.15)] w-[130px] h-[25px] items-center cursor-pointer">
          <div class="w-[65px] text-center rounded-[94] h-[25px] leading-[25px]"
            @click="[publishStatus = '1', getExamLists()]"
            :class="publishStatus == '1' ? 'bg-gradient-to-r from-[#219FFF] to-[#0066FF] font-bold text-white' : 'text-[#3F8CFF]'">
            已发布
          </div>
          <div class="w-[65px] text-center rounded-[94] h-[25px] leading-[25px]"
            @click="[publishStatus = '2', getExamLists()]"
            :class="publishStatus == '2' ? 'bg-gradient-to-r from-[#219FFF] to-[#0066FF] font-bold text-white' : 'text-[#3F8CFF]'">
            未发布
          </div>
        </div>
      </div>
      <div class="flex">
        <AButton type="primary" class="gradient-a-button w-[82px] h-[32px]" @click="handleCreate()">
          新建
        </AButton>

        <AButton type="primary" ghost class="outline-a-button ml-2.5 flex! items-center gap-2"
          @click="handleDelete(chooseIdsSet)" :disabled="chooseIdsSet.size === 0">
          <IconTrash />
          删除
        </AButton>
      </div>
    </div>

    <div class="overflow-y-auto hide-scrollbar ">
      <div class="content" v-for="i in homeworkLists" :key="i.id">
        <div class="content-header">
          <a-checkbox v-model:checked="i.checked"
            style="font-weight: 700;font-size: 16px;line-height: 24px;color: #333;height: 24px;"
            @click="checkedChange(i)">
            {{ i.name }}
          </a-checkbox>
          <div class="action-group" v-show="publishStatus == '1'">
            <div class="action" style="margin-right: 20px;">
              <a-button @click="analysis(i.id)" type="text" class="action1">
                作业详情分析
              </a-button>
            </div>
            <div class="action">
              <a-button type="text" class="action1" @click="handleGrade(i)">阅卷</a-button>
            </div>
          </div>
        </div>
        <div class="content-body">
          <div class="flex items-center w-[20%] leading-[18px] font-[14px] flex-wrap">
            <div class="text-[#666666] font-[400] font-[alibabapuhuiti] shrink-0">班级：</div>
            <div class="font-[500]">{{ i.class_names.join(',') }}</div>
          </div>
          <div class="flex items-center w-[40%] leading-[18px] font-[14px] flex-wrap">
            <div class="text-[#666666] font-[400] font-[alibabapuhuiti]">有效时间：</div>
            <div class="font-[500]">{{ formatDate(i.start_time) }} - {{ formatDate(i.end_time) }}</div>
          </div>
          <div class="flex items-center leading-[18px] font-[14px] flex-wrap">
            <div class="text-[#666666]  font-[400] font-[alibabapuhuiti]">已提交数量：</div>
            <div class="font-[500]">{{ i.submitted_count }}</div>
          </div>
          <div class="h-[18px] flex items-center gap-[32px] cursor-pointer">
            <a-tooltip>
              <template #title>编辑</template>
              <img src="@/assets/icon/editIcon.png" class="w-[14px] h-[14px]" @click="editExamItem(i)"
                :class="publishStatus == '2' ? '' : 'opacity-0'" />
            </a-tooltip>
            <a-tooltip>
              <template #title>删除</template>
              <IconTrash class="text-[#3F8CFF] " @click="handleDelete([i.id])" />
            </a-tooltip>
          </div>
        </div>
      </div>

      <div v-if="homeworkLists.length == 0">
        <img src="@/assets/image/zanwu/nodata.png" style="width: 309px;height: 301px;margin: auto;" />
      </div>
      <div class="pagination">
        <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
          layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>




    <a-modal v-model:open="open" title="作业修改" :footer="null" width="700px">
      <div style="margin: 4vh 0 0 0;">
        <a-form :model="homework" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" autocomplete="off">
          <a-form-item label="作业名称" v-bind="validateInfos.name">
            <a-input v-model:value="homework.name" placeholder="请输入作业名称" />
          </a-form-item>
          <a-form-item label="答题班级" name="class_ids" v-bind="validateInfos.class_ids">
            <a-select v-model:value="homework.class_ids" mode="multiple" style="width: 100%" placeholder="请选择班级"
              :options="options" />
          </a-form-item>
          <a-form-item label="有效时间" v-bind="validateInfos.time">
            <a-range-picker v-model:value="homework.time" show-time style="width: 100%;" format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss" />
          </a-form-item>
          <a-form-item label="考试时长" v-bind="validateInfos.duration">
            <div style="display: flex;align-items: center;">
              <a-input-number id="inputNumber" v-model:value="homework.duration" :min="1" :max="99999"
                placeholder="考试时长" />
              <div style="margin-left: 4px;">分钟</div>
            </div>
          </a-form-item>
          <a-form-item label="乱序设置" v-bind="validateInfos.disorder">
            <a-radio-group v-model:value="homework.disorder" name="radioGroup">
              <a-radio value="1">试题乱序</a-radio>
              <a-radio value="2">选项乱序</a-radio>
            </a-radio-group>
          </a-form-item>
          <!-- <a-form-item label="关联章节" v-bind="validateInfos.chapter">
            <a-tree-select v-model:value="homework.chapter" show-search style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请关联章节" allow-clear
              tree-default-expand-all :tree-data="treeData" :field-names="{
                children: 'children',
                value: 'value',
                label: 'title',
              }" tree-node-filter-prop="title">
            </a-tree-select>
          </a-form-item> -->
          <a-form-item label="作业描述" v-bind="validateInfos.introduction">
            <a-textarea v-model:value="homework.introduction" placeholder="请输入作业描述" :rows="4" />
          </a-form-item>
          <div style="display: flex;justify-content: flex-end">
            <a-form-item>
              <a-space>
                <a-button @click=" open = false">取消</a-button>
                <a-button type="primary" html-type="submit" @click="onSubmit" style=""
                  :loading="loadingSubmit">确定</a-button>
              </a-space>
            </a-form-item>
          </div>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import IconTrash from '~icons/lucide/trash-2'
import IconLoading from '~icons/lucide/loader-circle'

import { getCourse } from '@/utils/auth'

import { usePagination } from '@/hooks/usePagination'; //解构分页参数
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 6, total.value, getExamLists);
import { getExamList, deleteExam, editExam } from '@/services/api/exam'
import { DeleteOutlined } from '@ant-design/icons-vue';
import type { TreeSelectProps } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
type RangeValue = [Dayjs, Dayjs];
import { Form } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
const useForm = Form.useForm
import back from '@/components/ui/back.vue';
import { formatDate } from '@/utils/util';
import { ref, reactive } from 'vue';
import { useRouter,useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const checked = ref(false);

//筛选发布状态
const publishStatus = ref('1');

const open = ref(false); //编辑弹窗

interface OptionType {
  label: string;
  value: string;
}
const options = reactive<OptionType[]>([])
import { courseClassList } from '@/services/api/courseClass'

onMounted(() => {
  const params = {
    course_semester: getCourse().course_semester_id,
    page: '',
    page_size: '9999',
  }
  courseClassList(params).then((res: any) => {
    if (res.code == 200) {
      options.push(...res.data.results.map((item: any) => {
        return {
          label: item.name,
          value: item.id
        }
      }))
    }
  }).catch((err: any) => {
  })
})


const treeData = ref<TreeSelectProps['treeData']>([
  {
    title: '课程章 1',
    value: 'charpter1',
    children: [
      {
        title: '课程节 1-0',
        value: 'charpter1-0',
        children: [
          {
            title: '制造',
            value: 'leaf1',
          },
          {
            title: '智能制造',
            value: 'leaf2',
          },
        ],
      },
      {
        title: '课程节 1-1',
        value: 'charpter1-1',
      },
    ],
  },
]);
const homework = reactive({
  id: '',//作业id
  name: '',//作业名称
  class_ids: [],//班级
  time: [] as unknown as RangeValue,//有效时间
  start_time: '',//开始时间
  end_time: '',//结束时间
  duration: '',//答题时间
  disorder: '',//乱序
  introduction: '',//描述
  status: '',//状态
  author: '',//作者
  pass_rate: '',//及格率
});
const homeworkRules = reactive({
  name: [{ required: true, message: '请输入作业名称', trigger: 'change' }],
  class_ids: [{ required: true, message: '请选择答题班级', trigger: ['change', 'blur'] }],
  time: [{ required: true, message: '请选择有效时间', trigger: 'change' }],
  duration: [{ required: true, message: '请输入考试时长', trigger: 'change' }],
  disorder: [{ required: true, message: '请选择乱序设置', trigger: 'change' }],
  introduction: [{ required: true, message: '请输入作业描述', trigger: 'change' }],
})
const { resetFields, validate, validateInfos } = useForm(homework, homeworkRules);//验证课程表单提交
const loadingSubmit = ref(false)
const onSubmit = () => {
  validate().then(() => {
    onSubmitSuccess()
  }).catch(err => {
    console.log('error', err);
  });
};


function editExamItem(item: any) {
  Object.assign(homework, item);
  console.log('homework', homework);
  homework.class_ids = item.class_names
  homework.time = [item.start_time, item.end_time]
  open.value = true
}
const onSubmitSuccess = () => {
  loadingSubmit.value = true
  const params = {
    "id": homework.id,
    "status": publishStatus.value == '1' ? '已发布' : '未发布',  // 考试/作业状态
    "name": homework.name,
    "author": homework.author,
    "course_id": getCourse().id,
    "semester_id": getCourse().semestername.value,
    "class_ids": homework.class_ids,
    "duration": homework.duration,
    "start_time": homework.time[0],
    "end_time": homework.time[1],
    "pass_rate": homework.pass_rate,
    "introduction": homework.introduction,
  }
  console.log('提交成功', params);

  editExam(params).then(res => {
    loadingSubmit.value = false
    open.value = false
    message.success('修改成功')
    getExamLists()
    resetFields();
  }).catch(err => {
    console.log(err, '===');
    loadingSubmit.value = false
    // message.error(err||'创建失败')
  })
};

async function handleDelete(item: any) {
  console.log(item, 'item');
  const confirmed = await showDeleteConfirm('确定删除本记录吗?删除后无法恢复!');
  const params = {
    exam_ids: Array.from(item)
  }
  if (confirmed) {
    deleteExam(params).then(res => {
      message.success('删除成功')
      chooseIdsSet.value.clear()
      getExamLists()
    }).catch(err => {
      // message.error(err)
    })
  }
}

const chooseIdsSet = ref<Set<number | string>>(new Set());// 选中的题目ID数组
//选中作业
function checkedChange(item: any) {
  if (chooseIdsSet.value.has(item.id)) {
    chooseIdsSet.value.delete(item.id)
  } else {
    chooseIdsSet.value.add(item.id)
  }
  console.log(chooseIdsSet.value, '选中的题目ID数组')
}

//阅卷
function handleGrade(i: any) {
  router.push({ path: '/homework/grade', query: { examid: i.id, examname: i.name } })
}
//新建作业
function handleCreate() {
  router.push({ path: '/homework/create' })
}
//学情分析
function analysis(id: number) {
  router.push({ path: '/homework/homeWorkAnalysis', query: { homework_id: id } })
}

interface HomeworkList {
  id: number;
  name: string;
  class: string;
  class_names: string[];
  submitted_count: number;//已提交
  time: string;//有效时间
  status: string;//发布状态
  checked: boolean;
  start_time: string;//开始时间
  end_time: string;//结束时间
  duration: string;//答题时间
  disorder: string;//乱序
  introduction: string;//描述
  author: string;//作者
  pass_rate: string;//及格率
}
const homeworkLists = ref<HomeworkList[]>([]) //作业列表
async function getExamLists() {
  const courseData = localStorage.getItem('course');
  const course = courseData ? JSON.parse(courseData) : null;
  const param = {
    page: currentPage.value,
    page_size: pageSize.value,
    course_id: course.id,
    semester_id: course.semestername.value,
    usage_type: route.query.type,
    status: publishStatus.value == '1' ? '已发布' : '未发布',
    // ...params
  }
  getExamList(param).then((res: any) => {
    if (res.code == 200) {
      homeworkLists.value = res.data.results.map((item: any) => {
        return {
          checked: false,
          ...item
        }
      })
      total.value = res.data.count
    }
  }).catch(error => {
    // console.log('获取失败:', error)
  })
}
getExamLists()


</script>
<style lang="scss" scoped>
.mian {
  min-height: 100vh;
  background: #F0F9FF;
  display: flex;
  flex-direction: column;
  padding: 0 40px;
  background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
  background-repeat: no-repeat;
  background-size: 100% 50%;
  background-position: center bottom;
  overflow: auto;
}

.top {
  // display: flex;
  // flex-wrap: wrap;
  // align-items: center;
  // justify-content: space-between;
  // width: 100%;

  // .title {
  //   margin: 20px 0;
  //   display: flex;
  //   flex-wrap: wrap;
  // }
}

.content {
  border-radius: 0.9px;
  border: 2px solid #FFFFFF;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  margin-bottom: 10px;
  padding: 10px;
  width: 100%;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .action-group {
      display: flex;
    }

    .action {
      border-radius: 3px;
      background: rgba(63, 140, 255, 1);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 5px 10px;
      height: 24px;

      .action1 {
        font-size: 14px;
        line-height: 14px;
        color: #fff;
        font-weight: 500;
        padding: 0;
      }
    }
  }

  .content-body {
    display: flex;
    // height: 82px;
    align-items: center;
    justify-content: space-between;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(227, 240, 252, 1);
    padding: 16px 30px 16px 27px;
    margin-top: 13px;
  }
}

.pagination {
  text-align: center;
}

:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
  background-color: transparent !important;
  gap: 5 !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff !important;
}

:deep(.btn-prev) {
  background-color: transparent !important;
}

:deep(.btn-next) {
  background-color: transparent !important;
}
</style>