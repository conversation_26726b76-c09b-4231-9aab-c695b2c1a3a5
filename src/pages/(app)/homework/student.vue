<template>
  <div class="mian">
    <back :url="'-1'" />
    <div class="top">
      <div class="title flex items-center leading-[12px]">
        <div class="font-bold " style="font-size: 24px;">{{getCourse().title}}-作业</div>
      </div>
      
    </div>

    <div class="overflow-y-auto hide-scrollbar min-w-[900px]" style="height: calc(100vh - 130px);">
      <div class="content" v-for="i in homeworkLists" :key="i.id">
        <div class="content-header">
          <div style="font-weight: 700;font-size: 16px;line-height: 16px;color: #333;">
            {{ i.name }}
          </div>
        </div>
        <div class="content-body">
          <div class="flex items-center leading-[18px] font-[14px]">
            <div class="text-[#666666] font-[400] font-[alibabapuhuiti]">分数：</div>
            <div class="font-[500]">{{ i.total_score }}</div>
          </div>
          <div class="flex items-center leading-[18px] font-[14px]">
            <div class="text-[#666666] font-[400] font-[alibabapuhuiti]">考试时长：</div>
            <div class="font-[500]">{{ i.duration }}分钟</div>
          </div>
          <div class="flex items-center justify-center w-[50%] leading-[18px] font-[14px]">
            <div class="text-[#666666] font-[400] font-[alibabapuhuiti]">考试时长：</div>
            <div class="font-[500]">{{ formatDate(i.start_time) }} 至 {{ formatDate(i.end_time) }}</div>
          </div>
          <div class="flex items-center leading-[18px] font-[14px]">
            <div class="text-[#666666]  font-[400] font-[alibabapuhuiti]">状态：</div>
            <div class="font-[500]">{{ i.take_part_status }}</div>
          </div>
          <div class="h-[18px] flex items-center gap-[24px] cursor-pointer">
            <a-tooltip>
              <template #title>
                <div>答题</div>
              </template>
              <img src="@/assets/icon/editIcon.png" class="w-[14px] h-[14px]" @click="editExamItem(i)" v-if="i.take_part_status == '未提交'" />
              <img src="@/assets/icon/editIconw.svg" class="w-[14px] h-[14px]" v-else />
            </a-tooltip>
            <a-tooltip>
              <template #title>查看</template>
              <img src="@/assets/image/img/viewIcon.png" class="w-[14px] h-[14px]" @click="viewExamItem(i)"/>
            </a-tooltip>
            
          </div>
        </div>
      </div>

      <div v-if="homeworkLists.length == 0">
        <img src="@/assets/image/zanwu/nodata.png" style="width: 309px;height: 301px;margin: auto;" />
      </div>
      <!-- 分页组件 -->
      <div class="pagination">
        <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
          layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>




    
  </div>
</template>

<script lang="ts" setup>
 import IconTrash from '~icons/lucide/trash-2'
import IconLoading from '~icons/lucide/loader-circle'
import { emitter } from '@/utils/mitter'
import { getCourse } from '@/utils/auth'
const course = getCourse()

import { useCourseStore } from '@/stores/course'
const courseStore = useCourseStore()
const { courseId } = toRefs(courseStore)

import { usePagination } from '@/hooks/usePagination'; //解构分页参数
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getExamLists);
import { examListStu } from '@/services/api/exam'
import { DeleteOutlined } from '@ant-design/icons-vue';
import type { TreeSelectProps } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
type RangeValue = [Dayjs, Dayjs];
import { Form } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
const useForm = Form.useForm
import back from '@/components/ui/back.vue';
import { formatDate } from '@/utils/util';
import { ref, reactive } from 'vue';
import { useRouter,useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()


//答卷
function editExamItem(i: any) {
  console.log(i,'----')
  window.open('/exam?id=' + i.id)
}
function viewExamItem(i: any) {
  window.open('/exam/view?id=' + i.id)
}

interface HomeworkList {
  id: number;
  name: string;
  class: string;
  class_names: string[];
  submitNum: number;//已提交
  time: string;//有效时间
  status: string;//发布状态
  checked: boolean;
  start_time: string;//开始时间
  end_time: string;//结束时间
  duration: string;//答题时间
  disorder: string;//乱序
  introduction: string;//描述
  author: string;//作者
  pass_rate: string;//及格率
  total_score: string;//总分
  take_part_status: string;//参与状态
}
const homeworkLists = ref<HomeworkList[]>([]) //作业列表
async function getExamLists() {
  const param = {
    page: currentPage.value,
    page_size: pageSize.value,
    course_semester_id:course.course_semester_id,
    usage_type:route.query.type
    // ...params
  }
  examListStu(param).then((res: any) => {
    if (res.code == 200) {
      homeworkLists.value = res.data.results
      total.value = res.data.count
    }
  }).catch(error => {
    console.error('获取失败:', error)
  })
}
onMounted(() => { 
  getExamLists()
  // 监听刷新事件
  emitter.on('refreshHomeworkList', () => {
    getExamLists()
  })
})
onUnmounted(() => {
  emitter.off('refreshHomeworkList')
})


</script>
<style lang="scss" scoped>
.mian {
  min-height: 100vh;
  background: #F0F9FF;
  display: flex;
  flex-direction: column;
  padding: 0 40px;
  background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
  background-repeat: no-repeat;
  background-size: 100% 50%;
  background-position: center bottom;
  overflow: auto;
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // min-width: 776px;
  width: 100%;

  .title {
    margin: 20px 0;
  }
}

.content {
  border-radius: 0.9px;
  border: 2px solid #FFFFFF;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  margin-bottom: 20px;
  padding: 10px;
  width: 100%;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .action-group {
      display: flex;
    }

    .action {
      border-radius: 3px;
      background: rgba(63, 140, 255, 1);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 5px 10px;
      height: 24px;

      .action1 {
        font-size: 14px;
        line-height: 14px;
        color: #fff;
        font-weight: 500;
        padding: 0;
      }
    }
  }

  .content-body {
    display: flex;
    // height: 82px;
    align-items: center;
    justify-content: space-between;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(227, 240, 252, 1);
    padding: 16px 30px 16px 27px;
    margin-top: 13px;
  }
}

.pagination {
  text-align: center;
}

:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}

:deep(.btn-prev) {
  background-color: transparent !important;
}

:deep(.btn-next) {
  background-color: transparent !important;
}
</style>