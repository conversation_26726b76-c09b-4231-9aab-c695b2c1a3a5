<template>
  <div class="mian">
    <back :url="'-1'" />
    <div class="font-bold my-2 text-[24px] max-w-[40%] flex">
        <ATooltip 
            :title="getCourse().title" 
            placement="top"
            :mouseEnterDelay="0.3"
            >
            <div class="truncate cursor-pointer">{{ getCourse().title }}</div>
        </ATooltip>
        <div class="shrink-0">-新建作业</div>
    </div>
    <!-- <div class="top">
      <div class="title truncate">{{ getCourse().title }}-作业</div>
      -新建作业
    </div> -->
    <div class="content gap-y-4">
      <div class="item cursor-pointer" @click="openChoose">
        <img src="@/assets/image/course/createbg.png" style="width: 217px;height: 189px;margin-top: 33px;" />
        <div class="itemtitle">题库选题作业</div>
        <div class="remark">将题库试题发布给学生学习</div>
      </div>
      <div class="item hover cursor-pointer">
        <img src="@/assets/image/course/createbg1.png" style="width: 217px;height: 189px;margin-top: 33px;" />
        <div class="itemtitle">知识学习作业</div>
        <div class="remark">将知识视图或知识点发布给学生学习</div>
      </div>
    </div>


    <a-modal v-model:open="open" :footer="null" :wrap-class-name="twMerge(
      '[&_.ant-modal-content]:p-[0px]!'
    )
      " width="700px">
      <div class="modal">
        <div style="height: 77px;"></div>
        <div class="modaltitle flex justify-center items-center">生成作业方式</div>
        <div class="flex justify-center flex-wrap" style="margin-top: 59px;">
          <img src="@/assets/image/course/aichoose.png" @click="aihandchoose" class="cursor-pointer" />
          <img src="@/assets/image/course/handlechoose.png" class="cursor-pointer ml-[50px]" @click="handchoose" />
        </div>
      </div>
    </a-modal>

  </div>
</template>

<script lang="ts" setup>
import { useCourseStore } from '@/stores/course'
const courseStore = useCourseStore()
// const { courseId,user_id } = toRefs(courseStore)
import { getCourse } from '@/utils/auth'

import { creatExam } from '@/services/api/exam'
import { twMerge } from 'tailwind-merge'
import back from '@/components/ui/back.vue';
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router'
const router = useRouter()
const open = ref(false);
function openChoose() {
  open.value = true;
}

async function handchoose() {
  const params = {
    course_id: getCourse().id,//
    semester_id: getCourse().semestername.value,
    // author: user_id.value
  }
  const res = await creatExam(params)
  console.log(res, '新建作业')
  courseStore.setHomeworkDetails(res.data)
  router.push({ path: '/homework/handquetion' })
}
async function aihandchoose() {
  const params = {
    course_id: getCourse().id,//
    semester_id: getCourse().semestername.value,
  }
  const res = await creatExam(params)
  console.log(res, '新建作业')
  courseStore.setHomeworkDetails(res.data)
  // router.push('/question/ailist')
  router.push({
    path: '/question/ailist',
    query: {
      type: 3 //创建作业时候，选择AI生成作业后，进入AI生成
    }
  })
}

</script>
<style lang="scss" scoped>
.mian {
  min-height: 100vh;
  background: #F0F9FF;
  display: flex;
  flex-direction: column;
  padding: 0 40px;
  background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center bottom;
  // min-width:907px;
}

.top {
  display: flex;
  // align-items: center;
  // justify-content: space-between;
  font-size: 24px;
  font-weight: 700;
  margin: 7px 0; 
  .title {
    max-width: 40%;
  }
}

.content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  min-height: calc(100vh - 118px);
}

.item {
  width: 372px;
  height: 361px;
  flex-shrink: 0;
  flex-wrap: wrap;
  opacity: 1;
  border-radius: 10px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;

  .itemtitle {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 12px;
    color: rgba(0, 0, 0, 1);
    margin-top: 26px;
  }

  .remark {
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 30px;
    color: rgba(102, 102, 102, 1);
    margin-top: 21px;
  }
}

.hover {
  filter: grayscale(100%);
}

.modal {
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('@/assets/image/course/modelbg.png');
  width: 700px;
  height: 445px;
  opacity: 1;

  .modaltitle {
    margin: 0 auto;
    background-image: url('@/assets/image/course/xubg.png');
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.14) 0%, rgba(255, 255, 255, 0.6) 22.91%, rgba(255, 255, 255, 1) 67.72%, rgba(255, 255, 255, 0) 100%);

    // border: 1px solid rgba(255, 255, 255, 1);

    width: 378px;
    height: 47px;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 39px;
    color: rgba(41, 86, 150, 1);
  }

  img {
    width: 180px;
    height: 150px;
    cursor: pointer;
  }

  // background: linear-gradient(210.8deg, rgba(250, 252, 255, 1) 0%, rgba(191, 222, 255, 1) 100%);
}
</style>