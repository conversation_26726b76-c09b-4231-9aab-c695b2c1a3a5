<template>
<a-spin :spinning="spinning">
  <div class="page">
    <!-- 顶部返回 -->
    <div class="page-header">
      <div class="left">
        <div @click="handleBack" class="back-btn">
          <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg" />
          <span class="back-text">返回</span>
        </div>
      </div>
      <div class="center">教案生成描述</div>
    </div>

    <div class="page-content">
      <!-- 主要内容区域 -->
      <div class="main-content">

        <div class="chat-window">
          <div class="avatar">
            <img src="@/assets/image/avatar.svg" alt="机器人图标">
          </div>

          <div class="bubble">
            <div class="subtitle">根据教案模板生成教案</div>
            <div class="chat-content">
              <Outline :content="content"/>
            </div>
            <!-- <div class="chat-content"  v-html="parseMarkdown(content)"></div> -->
          </div>

        </div>

        <!-- 保存按钮 -->
        <button class="save-btn" @click="createTeachPlan()">生成教案</button>

        <!-- 底部输入区域 -->
        <div class="input-wrapper">
          <a-input 
            type="text" 
            placeholder="帮我根据该模版生成出相应内容"
            v-model:value="keyword"
            class="input-field"
            @pressEnter="createTeachPlan"
            :bordered="false"
          />

          <div class="input-actions">
            <a-dropdown trigger="click" placement="top">
              <a-button style="  
                border-radius: 20px;
                display: flex;
                align-items: center;
                color:rgba(102, 102, 102, 1);
              ">
                知识库
                <CaretUpOutlined style="color:rgba(102, 102, 102, 1);"/>
              </a-button>
              <template #overlay>
                <a-menu  class="menu">
                  <a-input v-model:value="searchQuery_2" placeholder="搜索" style="margin-bottom: 10px;">
                    <template #suffix>
                      <SearchOutlined style="color:#C4C4C4"/>
                    </template>
                  </a-input>

                  <!-- 全选复选框（单独处理） -->
                  <div style="display: flex;margin-left: 12px; margin-bottom: 10px;">
                    <a-checkbox
                      :checked="checkAll_2"
                      :indeterminate="indeterminate_2"
                      @change="onCheckAllChange_2"
                    >              
                    </a-checkbox>
                    <div style="flex:1; display: flex; justify-content: space-between;margin-left: 8px;">
                      <span>全选 </span>
                    </div>
                  </div>

                  <!-- 子项复选框组 -->
                  <div class="checkbox-group-wrapper">
                    <a-checkbox-group :value="checkedList_2" @change="onGroupChange_2">
                      <div style="display: flex; flex-direction: column; gap: 10px; width: 100%;">
                        <div style="display: flex; " v-for="item in filteredItems_2" :key="item.value" >
                          <a-checkbox :value="item.value" />
                          <div style="flex:1; display: flex; justify-content: space-between; align-items: center; margin-left: 8px; ">
                            <span>{{ item.label }}</span>
                          </div>
                        </div>
                      </div>
                    </a-checkbox-group>
                  </div>

                </a-menu>
              </template>
            </a-dropdown>
            <span class="attach">
              <a-upload style="display: flex;align-items: center;" :file-list="fileList" :maxCount="1" accept=".doc,.docx"  @remove="handleRemove" :before-upload="beforeUpload">
                  <img style="width: 16px;height: 16px;" src="@/assets/image/clip.svg" />
              </a-upload>
            </span>
            <span class="send" @click="createTeachPlan()">
              <img style="width: 18px;height: 16px;" src="@/assets/image/plane.svg"/>
            </span>
          </div>
        </div>

      </div>

      
    </div>
  </div>
</a-spin>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import  Outline  from "@/components/lessonPlan/OutLine.vue";
import { parseMarkdown } from "@/utils/markdownParser";
import type { UploadProps } from 'ant-design-vue';
import { datasetsList } from '@/services/api/LessonPlan';
import { CaretUpOutlined,SearchOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

const spinning = ref(false);

const router = useRouter()
const handleBack = () => {
  router.back()
}

const keyword = ref('帮我根据该模版生成出相应内容')

const content = ref('')

console.log(window.history.state.content,'!!!')
content.value = window.history.state.content as string

const fileList = ref<UploadProps['fileList']>([])
const fileValue = ref<File>();
const handleRemove: UploadProps['onRemove'] = file => {
  fileList.value = [];
};

const beforeUpload: UploadProps['beforeUpload'] = file => {
  if (fileSizeCheck(file, 10 * 1024 * 1024)) {
    message.error('文件大小不能超过10M');
    return
  }
  fileList.value = [file];
  fileValue.value = file;
  return false;
};

// // 将文件转为 base64
// const fileToBase64 = (file:any) => {
//   return new Promise((resolve, reject) => {
//     const reader = new FileReader();
//     reader.onloadend = () => resolve(reader.result);
//     reader.onerror = reject;
//     reader.readAsDataURL(file);
//   });
// };
// const base64File:any = await fileToBase64(fileValue.value);

interface DatasetOption {
  value: number | string; 
  label: string;
}
const datasetsOptions = ref<DatasetOption[]>([]);
const filteredItems_2 = ref(); // 存储过滤后的结果

function getDatasets() {
    // const data = [{ id: 1, title: '建筑学' }]
    datasetsList().then((res:any) => {
      // console.log(res,"datadatadata")
        datasetsOptions.value = []
        res.data.forEach((item: any) => {
            datasetsOptions.value.push({
                value: item.id,
                label: item.name
            })
        })

        filteredItems_2.value = datasetsOptions.value
    })
}

onMounted(() => {
  getDatasets() // 获取知识库列表
})

const searchQuery_2 = ref(""); // 搜索框的输入内容
// 监听 searchQuery 的变化
watch(searchQuery_2, (newQuery) => {
  filteredItems_2.value = datasetsOptions.value.filter(item => 
    item.label.toLowerCase().includes(newQuery.toLowerCase())
  );
});

// 下拉菜单
const checkedList_2 = ref<any[]>([])

const checkAll_2 = computed(() => checkedList_2.value.length === datasetsOptions.value.length && datasetsOptions.value.length != 0)

const indeterminate_2 = computed(
  () => checkedList_2.value.length > 0 && checkedList_2.value.length < datasetsOptions.value.length
)

const onGroupChange_2 = (list: any) => {
  checkedList_2.value = list
  console.log(list,checkedList_2.value,'list')
}

const onCheckAllChange_2 = (e: any) => {
  checkedList_2.value = e.target.checked ? datasetsOptions.value.map(item => item.value) : []
}


const route = useRoute() //获取路由参数
//生成教案
const createTeachPlan = () => {
  if(route.query.type === 'aiSpace'){
      if(fileValue.value){
      const reader = new FileReader();
      reader.onloadend = () => {
        // 将文件转换为 base64 格式
        const base64File = reader.result as string;

        router.push({
          //  path: '/teachPlanEditor', query: { content: templateItems.value[selectedIndex.value] }
            path:'/LessonPlan/teachPlanEditor',
            state: {
              dateset_ids: JSON.stringify(checkedList_2.value),
              desc: keyword.value,
              template: content.value,
              mode:'create',
              file: {
                byte: base64File,
                name : fileValue.value?.name || 'defaultFileName',
                type : fileValue.value?.type || 'application/octet-stream' 
              }
            },
            query: { type: 'aiSpace' }
          })

      };
      reader.readAsDataURL(fileValue.value);
    }else{
      router.push({
        //  path: '/teachPlanEditor', query: { content: templateItems.value[selectedIndex.value] }
          path:'/LessonPlan/teachPlanEditor',
          state: {
            dateset_ids: JSON.stringify(checkedList_2.value),
            desc: keyword.value,
            template: content.value,
            mode: 'create',
            file: ''
          },
          query: { type: 'aiSpace' }
        })
    }
  }else{
      if(fileValue.value){
      const reader = new FileReader();
      reader.onloadend = () => {
        // 将文件转换为 base64 格式
        const base64File = reader.result as string;

        router.push({
          //  path: '/teachPlanEditor', query: { content: templateItems.value[selectedIndex.value] }
            path:'/LessonPlan/teachPlanEditor',
            state: {
              dateset_ids: JSON.stringify(checkedList_2.value),
              desc: keyword.value,
              template: content.value,
              mode:'create',
              file: {
                byte: base64File,
                name : fileValue.value?.name || 'defaultFileName',
                type : fileValue.value?.type || 'application/octet-stream' 
              }
            }
          })

      };
      reader.readAsDataURL(fileValue.value);
    }else{
      router.push({
        //  path: '/teachPlanEditor', query: { content: templateItems.value[selectedIndex.value] }
          path:'/LessonPlan/teachPlanEditor',
          state: {
            dateset_ids: JSON.stringify(checkedList_2.value),
            desc: keyword.value,
            template: content.value,
            mode: 'create',
            file: ''
          }
        })
    }
  }
}


</script>

<style scoped lang="scss">
:deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item){
  bottom: 5vw;
  right: 5vw;
  width: 200px;
}
:deep(.ant-upload-list-item-container){
  width: 0;
  height: 0;
}

.menu{
  width: 185px;
  height: 248px;
  padding: 10px;
  opacity: 1;
  border-radius: 10px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.1);
}

.checkbox-group-wrapper{
  width: 100%;
  padding-left: 34px;
  max-height: 150px;
  overflow: auto;
}

.checkbox-group-wrapper::-webkit-scrollbar {
  width: 4px; /* 滚动条宽度 */
}

.checkbox-group-wrapper::-webkit-scrollbar-thumb {
  background-color: #C7DDFC; /* 滑块颜色 */
  border-radius: 136px; /* 圆角 */
}

.checkbox-group-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #C7DDFC; /* 滑块 hover 时颜色 */
}

.page {
  width: 100%;
  height: 100vh;
  min-width: 800px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    position: relative; /* 创建层叠上下文 */
    z-index: 1; /* 确保阴影在上层 */
    min-width: 800px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    box-shadow: 0px 2px 10px  rgba(67, 143, 254, 0.1);
    padding: 0 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .center {

      font-size: 16px;
      font-weight: bold;

      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }

    .back-btn {
      cursor: pointer;
      margin-right: 20px;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }

  .page-content {
    background: url(@/assets/image/bg1.png) no-repeat center top;
    background-size: cover;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 60px);// 添加这个

    .main-content {
      flex: 1;/* 关键属性 - 填充剩余空间 */
      min-height: 0; // 添加这个
      background-color: transparent;
      border-radius: 8px;
      padding: 30px 0;
      width: 45%;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .chat-window {
        flex: 1;
        min-height: 0; // 添加这个
        display: flex;
        gap: 10px;

        .avatar {
          flex-shrink: 0;
          width: 36px;
          height: 36px;
        }

        .bubble {
          flex: 1;
          min-height: 0; // 添加这个
          overflow: auto;
          box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.05);
          padding: 20px;
          border-radius: 5px;
          background-color: #ffffff;
          color: #000;
          white-space: pre-wrap;
          word-break: break-word;
          line-height: 1.4;
          font-size: 16px;
          display: flex;
          flex-direction: column;

          .subtitle {
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(229, 229, 229, 1);
          }

          .chat-content {
            flex:1;
            overflow: auto;
            scrollbar-width: none;
            margin-top: 20px;
          }
        }
      }

      .save-btn {
        width: 100px;
        background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
        color: white;
        border: none;
        padding: 8px 10px;
        border-radius: 4px;
        cursor: pointer;
        margin: 20px 46px;
      }

      .input-wrapper {
        display: flex;
        background-color: #ffffff;
        padding: 0.8vw;
        border-radius: 0.5vw;
        box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.05);

        .input-field {
          flex: 1;
          border: none;
          outline: none;
          padding: 0 1vw;
          font-size: 1vw;
          border-radius: 0.5vw;
          background-color: #ffffff;
        }

        .input-actions {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .el-dropdown-link {
          width: 6vw;
          height: 4vh;
          font-size: 0.8vw;
          font-weight: 500;
          gap: 0.2vw;
          cursor: pointer;
          background-color: white;
          user-select: none;
          border: 0.1vw solid #dcdfe6;
          padding: 0.5vh 0.8vw;
          border-radius: 94px;
          display: inline-flex;
          align-items: center;
        }

        .attach,
        .send {
          cursor: pointer;
        }

        /* .attach {
          display: inline-flex;
          align-items: center;
        } */

        .send {
          width: 50px;
          height: 30px;
          background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
          color: white;
          padding: 7px 15px;
          border-radius: 148px;
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}

</style>
