<template>
  <a-spin :spinning="spinning">
  <div class="page">
    <!-- 顶部返回 -->
    <div class="page-header">
      <div class="left">
        <div @click="handleBack" class="back-btn">
          <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg" />
          <span class="back-text">返回</span>
        </div>
      </div>
      <div class="center">教案模板生成</div>
      <div class="right"></div>
    </div>

    <div class="page-content">
      <!-- 主要内容区域 -->
      <div class="main-content">

        <div class="chat-window">
          <div class="avatar">
            <img src="@/assets/image/avatar.svg" alt="机器人图标">
          </div>

          <img v-if="loading" src="@/assets/image/3dots.svg" style="width: 36px;height: 36px;">

          <div class="bubble" v-else>
            <div class="subtitle">根据课程-授课方向返回教案模板</div>

              <!-- <pre ref="outlineRef" v-if="outlineCreating">{{ outline }}</pre>
              <div class="chat-content" v-else>
                <OutlineTimeline :items="timelineItems"/>
              </div> -->

            <!-- <div class="chat-content" >
              {{ timelineItems }}
              <OutlineTimeline :items="timelineItems" />
            </div> -->
            <div class="chat-content" v-if="isCreating" v-html="parseMarkdown(timelineItems)"></div>
            <div class="chat-content" v-else>
              <Outline :content="content"/>
            </div>
          </div>

        </div>

        <!-- 保存按钮 -->
        <button class="save-btn" @click="saveTemp()">保存</button>

        <!-- 底部输入区域 -->
        <div class="input-wrapper">
          <input
            type="text"
            placeholder="请输入教案模板主题，如：物理"
            v-model="keyword"
            class="input-field"
            @enter="createTeachPlanTemp()"
          >

          <div class="input-actions">
            <!-- <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                课程章节
                <img style="width: 12px;height: 12px;" src="@/assets/image/ArrowDown.svg" />
              </span>
              <template #dropdown>
                <chapter-selector :tree-data="treeData" @update:checked="handleCheckedUpdate"
                  ref="chapterSelectorRef" />
              </template>
            </el-dropdown> -->
          <a-dropdown trigger="click" placement="top">
            <a-button style="
              border-radius: 20px;
              display: flex;
              align-items: center;
              color:rgba(102, 102, 102, 1);
            ">
              选择课程
              <CaretUpOutlined style="color:rgba(102, 102, 102, 1);"/>
            </a-button>
            <template #overlay>
              <a-menu  class="menu">
                <a-input v-model:value="searchQuery_2" placeholder="搜索" style="margin-bottom: 10px;">
                  <template #suffix>
                    <SearchOutlined style="color:#C4C4C4"/>
                  </template>
                </a-input>

                <!-- 全选复选框（单独处理） -->
                <div style="display: flex;margin-left: 12px; margin-bottom: 10px;">
                  <a-checkbox
                    :checked="checkAll_2"
                    :indeterminate="indeterminate_2"
                    @change="onCheckAllChange_2"
                  >
                  </a-checkbox>
                  <div style="flex:1; display: flex; justify-content: space-between;margin-left: 8px;">
                    <span>全选 </span>
                  </div>
                </div>

                <!-- 子项复选框组 -->
                <div class="checkbox-group-wrapper">
                  <a-checkbox-group :value="checkedList_2" @change="onGroupChange_2">
                    <div style="display: flex; flex-direction: column; gap: 10px; width: 100%;">
                      <div style="display: flex; " v-for="item in filteredItems_2" :key="item.value" >
                        <a-checkbox :value="item.value" />
                        <div style="flex:1; display: flex; justify-content: space-between; align-items: center; margin-left: 8px; ">
                          <span>{{ item.label }}</span>
                        </div>
                      </div>
                    </div>
                  </a-checkbox-group>
                </div>

              </a-menu>
            </template>
          </a-dropdown>

            <span class="attach">
              <a-upload style="display: flex;align-items: center;" :file-list="fileList" :maxCount="1" accept=".doc,.docx"  @remove="handleRemove" :before-upload="beforeUpload">
                  <img style="width: 16px;height: 16px;" src="@/assets/image/clip.svg" />
              </a-upload>
            </span>
            <span class="send" @click="createTeachPlanTemp()">
              <img style="width: 18px;height: 16px;" src="@/assets/image/plane.svg"/>
            </span>
          </div>
        </div>

      </div>


    </div>
  </div>
  </a-spin>
</template>

<script lang="ts" setup>
import { parseMarkdown } from "@/utils/markdownParser";
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import ChapterSelector from '@/components/lessonPlan/ChapterSelector.vue'
import { saveTeachPlanTemp, getTeachPlanTemp } from '@/services/api/LessonPlan'
import type { UploadProps } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import  Outline  from "@/components/lessonPlan/OutLine.vue";
import { courseOwner } from '@/services/api/course';
import { CaretUpOutlined,SearchOutlined } from '@ant-design/icons-vue';

definePage({
  meta: {
    hideUserInfo: true
  }
})

const router = useRouter()
const handleBack = () => {
  router.back()
}

const courseOptions = ref([{
    value: 0,
    label: '未关联课程'
}])
const filteredItems_2 = ref(); // 存储过滤后的结果
function getcourseOwner() {
    // const data = [{ id: 1, title: '建筑学' }]
    courseOwner().then((res:any) => {
      // console.log(res,"datadatadata")
        courseOptions.value = []
        res.data.forEach((item: any) => {
            courseOptions.value.push({
                value: item.id,
                label: item.title
            })
        })

        filteredItems_2.value = courseOptions.value
    })
}


onMounted(() => {
  getcourseOwner() // 获取课程列表
})

const spinning = ref(false);
const keyword = ref('')
const loading = ref(false)
const isCreating = ref(false)
const content = ref('')

const fileList = ref<UploadProps['fileList']>([])
const fileValue = ref<File>();
const handleRemove: UploadProps['onRemove'] = file => {
  fileList.value = [];
};

const beforeUpload: UploadProps['beforeUpload'] = file => {
  if (fileSizeCheck(file, 10 * 1024 * 1024)) {
    message.error('文件大小不能超过10M');
    return
  }
  fileList.value = [file];
  fileValue.value = file;
  return false;
};

const timelineItems = ref('');

const createTeachPlanTemp = async () => {
  isCreating.value = true
  loading.value = true

  timelineItems.value = ''
  if (keyword.value === '') {
    message.error('请输入模板主题！');
  }

  const params = {
    theme:keyword.value,
    document:fileValue.value
  }

  const res = await getTeachPlanTemp(params)

  if (!res.body) {
    message.error('响应体为空');
    return;
  }
  if (!res.ok) {
    message.error(`HTTP请求错误! 状态码: ${res.status}`);
    return;
  }

  const reader: ReadableStreamDefaultReader = res.body.getReader()
  const decoder = new TextDecoder('utf-8')

  const readStream = () => {
    reader.read().then(({ done, value }) => {
      loading.value = false

      if (done) {
        isCreating.value = false
        content.value = timelineItems.value
        return
      }

      const chunk = decoder.decode(value, { stream: true })
      timelineItems.value += chunk
      const chatContent = document.querySelector('.chat-content');

      if (chatContent) {
        chatContent.scrollTop = chatContent.scrollHeight;
      }

      readStream()
    })
  }
  readStream()
}

const searchQuery_2 = ref(""); // 搜索框的输入内容
// 监听 searchQuery 的变化
watch(searchQuery_2, (newQuery) => {
  filteredItems_2.value = courseOptions.value.filter(item =>
    item.label.toLowerCase().includes(newQuery.toLowerCase())
  );
});

// 下拉菜单
const checkedList_2 = ref<any[]>([])

const checkAll_2 = computed(() => checkedList_2.value.length === courseOptions.value.length && courseOptions.value.length != 0)

const indeterminate_2 = computed(
  () => checkedList_2.value.length > 0 && checkedList_2.value.length < courseOptions.value.length
)

const onGroupChange_2 = (list: any) => {
  checkedList_2.value = list
  console.log(list,checkedList_2.value,'list')
}

const onCheckAllChange_2 = (e: any) => {
  checkedList_2.value = e.target.checked ? courseOptions.value.map(item => item.value) : []
}

const saveTemp = async() => {
  const match = timelineItems.value.match(/^# (.+)$/m);
  const title = match ? match[1] : '无标题';

  const params = {
      title: title,
      content: content.value,
      courses: checkedList_2.value
  }
  try {
    spinning.value = true
    const res = await saveTeachPlanTemp(params)
    spinning.value = false
    message.success('保存成功！')
  } catch (error) {
    message.error('保存失败')
  }
}

</script>

<style scoped lang="scss">
:deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item){
  bottom: 5vw;
  right: 5vw;
  width: 200px;
}
:deep(.ant-upload-list-item-container){
  width: 0;
  height: 0;
}

.menu{
  width: 185px;
  height: 248px;
  padding: 10px;
  opacity: 1;
  border-radius: 10px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.1);
}

.checkbox-group-wrapper{
  width: 100%;
  padding-left: 34px;
  max-height: 150px;
  overflow: auto;
}

.checkbox-group-wrapper::-webkit-scrollbar {
  width: 4px; /* 滚动条宽度 */
}

.checkbox-group-wrapper::-webkit-scrollbar-thumb {
  background-color: #C7DDFC; /* 滑块颜色 */
  border-radius: 136px; /* 圆角 */
}

.checkbox-group-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #C7DDFC; /* 滑块 hover 时颜色 */
}

.page {
  width: 100%;
  height: 100vh;
  min-width: 800px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    position: relative; /* 创建层叠上下文 */
    z-index: 1; /* 确保阴影在上层 */
    min-width: 800px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    box-shadow: 0px 2px 10px  rgba(67, 143, 254, 0.1);
    padding: 0 20px;

    .left,
    .right {
      display: flex;
      align-items: center;
    }

    .center {
      flex: 1;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
    }

    .back-btn {
      cursor: pointer;
      margin-right: 20px;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }

  .page-content {
    background: url(@/assets/image/bg1.png) no-repeat center top;
    background-size: cover;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 60px);// 添加这个

    .main-content {
      flex: 1;/* 关键属性 - 填充剩余空间 */
      min-height: 0; // 添加这个
      background-color: transparent;
      border-radius: 8px;
      padding: 30px 0;
      width: 45%;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .chat-window {
        flex: 1;
        min-height: 0; // 添加这个
        display: flex;
        gap: 10px;

        .avatar {
          flex-shrink: 0;
          width: 36px;
          height: 36px;
        }

        .bubble {
          flex: 1;
          min-height: 0; // 添加这个
          overflow: auto;
          box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.05);
          padding: 20px;
          border-radius: 5px;
          background-color: #ffffff;
          color: #000;
          white-space: pre-wrap;
          word-break: break-word;
          line-height: 1.4;
          font-size: 16px;
          display: flex;
          flex-direction: column;

          .subtitle {
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(229, 229, 229, 1);
          }

          .chat-content {
            flex:1;
            overflow: auto;
            scrollbar-width: none;
            margin-top: 20px;
          }
        }
      }

      .save-btn {
        width: 100px;
        background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
        color: white;
        border: none;
        padding: 8px 10px;
        border-radius: 4px;
        cursor: pointer;
        margin: 20px 46px;
      }

      .input-wrapper {
        display: flex;
        background-color: #ffffff;
        padding: 0.8vw;
        border-radius: 0.5vw;
        box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.05);

        .input-field {
          flex: 1;
          border: none;
          outline: none;
          padding: 0 1vw;
          font-size: 1vw;
          border-radius: 0.5vw;
          background-color: #ffffff;
        }

        .input-actions {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .el-dropdown-link {
          width: 6vw;
          height: 4vh;
          font-size: 0.8vw;
          font-weight: 500;
          gap: 0.2vw;
          cursor: pointer;
          background-color: white;
          user-select: none;
          border: 0.1vw solid #dcdfe6;
          padding: 0.5vh 0.8vw;
          border-radius: 94px;
          display: inline-flex;
          align-items: center;
        }

        .attach,
        .send {
          cursor: pointer;
        }

        /* .attach {
          display: inline-flex;
          align-items: center;
        } */

        .send {
          width: 50px;
          height: 30px;
          background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
          color: white;
          padding: 7px 15px;
          border-radius: 148px;
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}
</style>
