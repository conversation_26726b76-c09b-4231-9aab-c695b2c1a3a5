<template>
  <div class="page">

      <div @click="handleBack" class="back-btn">
        <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
        <span style="font-size: 14px;line-height: 14px;">返回</span>
      </div>
      <h1 class="text">教案管理</h1>
      <div class="toolbar gap-y-2">
        <div class="toolbar-left">
          <div style="display: flex;" class="flex-wrap gap-2">
            <AButton type="primary" class="gradient-a-button w-[82px]" @click="handleAddTeachPlan">
                新建
            </AButton>
            <a-upload :file-list="fileList" :before-upload="beforeUpload" accept=".doc,.docx"  :showUploadList="false">
              <a-button type="primary" ghost :loading="uploadloading" style="width: 82px;margin: 0 10px;">上传</a-button>
            </a-upload>
            
            <a-button type="primary" ghost style="width: 82px;" @click="teachPlanRecycle">回收站</a-button>
            <a-button type="primary" ghost style="width: 127px;margin-left: 10px;margin-right: 36px" @click="dialogVisible = true">关联课程-章节</a-button>
            
            <a-button type="primary" class="but"  @click="downloadByIds(selectedIds)" ghost
            :icon="h(DownloadOutlined)">下载</a-button>
            <a-button type="primary" class="but" style="margin-left: 10px;" @click="delMessage(selectedIds)" ghost 
            :icon="h(DeleteOutlined)">删除</a-button>

            <a-modal v-model:open="dialogVisible" title="关联课程-章节" :footer="null" width="700px">
              <img v-if="loading" src="@/assets/image/3dots.svg" style="width: 36px;height: 36px;">
              <chapter-selector v-else :treeData="treeData" @update:checkedKeys="handleCheckedKeys"/>

              <div style="display: flex; justify-content: flex-end; align-items: center;margin-top: 10px;">
                  <a-space>
                      <a-button @click="dialogVisible = false">取消</a-button>
                      <a-button type="primary" html-type="submit" @click="relationChapter">确定</a-button>
                  </a-space>
              </div>
            </a-modal>

          </div>

        </div>
        <div class="toolbar-right">
          <a-dropdown trigger="click" placement="bottom">
            <a-button 
              style="  
              border-radius: 20px;
              display: flex;
              align-items: center;
              color:rgba(102, 102, 102, 1);"
            >
              选择章节
              <CaretDownOutlined style="color:rgba(102, 102, 102, 1);"/>
            </a-button>
            <template #overlay>
              <a-menu  class="menu">
              <img v-if="loading" src="@/assets/image/3dots.svg" style="width: 36px;height: 36px;">
              <chapter-selector v-else :treeData="treeData" @update:checkedKeys="handleCheckedKeys"/>
              </a-menu>
            </template>
          </a-dropdown>

          <a-input v-model:value="searchValue" style="border-radius: 94px;width: 302px;min-height: 32px;" placeholder="输入名称查询" @pressEnter="getTeachPlanList">
            <template #suffix>
              <SearchOutlined style="color:#C4C4C4" @click="getTeachPlanList"/>
            </template>
          </a-input>
        </div>
      </div>
      <a-spin :spinning="spinning">
        <div class="table">
          <div class="table-head height" style="font-weight:600">
            <div class="item" style="max-width: 56px;">
              <a-checkbox v-model:checked="state.checkAll" :indeterminate="state.indeterminate"
                @change="onCheckAllChange" />
            </div>
            <div class="item" style="width: 400px;">教案名称</div>
            <div class="item" style="width: 130px;">创建者</div>
            <div class="item" style="width: 140px;">更新时间</div>
            <div class="item" style="max-width: 118px;">操作</div>
          </div>

          <div class="center" v-if="files?.length === 0">
            <img src="@/assets/image/zanwu/nodata.png" style="width: 385px;height: 379px;" />
          </div>
          <div v-else>
            <div class="table-head height1" v-for="(item, index) in files" :key="item.id">
              <div class="item" style="max-width: 58px;">
                <a-checkbox :checked="selectedIds.includes(item.id)"
                  @change="(e: any) => handleCheckboxChange(item.id, e.target.checked)" />
              </div>
              <div class="item file-name titlew">
                <div class="file-name-text">
                  <a-tooltip v-if="editingIndex !== index" :title="item.title" placement="bottom">
                    <span style="cursor: pointer;" @click="handleEditTeachPlan(item)">{{ item.title }}</span>
                  </a-tooltip>
                  <a-input v-else v-model:value="teachPlanTitle" @change="handleChange" @blur="editingIndex = null" ref="inputRef" class="topTitle-input" style="max-width: 300px;" >
                    <template #suffix>
                      <CheckCircleOutlined  @mousedown.prevent style="color:#C4C4C4;margin-left:10px;" @click="saveTitle(item.id)"/>
                      <CloseCircleOutlined  @mousedown.prevent style="color:#C4C4C4;margin-left:5px;" @click="cancleSaveTitle"/>
                    </template> 
                  </a-input>
                </div>
                <span class="edit-icon">
                  <a-tooltip placement="bottom">
                    <template #title>
                      <span>编辑</span>
                    </template>
                    <img style="cursor: pointer;width: 14px;height: 14px;" src="@/assets/image/img/edittable.png" @click="startEditing(index,item.title)" />
                  </a-tooltip>
                </span>
              </div>
              <div class="item" style="width: 130px;">
                {{ item.author_first_name || '无' }}
              </div>
              <div class="item" style="width: 140px;">
                {{ formatDate(item.updated_at) }}
              </div>
              <div class="item" style="max-width: 118px;display: flex;align-items: center;">
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>下载</span>
                  </template>
                  <DownloadOutlined style="color: #3F8CFF;font-size: 14px;" @click="downloadByIds([item.id])" />
                </a-tooltip>
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>删除</span>
                  </template>
                  <img style="cursor: pointer;margin-left: 16px;" src="@/assets/image/delete.svg" @click="delMessage(item.id)" />
                </a-tooltip>
              </div>
            </div>
          </div>

          <div class="pagination">
            <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
              layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </div>
      </a-spin>

      <Preview v-model:show="showPreDialog" :item="previewItem"/>
  </div>
</template>

<script lang="ts" setup>
import { formatDate } from '@/utils/util'
import { DeleteOutlined, DownloadOutlined,CheckCircleOutlined,CloseCircleOutlined,SearchOutlined,CaretDownOutlined} from '@ant-design/icons-vue';
//引入接口
import { teachPlanList, teachPlan_softDelete, teachPlanUpload, search_teachplan,editTeachPlan,associateChapter } from '@/services/api/LessonPlan'
import { usePagination } from '@/hooks/usePagination'; //解构分页参数
import { ref, computed, markRaw, onMounted, reactive, watch, h , createVNode} from 'vue'
import { Search, WarningFilled, } from '@element-plus/icons-vue'
import { useRouter,useRoute} from 'vue-router'
import { message, Modal} from 'ant-design-vue';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import type { UploadProps } from 'ant-design-vue';
import ChapterSelector from '@/components/lessonPlan/ChapterSelector.vue'
import { downloadWord } from '@/utils/downloadFile'
import { chapterList, chapterOwner } from '@/services/api/course';

const router = useRouter()
const route = useRoute() //获取路由参数

const fileList = ref<UploadProps['fileList']>([]);
const uploadloading = ref(false)
const beforeUpload: UploadProps['beforeUpload'] = file => {
  if (fileSizeCheck(file, 10 * 1024 * 1024)) {
    message.error('文件大小不能超过10M');
    return
  }
  uploadloading.value = true
  fileList.value = [...(fileList.value || []), file];
  console.log(file,'file')
  const params = new FormData();
  params.append('file', file);
  params.append('title', fileList.value[0].name);
  teachPlanUpload(params).then(res => {
    message.success('上传成功');
    uploadloading.value = false
    getTeachPlanList()
  }).catch(err => {
    message.error('上传失败');
    uploadloading.value = false
  })
  return false;
};


const handleAddTeachPlan = () => {
  router.push('/lessonPlan/teachPlanTempEditor')
}

//当前页所有id
const currentPageIds = computed(() => files.value?.map(item => item.id) || []);
// 已选中的id
const selectedIds = ref<number[]>([]);
// 处理单个复选框的选择变化
const handleCheckboxChange = (id: number, checked: boolean) => {
  if (checked) {
    selectedIds.value.push(id);
  } else {
    selectedIds.value = selectedIds.value.filter((item) => item !== id);
  }
};
// 全选操作
const onCheckAllChange = (e: any) => {
  const isChecked = e.target.checked;
  if (isChecked) {
    selectedIds.value = [...new Set([...selectedIds.value, ...currentPageIds.value])]; // 合并去重
  } else {
    selectedIds.value = selectedIds.value.filter(id => !currentPageIds.value.includes(id));
  }
  state.indeterminate = false;
};
const state = reactive({
  indeterminate: false,
  checkAll: false,
  checkedList: [],
});

//定义列表数据结构
interface fileList {
  id: number;
  title: string;
  author: string;
  updated_at: string;
  author_first_name: string;
}
// 模拟数据
const files = ref<fileList[]>()
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getTeachPlanList);
const spinning = ref(false)

const searchValue = ref("")

// 搜索教案
// async function searchTeachPlan () {
//   const params = {
//       title: searchValue.value,
//       is_deleted: 'false',
//   }
//   try {
//     spinning.value = true
//     const res = await search_teachplan(params)
//     files.value = res.data.results
//     total.value = res.data.count
//     spinning.value = false

//   } catch (error) {
//     console.error('获取教案列表失败:', error)
//   }
// }

const courseId = ref('');
onMounted(() => {
  courseId.value = '';
  if(route.query.type === 'aiSpace'){
    getTeachPlanList() //  获取教案列表
    getChapterOwner() // 获取课程章节列表(个人空间)
  }else{
    // 从 localStorage 中获取数据
    const storedCourse = JSON.parse(localStorage.getItem('course') as string);
    // 获取 id 值
    courseId.value = String(storedCourse?.id);
    console.log('courseId.value',courseId.value)
    getTeachPlanList() //  获取教案列表
    getChapterById(courseId.value) // 获取课程章节列表
  }
})

// 获取教案列表
async function getTeachPlanList () {
  const params = {
    page: currentPage.value,
    pageSize: pageSize.value,
    is_deleted: 'false',
    course_id: courseId.value,
    chapter_id: checkedKeys.value.join(),
    search: searchValue.value,
  }
  try {
    spinning.value = true
    const res = await teachPlanList(params)
    files.value = res.data.results
    total.value = res.data.count
    spinning.value = false
  } catch (error) {
    console.error('获取教案列表失败:', error)
  }
}


// 教案关联章节
async function relationChapter() {
  console.log('checkedList!',state.checkedList,checkedKeys.value)
  if(selectedIds.value.length === 0){
    message.error(`请选择要关联的教案`);
  }else if(checkedKeys.value.length === 0){
    message.error(`请选择要关联的课程`);
  }else{
    const params = {
      teachplan_ids: selectedIds.value,  //教案id
      chapter_id:  checkedKeys.value //课程id
    }
    associateChapter(params).then(res => {
        message.success('关联成功')
        getTeachPlanList()
        dialogVisible.value = false
        checkedKeys.value = []
    })        
    .catch(error => {
        message.error(`关联失败: ${error.message || '未知错误'}`);
    });
  }
}


const handleBack = () => {
  //需要动态配置课程id
  // router.push('/course/1')
  router.back()
}

//删除
const delMessage = async (params: any) => {
  const ids = Array.isArray(params) ? params : [params]
  if(ids.length == 0){
    message.error('请选择要删除的记录');
    return
  } 
  const confirmed = await showDeleteConfirm('确定要删除这条记录吗？');
  if (confirmed) {
    softDelete(ids)
  }
  console.log(confirmed);
}
async function softDelete(id: number[]) {
  const params = {
    "ids": id,  //传单个及单个删除
    "action": "delete"  //restore:恢复，delete：软删除
  }
  try {
    const res = await teachPlan_softDelete(params)
    getTeachPlanList()
  } catch (error) {
    message.error('删除失败')
  }
}

function teachPlanRecycle() {
  if(route.query.type === 'aiSpace'){
    router.push({
      path: '/LessonPlan/teachPlanRecycle',
      query: { type: 'aiSpace' }
    })
  }else{
    router.push('/LessonPlan/teachPlanRecycle')
  }
};

async function downloadByIds(ids: any[]) {
  let newFiles:any = files.value?.filter(item => ids.includes(item.id))
  newFiles.forEach((obj: any) => {
    if(obj.file == null){
      downloadWord(obj.content, obj.title + '.doc');
    }else{
      window.open(obj.file, '_blank');
    }
  })
}

// async function downloadTeachPlan(item: any) {
//   console.log(item);
//   if(item.file == null){
//     downloadWord(item.content, item.title + '.doc');
//   }else{
//     window.open(item.file, '_blank');
//   }
// }

const showPreDialog = ref(false)
const previewItem = ref();

const handleEditTeachPlan = (item: any) => {
  console.log(item.file);
  if(item.file != null){
    item.file_type = 'word'
    previewItem.value = item
    showPreDialog.value = true;
    return
  }
    else{
        router.push({
            path:'/LessonPlan/teachPlanEditor',
            state: {
                id: item.id,
                title: item.title,
                content: item.content,
                mode:'edit'
            }
        })
    }
}

const treeData = ref<TreeNode[]>([])
const loading = ref<boolean>(true);

//转换 API 数据为 TreeNode 类型
const convertToTreeNode = (data: any[]): TreeNode[] => {
  return data.map(item => ({
    title: item.title, // 根据后端字段修改
    key: item.id,     // 根据后端字段修改
    children: item.children ? convertToTreeNode(item.children) : undefined
  }));
};

//加载章节树结构
const getChapterById = async (courseId:any) => {
  loading.value = true;
  const params = {
    course_id: courseId
  }
  try {
    const res = await chapterList(params); // 替换为你的实际 API
    treeData.value = convertToTreeNode(res.data);
  } catch (error) {
    console.error('获取章节树失败', error);
  } finally {
    loading.value = false;
  }
};

//加载章节树结构
const getChapterOwner = async () => {
  loading.value = true;
  try {
    const res = await chapterOwner(); // 替换为你的实际 API
    treeData.value = convertToTreeNode(res.data);
  } catch (error) {
    console.error('获取章节树失败', error);
  } finally {
    loading.value = false;
  }
};

const dialogVisible = ref(false)

type Key = string | number;

// 定义数据结构类型
interface TreeNode {
  title: string;
  key: string;
  children?: TreeNode[];
}

const checkedKeys = ref<Key[]>([]);

const handleCheckedKeys = (keys: Key[]) => {
  checkedKeys.value = keys;
  console.log('选中的章节ID:', checkedKeys.value);
  if(!dialogVisible.value === true){
    getTeachPlanList()
  }
};


const editingIndex = ref(null)
const title = ref('这是一个可编辑标题')
const teachPlanTitle = ref('')
const inputRef = ref(null)
const MAX_LENGTH = 50;

const handleChange = () => {
  if (teachPlanTitle.value.length > MAX_LENGTH) {
    message.error(`输入不能超过 ${MAX_LENGTH} 个字符`);
    // 截断超长字符（防止后续逻辑异常）
    teachPlanTitle.value = teachPlanTitle.value.slice(0, MAX_LENGTH);
  }
};

function startEditing(index: any, itemTitle: any) {
  editingIndex.value = index
  title.value = itemTitle
  teachPlanTitle.value = itemTitle
  // 等 DOM 渲染完成后聚焦
  // nextTick(() => {
  //   inputRef.value?.focus()
  // })
}

function saveTitle(tempId:any) {
  title.value = teachPlanTitle.value.trim() || title.value
  editingIndex.value = null
  savePlanTitle(tempId).then(() => {
    message.success('标题保存成功！')
    getTeachPlanList()
  }).catch(() => {
    message.error('标题保存失败！')
  })
}

const savePlanTitle = async(tempId:any) => {
  const params = {
      title: title.value , // 教案模板标题
      // course_id:  checkedList_1.value //课程id
  }
  try {
    spinning.value = true
    const res = await editTeachPlan(tempId, params)
    console.log(res);
    spinning.value = false //加载
    // message.success('保存成功！')//保存成功提示
  } catch (error) {
    // message.error('保存失败')
  }
}

function cancleSaveTitle() {
  teachPlanTitle.value = title.value
  editingIndex.value = null
}

</script>

<style scoped lang="scss">
:deep(.el-input__suffix-inner) {
    cursor: pointer;
}

.menu{
  width: 185px;
  height: 248px;
  padding: 10px;
  opacity: 1;
  border-radius: 10px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.1);
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0 40px;

  .back-btn {
    cursor: pointer;
    width: 60px;
    margin-top: 50px;
    color: #333;
    display: flex;
    align-items: center;
  }

}

.text {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 12px;
  margin: 20px 0 20px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  min-width: 900px;
  flex-wrap: wrap;

  .toolbar-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 36px;

    .addbut {
      width: 82px;
      color: #fff;
      background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
    }

    .but{
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .toolbar-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
}

:deep(.el-select__wrapper) {
  border-radius: 95px;
}


:deep(.el-input__wrapper) {
  border-radius: 94px;
}


.table {
  background: #fff;
  border-radius: 4.79px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 0 20px 20px;
  min-width: 900px;
  width: 100%;


  .table-head {
    // display: flex;
    align-items: center;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    /* 禁止换行 */
    overflow-x: auto;
    /* 允许水平滚动（如果需要） */
    font-size: 14px;
    padding-left: 12px;

    .item {
      flex: 1 0 auto;
      white-space: nowrap;
      /* 防止文字换行 */
    }

    .flex {
      display: flex;
      align-items: center;
    }

    .titlew {
      width: 400px;
      position: relative;
      height: 56px;
      line-height: 56px;
    }

    .topTitle-input{
      height: 32px;
      padding-left:10px;

      border-radius: 5px;
      border: 1px solid #3F8CFF;
      box-shadow: 0px 1px 4px  #3F8CFF;

      color: #333;
      // width: 300px;
      white-space: nowrap;      /* 不换行 */
      overflow-x: auto;         /* 超出时显示横向滚动条 */  
    }

    .file-name-text {
      max-width: 330px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden
    }

    .file-name .edit-icon {
      opacity: 0;
      transition: opacity 0.3s ease;
      position: absolute;
      left: 345px;
      top: 50%;
      transform: translateY(-50%);
    }

    .file-name:hover .edit-icon {
      opacity: 1;
    }
  }

  .height {
    height: 56px;
    border-bottom: 1px solid #E5E5E5;
  }

  .height1 {
    height: 58px;
    border-bottom: 1px solid #E5E5E5;
    color: #666666;

    &:hover {
      background: rgba(63, 140, 255, 0.05);
    }
  }
}

.pagination {
  margin: 20px 0 12px 0;
  text-align: center;
}

:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}
</style>
