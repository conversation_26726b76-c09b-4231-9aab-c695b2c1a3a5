<template>
  <div class="page">

      <div @click="handleBack" class="back-btn">
        <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
        <span style="font-size: 14px;line-height: 14px;">返回</span>
      </div>
      <h1 class="text">模板管理</h1>
      <div class="toolbar gap-y-2">
        <div class="toolbar-left">
          <div style="display: flex;" class="flex-wrap gap-2">
            <AButton type="primary" class="gradient-a-button w-[82px]" @click="handleAddTeachPlanTemp">
                新建
            </AButton>
            <a-upload :file-list="fileList" :before-upload="beforeUpload" accept=".doc,.docx"  :showUploadList="false">
              <a-button type="primary" ghost :loading="uploadloading" style="width: 82px;margin: 0 10px;">上传</a-button>
            </a-upload>
            
            <a-button type="primary" ghost style="width: 82px;" @click="teachPlanTempRecycle">回收站</a-button>
            <a-button type="primary" ghost style="width: 82px;margin-left: 10px;margin-right: 36px;padding: 0;" @click="dialogVisible = true">关联课程</a-button>
            
            <a-button type="primary" class="but"  @click="downloadByIds(selectedIds)" ghost
              :icon="h(DownloadOutlined)">下载</a-button>
            <a-button type="primary" class="but" style="margin-left: 10px;" @click="delMessage(selectedIds)" ghost
              :icon="h(DeleteOutlined)">删除</a-button>

            <a-modal v-model:open="dialogVisible" title="关联课程" :footer="null" width="700px">
                <a-input v-model:value="searchQuery_1" placeholder="搜索" style="margin-bottom: 10px;">
                  <template #suffix>
                    <SearchOutlined style="color:#C4C4C4"/>
                  </template>
                </a-input>
                <!-- 全选复选框（单独处理） -->
                <div style="display: flex;margin-left: 12px; margin-bottom: 10px;">
                  <a-checkbox
                    v-model:checked="checkAll_1"
                    :indeterminate="indeterminate_1"
                    @change="onCheckAllChange_1"
                  >              
                  </a-checkbox>
                  <div style="flex:1; display: flex; justify-content: space-between;margin-left: 8px;">
                    <span>全选 </span>
                  </div>
                </div>

                <!-- 子项复选框组 -->
                <div class="checkbox-group-wrapper" style="max-height: 300px;">
                  <a-checkbox-group :value="checkedList_1" @change="onGroupChange_1">
                    <div style="display: flex; flex-direction: column; gap: 10px; width: 100%;">
                      <div style="display: flex; " v-for="item in filteredItems_1" :key="item.value" >
                        <a-checkbox :value="item.value" />
                        <div style="flex:1; display: flex; justify-content: space-between; align-items: center; margin-left: 8px; ">
                          <span>{{ item.label }}</span>
                        </div>
                      </div>
                    </div>
                  </a-checkbox-group>
                </div>
              <div style="display: flex; justify-content: flex-end; align-items: center;margin-top: 10px;">
                  <a-space>
                      <a-button @click="dialogVisible = false">取消</a-button>
                      <a-button type="primary" html-type="submit" @click="relationCourse">确定</a-button>
                  </a-space>
              </div>
            </a-modal>

            <!-- <el-dialog
              v-model="dialogVisible"
              title="关联课程-章节"
            >
                <chapter-selector
                  :tree-data="treeData"
                  @update:checked="handleCheckedUpdate"
                  ref="chapterSelectorRef"
                />
              <template #footer>
                <div class="dialog-footer">
                  <el-button type="primary" @click="dialogVisible = false">
                    保留已关联知识点
                  </el-button>
                </div>
              </template>
            </el-dialog> -->

          </div>

        </div>
        <div class="toolbar-right">
          <!-- <el-select v-model="valueSelect" placeholder="课程章节" style="width: 100px;min-height: 32px;"
            suffixIcon="CaretBottom">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select> -->
          <!-- <el-input v-model="searchValue" style="width: 302px;min-height: 32px;" placeholder="请输入" 
            @keyup.enter="searchTeachPlanTemp" >
            <template #suffix>
              <el-icon @click="searchTeachPlanTemp"><Search /></el-icon>
            </template>
          </el-input> -->

          
          <!-- <a-select
              v-model:value="courseValue"
              style="width: 100px;min-height: 32px;"
              placeholder="选择课程"
              :options="courseOptions"
              :filter-option="filterOption"
              @focus="handleFocus"
              @blur="handleBlur"
              @change="handleChange"
          ></a-select> -->

          <a-dropdown trigger="click" placement="bottom">
            <a-button 
              style="  
              border-radius: 20px;
              display: flex;
              align-items: center;
              color:rgba(102, 102, 102, 1);"
            >
              选择课程
              <CaretDownOutlined style="color:rgba(102, 102, 102, 1);"/>
            </a-button>
            <template #overlay>
              <a-menu  class="menu">
                <a-input v-model:value="searchQuery_2" placeholder="搜索" style="margin-bottom: 10px;">
                  <template #suffix>
                    <SearchOutlined style="color:#C4C4C4"/>
                  </template>
                </a-input>

                <!-- 全选复选框（单独处理） -->
                <div style="display: flex;margin-left: 12px; margin-bottom: 10px;">
                  <a-checkbox
                    :checked="checkAll_2"
                    :indeterminate="indeterminate_2"
                    @change="onCheckAllChange_2"
                  >              
                  </a-checkbox>
                  <div style="flex:1; display: flex; justify-content: space-between;margin-left: 8px;">
                    <span>全选 </span>
                  </div>
                </div>


                <!-- 子项复选框组 -->
                <div class="checkbox-group-wrapper">
                  <a-checkbox-group :value="checkedList_2" @change="onGroupChange_2" >
                    <div style="display: flex; flex-direction: column; gap: 10px; width: 100%;">
                      <div style="display: flex; " v-for="item in filteredItems_2" :key="item.value" >
                        <a-checkbox :value="item.value" />
                        <div style="flex:1; display: flex; justify-content: space-between; align-items: center; margin-left: 8px; ">
                          <span>{{ item.label }}</span>
                        </div>
                      </div>
                    </div>
                  </a-checkbox-group>
                </div>

              </a-menu>
            </template>
          </a-dropdown>

          <a-input v-model:value="searchValue" style="border-radius: 94px;width: 302px;min-height: 32px;" placeholder="输入名称查询" @pressEnter="getTeachPlanTempList">
            <template #suffix>
              <SearchOutlined style="color:#C4C4C4" @click="getTeachPlanTempList"/>
            </template>
          </a-input>

        </div>
      </div>
      <a-spin :spinning="spinning">
        <div class="table">
          <div class="table-head height" style="font-weight:600">
            <div class="item" style="max-width: 56px;">
              <a-checkbox v-model:checked="state.checkAll" :indeterminate="state.indeterminate"
                @change="onCheckAllChange" />
            </div>
            <div class="item" style="width: 400px;">模板名称</div>
            <div class="item" style="width: 130px;">创建者</div>
            <div class="item" style="width: 140px;">更新时间</div>
            <div class="item" style="max-width: 118px;">操作</div>
          </div>

          <div class="center" v-if="files?.length === 0">
            <img src="@/assets/image/zanwu/nodata.png" style="width: 385px;height: 379px;" />
          </div>
          <div v-else>
            <div class="table-head height1" v-for="(item, index) in files" :key="item.id">
              <div class="item" style="max-width: 58px;">
                <a-checkbox :checked="selectedIds.includes(item.id)"
                  @change="(e: any) => handleCheckboxChange(item.id, e.target.checked)" />
              </div>
              <div class="item file-name titlew">
                <div class="file-name-text">
                  <a-tooltip v-if="editingIndex !== index" :title="item.title" placement="bottom">
                    <span style="cursor: pointer;" @click="handleEditTeachPlanTemp(item)">{{ item.title }}</span>
                  </a-tooltip>

                  <a-input v-else v-model:value="teachPlanTitle" @change="handleChange" @blur="editingIndex = null" ref="inputRef" class="topTitle-input" style="max-width: 300px;" >
                    <template #suffix>
                      <CheckCircleOutlined  @mousedown.prevent style="color:#C4C4C4;margin-left:10px;" @click="saveTitle(item.id)"/>
                      <CloseCircleOutlined  @mousedown.prevent style="color:#C4C4C4;margin-left:5px;" @click="cancleSaveTitle"/>
                    </template> 
                  </a-input>

                </div>
                <span class="edit-icon">
                  <a-tooltip placement="bottom">
                    <template #title>
                      <span>编辑</span>
                    </template>
                    <img style="cursor: pointer;width: 14px;height: 14px;" src="@/assets/image/img/edittable.png" @click="startEditing(index,item.title)" />
                  </a-tooltip>
                </span>
              </div>
              <div class="item" style="width: 130px;">
                {{ item.author_first_name || '无' }}
              </div>
              <div class="item" style="width: 140px;">
                {{ formatDate(item.updated_at) }}
              </div>
              <div class="item" style="max-width: 118px;display: flex;align-items: center;">
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>下载</span>
                  </template>
                  <DownloadOutlined style="color: #3F8CFF;font-size: 14px;" @click="downloadByIds([item.id])" />
                </a-tooltip>
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>删除</span>
                  </template>
                  <img style="cursor: pointer;margin-left: 16px;" src="@/assets/image/delete.svg" @click="delMessage(item.id)" />
                </a-tooltip>
              </div>
            </div>
          </div>

          <div class="pagination">
            <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
              layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </div>
      </a-spin>

      <Preview v-model:show="showPreDialog" :item="previewItem"/>
  </div>
</template>

<script lang="ts" setup>
import { formatDate } from '@/utils/util'
import { DeleteOutlined, DownloadOutlined,CaretDownOutlined,SearchOutlined,CheckCircleOutlined,CloseCircleOutlined} from '@ant-design/icons-vue';
//引入接口
import { teachPlanTempList, teachPlanTemp_softDelete, teachPlanTempUpload, search_temp, associateCourse, editTeachPlanTemp} from '@/services/api/LessonPlan'
import { usePagination } from '@/hooks/usePagination'; //解构分页参数
import { ref, computed, markRaw, onMounted, reactive, watch, h , createVNode} from 'vue'
import { Search, WarningFilled, } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { message, Modal} from 'ant-design-vue';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import type { UploadProps } from 'ant-design-vue';
import ChapterSelector from '@/components/lessonPlan/ChapterSelector.vue'
import { courseOwner } from '@/services/api/course';
import { downloadWord } from '@/utils/downloadFile'
import type { MenuProps } from 'ant-design-vue';

const router = useRouter()

const fileList = ref<UploadProps['fileList']>([]);
const uploadloading = ref(false)
const beforeUpload: UploadProps['beforeUpload'] = file => {
  if (fileSizeCheck(file, 10 * 1024 * 1024)) {
    message.error('文件大小不能超过10M');
    return
  }
  uploadloading.value = true
  fileList.value = [...(fileList.value || []), file];
  console.log(file,'file')
  const params = new FormData();
  params.append('file', file);
  params.append('title', fileList.value[0].name);
  teachPlanTempUpload(params).then(res => {
    message.success('上传成功');
    uploadloading.value = false
    getTeachPlanTempList()
  }).catch(err => {
    message.error('上传失败');
    uploadloading.value = false
  })
  return false;
};


const handleAddTeachPlanTemp = () => {
  router.push('/lessonPlan/createTeachPlanTemp')
}

//当前页所有id
const currentPageIds = computed(() => files.value?.map(item => item.id) || []);
// 已选中的id
const selectedIds = ref<number[]>([]);
// 处理单个复选框的选择变化
const handleCheckboxChange = (id: number, checked: boolean) => {
  if (checked) {
    selectedIds.value.push(id);
  } else {
    selectedIds.value = selectedIds.value.filter((item) => item !== id);
  }
};
// 全选操作
const onCheckAllChange = (e: any) => {
  const isChecked = e.target.checked;
  if (isChecked) {
    selectedIds.value = [...new Set([...selectedIds.value, ...currentPageIds.value])]; // 合并去重
  } else {
    selectedIds.value = selectedIds.value.filter(id => !currentPageIds.value.includes(id));
  }
  state.indeterminate = false;
};
const state = reactive({
  indeterminate: false,
  checkAll: false,
  checkedList: [],
});

//定义列表数据结构
interface fileList {
  id: number;
  title: string;
  author_name: string;
  updated_at: string;
  author_first_name: string;
}
// 模拟数据
const files = ref<fileList[]>()
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getTeachPlanTempList);
const spinning = ref(false)


const courseOptions = ref([{
    value: 0,
    label: '未关联课程'
}])

const filteredItems_1 = ref(); // 存储过滤后的结果
const filteredItems_2 = ref(); // 存储过滤后的结果

function getcourseOwner() {
    // const data = [{ id: 1, title: '建筑学' }]
    courseOwner().then((res:any) => {
      // console.log(res,"datadatadata")
        courseOptions.value = []
        res.data.forEach((item: any) => {
            courseOptions.value.push({
                value: item.id,
                label: item.title
            })
        })

        filteredItems_1.value = courseOptions.value
        filteredItems_2.value = courseOptions.value
    })
}

const searchValue = ref("")

// 搜索教案
// async function searchTeachPlanTemp () {
//   const params = {
//       title: searchValue.value,
//       is_deleted: 'false',
//       username: searchValue.value,
//   }
//   try {
//     spinning.value = true
//     const res = await search_temp(params)
//     files.value = res.data.results
//     total.value = res.data.count
//     spinning.value = false

//   } catch (error) {
//     console.error('获取教案列表失败:', error)
//   }
// }

onMounted(() => {
  getTeachPlanTempList() //  获取教案列表
  getcourseOwner() // 获取课程列表
})

// 获取教案模板列表
async function getTeachPlanTempList () {
  console.log('checkedList_2.value',checkedList_2.value)
  const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      is_deleted:'false',
      course_id: checkedList_2.value.join(),
      search: searchValue.value,
  }
  try {
    spinning.value = true
    const res = await teachPlanTempList(params)
    files.value = res.data.results
    console.log("files.value",files.value)
    total.value = res.data.count
    spinning.value = false

  } catch (error) {
    console.error('获取教案模板列表失败:', error)
  }
}

// 教案模板关联课程
async function relationCourse() {
  console.log('checkedList_1!',state.checkedList,checkedList_1.value)
  if(selectedIds.value.length === 0){
    message.error(`请选择要关联的教案模板`);
  }else if(checkedList_1.value.length === 0){
    message.error(`请选择要关联的课程`);
  }else{
    const params = {
      teachplan_ids: selectedIds.value,  //教案模板id
      course_id:  checkedList_1.value  //课程id
    }
    associateCourse(params).then(res => {
        message.success('关联成功')
        getTeachPlanTempList()
        dialogVisible.value = false
        checkedList_1.value = []
    })        
    .catch(error => {
        message.error(`关联失败: ${error.message || '未知错误'}`);
    });
  }
}

const handleBack = () => {
  //需要动态配置课程id
  // router.push('/LessonPlan')
  router.back()
}

//删除
const delMessage = async (params: any) => {
  const ids = Array.isArray(params) ? params : [params]
  if(ids.length == 0){
    message.error('请选择要删除的记录');
    return
  } 
  const confirmed = await showDeleteConfirm('确定要删除这条记录吗？');
  if (confirmed) {
    softDelete(ids)
  }
  console.log(confirmed);
}
async function softDelete(id: number[]) {
  const params = {
    "ids": id,  //传单个及单个删除
    "action": "delete"  //restore:恢复，delete：软删除
  }
  try {
    const res = await teachPlanTemp_softDelete(params)
    getTeachPlanTempList()
  } catch (error) {
    message.error('删除失败')
  }
}

function teachPlanTempRecycle() {
  router.push('/LessonPlan/teachPlanTempRecycle')
};

async function downloadByIds(ids: any[]) {
  let newFiles:any = files.value?.filter(item => ids.includes(item.id))
  newFiles.forEach((obj: any) => {
    if(obj.file == null){
      downloadWord(obj.content, obj.title + '.doc');
    }else{
      window.open(obj.file, '_blank');
    }
  })
}

// async function downloadTeachPlanTemp(item: any) {
//   console.log(item);
//   if(item.file == null){
//     downloadWord(item.content, item.title + '.doc');
//   }else{
//     window.open(item.file, '_blank');
//   }
// }

const showPreDialog = ref(false)
const previewItem = ref();

const handleEditTeachPlanTemp = (item: any) => {
  console.log(item.file);
  if(item.file != null){
    item.file_type = 'word'
    previewItem.value = item
    showPreDialog.value = true;
    return
  }
    else{
        router.push({
            path:'/LessonPlan/teachPlanEditor',
            state: {
                id: item.id,
                title: item.title,
                content: item.content,
                mode:'temp'
            }
        })
    }
}

// const treeData = [
//   {
//     id: 1,
//     label: '课程XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
//     children: [
//       { id: 11, label: '章节1XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' },
//       { id: 12, label: '章节2XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' }
//     ]
//   },
//   {
//     id: 2,
//     label: '课程XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
//     children: [
//       { id: 21, label: '章节1XXXXXXXXXXXXXXXXXXXXXX' },
//       { id: 22, label: '章节2XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' }
//     ]
//   }
// ]

// const chapterSelectorRef = ref(null)

// const checkedKeys = ref<Array<number | string>>([])

// const handleCheckedUpdate = (val: Array<number | string>) => {
//   checkedKeys.value = val
// }

const searchQuery_1 = ref(""); // 搜索框的输入内容
// 监听 searchQuery 的变化
watch(searchQuery_1, (newQuery) => {
  filteredItems_1.value = courseOptions.value.filter(item => 
    item.label.toLowerCase().includes(newQuery.toLowerCase())
  );
});

// 关联课程弹窗
const dialogVisible = ref(false)

const checkedList_1 = ref<any[]>([])

const checkAll_1 = computed(() => checkedList_1.value.length === courseOptions.value.length && courseOptions.value.length != 0)

const indeterminate_1 = computed(
  () => checkedList_1.value.length > 0 && checkedList_1.value.length < courseOptions.value.length
)

const onGroupChange_1 = (list: any) => {
  checkedList_1.value = list
    console.log(list,checkedList_1.value,'list')
}

const onCheckAllChange_1 = (e: any) => {
  checkedList_1.value = e.target.checked ? courseOptions.value.map(item => item.value) : []
}

const searchQuery_2 = ref(""); // 搜索框的输入内容
// 监听 searchQuery 的变化
watch(searchQuery_2, (newQuery) => {
  filteredItems_2.value = courseOptions.value.filter(item => 
    item.label.toLowerCase().includes(newQuery.toLowerCase())
  );
});


// 下拉菜单
const checkedList_2 = ref<any[]>([])

const checkAll_2 = computed(() => checkedList_2.value.length === courseOptions.value.length && courseOptions.value.length != 0)

const indeterminate_2 = computed(
  () => checkedList_2.value.length > 0 && checkedList_2.value.length < courseOptions.value.length
)

const onGroupChange_2 = (list: any) => {
  checkedList_2.value = list
  console.log(list,checkedList_2.value,'list')
  getTeachPlanTempList()
}

const onCheckAllChange_2 = (e: any) => {
  checkedList_2.value = e.target.checked ? courseOptions.value.map(item => item.value) : []
  getTeachPlanTempList()
}

const editingIndex = ref(null)
const title = ref('这是一个可编辑标题')
const teachPlanTitle = ref('')
const inputRef = ref(null)
const MAX_LENGTH = 50;

const handleChange = () => {
  if (teachPlanTitle.value.length > MAX_LENGTH) {
    message.error(`输入不能超过 ${MAX_LENGTH} 个字符`);
    // 截断超长字符（防止后续逻辑异常）
    teachPlanTitle.value = teachPlanTitle.value.slice(0, MAX_LENGTH);
  }
};

function startEditing(index: any, itemTitle: any) {
  editingIndex.value = index
  title.value = itemTitle
  teachPlanTitle.value = itemTitle
  // 等 DOM 渲染完成后聚焦
  // nextTick(() => {
  //   inputRef.value?.focus()
  // })
}

function saveTitle(tempId:any) {
  title.value = teachPlanTitle.value.trim() || title.value
  editingIndex.value = null
  saveTempTitle(tempId).then(() => {
    message.success('标题保存成功！')
    getTeachPlanTempList()
  }).catch(() => {
    message.error('标题保存失败！')
  })
}

const saveTempTitle = async(tempId:any) => {
  const params = {
      title: title.value , // 教案模板标题
      // course_id:  checkedList_1.value //课程id
  }
  try {
    spinning.value = true
    const res = await editTeachPlanTemp(tempId, params)
    console.log(res);
    spinning.value = false //加载
    // message.success('保存成功！')//保存成功提示
  } catch (error) {
    // message.error('保存失败')
  }
}

function cancleSaveTitle() {
  teachPlanTitle.value = title.value
  editingIndex.value = null
}



</script>

<style scoped lang="scss">

:deep(.ant-select-selector){
    border-radius: 20px;
}

.menu{
  width: 185px;
  height: 248px;
  padding: 10px;
  opacity: 1;
  border-radius: 10px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.1);
}

.checkbox-group-wrapper{
  width: 100%;
  padding-left: 34px;
  max-height: 150px;
  overflow: auto;
}

.checkbox-group-wrapper::-webkit-scrollbar {
  width: 4px; /* 滚动条宽度 */
}

.checkbox-group-wrapper::-webkit-scrollbar-thumb {
  background-color: #C7DDFC; /* 滑块颜色 */
  border-radius: 136px; /* 圆角 */
}

.checkbox-group-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #C7DDFC; /* 滑块 hover 时颜色 */
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0 40px;

  .back-btn {
    cursor: pointer;
    width: 60px;
    margin-top: 50px;
    color: #333;
    display: flex;
    align-items: center;
  }
  
}

.text {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 12px;
  margin: 20px 0 20px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  min-width: 900px;
  flex-wrap: wrap;

  .toolbar-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 36px;

    .addbut {
      width: 82px;
      color: #fff;
      background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
    }

    .but{
      display: flex;
      justify-content: center;
      align-items: center;
    }

  }

  .toolbar-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
}

:deep(.el-select__wrapper) {
  border-radius: 95px;
}


:deep(.el-input__wrapper) {
  border-radius: 94px;
}


.table {
  background: #fff;
  border-radius: 4.79px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 0 20px 20px;
  min-width: 900px;
  width: 100%;

  .table-head {
    // display: flex;
    align-items: center;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    /* 禁止换行 */
    overflow-x: auto;
    /* 允许水平滚动（如果需要） */
    font-size: 14px;
    padding-left: 12px;

    .item {
      flex: 1 0 auto;
      white-space: nowrap;
      /* 防止文字换行 */
    }

    .flex {
      display: flex;
      align-items: center;
    }

    .titlew {
      width: 400px;
      position: relative;
      height: 56px;
      line-height: 56px;
    }

    .topTitle-input{
      height: 32px;
      padding-left:10px;

      border-radius: 5px;
      border: 1px solid #3F8CFF;
      box-shadow: 0px 1px 4px  #3F8CFF;

      color: #333;
      // width: 300px;
      white-space: nowrap;      /* 不换行 */
      overflow-x: auto;         /* 超出时显示横向滚动条 */  
    }

    .file-name-text {
      max-width: 330px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden
    }

    .file-name .edit-icon {
      opacity: 0;
      transition: opacity 0.3s ease;
      position: absolute;
      left: 345px;
      top: 50%;
      transform: translateY(-50%);
    }

    .file-name:hover .edit-icon {
      opacity: 1;
    }
  }

  .height {
    height: 56px;
    border-bottom: 1px solid #E5E5E5;
  }

  .height1 {
    height: 58px;
    border-bottom: 1px solid #E5E5E5;
    color: #666666;

    &:hover {
      background: rgba(63, 140, 255, 0.05);
    }
  }
}

.pagination {
  margin: 20px 0 12px 0;
  text-align: center;
}

:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}
</style>
