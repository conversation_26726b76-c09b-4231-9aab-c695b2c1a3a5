<template>
  <div class="page">

      <back @click="handback"></back>
      <h1 class="text">
        <span style="color: rgba(153, 153, 153, 1);cursor: pointer;" @click="handback">模板管理 </span>
        <span>/ 回收站</span>
      </h1>

      <div class="toolbar">
        <div class="toolbar-left">
          <div style="display: flex;">
            <a-button type="primary" ghost class="but" style="width: 80px;height: 30px;" @click="restoreTeachPlanTemp(selectedIds)"
              ><img style="margin-right: 8px;" src="@/assets/image/restore.svg">还原</a-button>
            <a-button type="primary" class="but" style="width: 80px;height: 30px;margin-left: 10px;" @click="delMessage(selectedIds)" ghost
              :icon="h(DeleteOutlined)">删除</a-button>
          </div>
        </div>
        <div class="toolbar-right">
          <el-input v-model="searchValue" style="width: 302px;min-height: 32px;" placeholder="请输入"
            @keyup.enter="searchTeachPlanTemp">
            <template #suffix>
              <el-icon @click="searchTeachPlanTemp">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <a-spin :spinning="spinning">
        <div class="table">
          <div class="table-head height" style="font-weight:600">
            <div class="item" style="max-width: 56px;">
              <a-checkbox v-model:checked="state.checkAll" :indeterminate="state.indeterminate"
                @change="onCheckAllChange" />
            </div>
            <div class="item" style="width: 400px;">文件名</div>
            <div class="item" style="width: 130px;">创建者</div>
            <div class="item" style="width: 140px;">更新时间</div>
            <div class="item" style="max-width: 118px;">操作</div>
          </div>

          <div class="center" v-if="files?.length === 0">
            <img src="@/assets/image/zanwu/nodata.png" style="width: 385px;height: 379px;" />
          </div>
          <div v-else>
            <div class="table-head height1" v-for="item in files" :key="item.id">
              <div class="item" style="max-width: 58px;">
                <a-checkbox :checked="selectedIds.includes(item.id)"
                  @change="(e: any) => handleCheckboxChange(item.id, e.target.checked)" />
              </div>
              <div class="item file-name titlew">
                <div style="" class="file-name-text">
                  {{ item.title }}
                </div>
                <span class="edit-icon" style="">
                  <a-tooltip placement="bottom">
                    <template #title>
                      <span>编辑</span>
                    </template>
                    <img style="cursor: pointer;width: 14px;height: 14px;" src="@/assets/image/img/edittable.png" />
                  </a-tooltip>
                </span>
              </div>
              <div class="item" style="width: 130px;">
                {{ item.author_first_name || '无' }}
              </div>
              <div class="item" style="width: 140px;">
                {{ formatDate(item.updated_at) }}
              </div>
              <div class="item" style="max-width: 118px;display: flex;align-items: center;">
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>还原</span>
                  </template>
                  <UndoOutlined style="color: #3F8CFF;font-size: 14px;" @click="restoreTeachPlanTemp(item.id)" />
                </a-tooltip>
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>删除</span>
                  </template>
                  <img style="cursor: pointer;margin-left: 16px;" src="@/assets/image/delete.svg" @click="delMessage(item.id)" />
                </a-tooltip>
              </div>
            </div>
          </div>

          <div class="pagination">
            <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
              layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </div>
      </a-spin>
      
  </div>
</template>

<script lang="ts" setup>
import { formatDate } from '@/utils/util'
import Back from '@/components/ui/back.vue'
import { DeleteOutlined, DownloadOutlined, UndoOutlined } from '@ant-design/icons-vue';
import { ref, markRaw, onMounted, computed, reactive, h } from 'vue'
import { ElMessageBox } from 'element-plus'
import { Search, WarningFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { teachPlanTempList, teachPlanTemp_hardDelete, teachPlanTemp_softDelete, search_temp} from '@/services/api/LessonPlan'
import { usePagination } from '@/hooks/usePagination'; //解构分页参数

import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import { message} from 'ant-design-vue';

const router = useRouter()
const searchValue = ref('')

// 搜索教案
async function searchTeachPlanTemp () {
  const params = {
      title: searchValue.value,
      is_deleted: 'true',
  }
  try {
    spinning.value = true
    const res = await search_temp(params)
    files.value = res.data.results
    total.value = res.data.count
    spinning.value = false

  } catch (error) {
    console.error('获取教案列表失败:', error)
  }
}

//定义列表数据结构
interface fileList {
  id: number;
  title: string;
  author: string;
  updated_at: string;
  author_first_name: string;
}
// 模拟数据
const files = ref<fileList[]>()
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getTeachPlanTempList);
const spinning = ref(false)

onMounted(() => {
  getTeachPlanTempList()
})

//当前页所有id
const currentPageIds = computed(() => files.value?.map(item => item.id) || []);
// 已选中的id
const selectedIds = ref<number[]>([]);
// 处理单个复选框的选择变化
const handleCheckboxChange = (id: number, checked: boolean) => {
  if (checked) {
    selectedIds.value.push(id);
  } else {
    selectedIds.value = selectedIds.value.filter((item) => item !== id);
  }
};
// 全选操作
const onCheckAllChange = (e: any) => {
  const isChecked = e.target.checked;
  if (isChecked) {
    selectedIds.value = [...new Set([...selectedIds.value, ...currentPageIds.value])]; // 合并去重
  } else {
    selectedIds.value = selectedIds.value.filter(id => !currentPageIds.value.includes(id));
  }
  state.indeterminate = false;
};
const state = reactive({
  indeterminate: false,
  checkAll: false,
  checkedList: [],
});

// 获取教案列表
async function getTeachPlanTempList() {
  const params = {
    page: currentPage.value,
    pageSize: pageSize.value,
    is_deleted: 'true',
    title: searchValue.value
  }
  try {
    spinning.value = true
    const res = await teachPlanTempList(params)
    files.value = res.data.results
    total.value = res.data.count
    spinning.value = false
  } catch (error) {
    console.error('获取教案回收站列表失败:', error)
  }
}


interface UserRow {
  id: number
}


const handleSelectionChange = (selection: UserRow[]) => {
  selectedIds.value = selection.map(item => item.id)
  console.log('选中的ID数组：', selectedIds.value)
}


const delMessage = async (params: any) => {
  const ids = Array.isArray(params) ? params : [params]
  if(ids.length == 0){
    message.error('请选择要删除的记录');
    return
  } 
  const confirmed = await showDeleteConfirm('确定删除本记录吗？删除后无法恢复！');
  if (confirmed) {
    hardDelete(ids)
  }
  console.log(confirmed);
}

//回收站应删除
async function hardDelete(id: number[]) {
  const params = {
    "ids": id,  //传单个及单个删除
    "action": "delete"  //restore:恢复，delete：软删除
  }
  try {
    const res = await teachPlanTemp_hardDelete(params)
    // console.log(res,'res')
    // if(){}
    getTeachPlanTempList()
  } catch (error) {
    message.error('删除失败')
  }
}

function handback() {
  router.back()
}

async function restoreTeachPlanTemp(data: any) {
  const ids = Array.isArray(data) ? data : [data]
  const params = {
    "ids": ids,  //传单个及单个删除
    "action": "restore"  //restore:恢复，delete：软删除
  }
  try {
    const res = await teachPlanTemp_softDelete (params)
    getTeachPlanTempList()
  } catch (error) {
    message.error('删除失败')
  }
}

</script>

<style scoped lang="scss">
:deep(.el-input__suffix-inner) {
    cursor: pointer;
}
:deep(.el-select__wrapper) {
  border-radius: 95px;
}


:deep(.el-input__wrapper) {
  border-radius: 94px;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0 40px;
}

.text {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 12px;
  margin: 20px 0;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  min-width: 900px;
  flex-wrap: wrap;
  
  .toolbar-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 36px;

    .addbut {
      width: 82px;
      color: #fff;
      background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
    }

    .but{
      display: flex;
      justify-content: center;
      align-items: center;
    }

  }

  .toolbar-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
}

.table {
  background: #fff;
  border-radius: 4.79px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 0 20px 20px;
  min-width: 900px;
  width: 100%;

  .table-head {
    // display: flex;
    align-items: center;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    /* 禁止换行 */
    overflow-x: auto;
    /* 允许水平滚动（如果需要） */
    font-size: 14px;
    padding-left: 12px;

    .item {
      flex: 1 0 auto;
      white-space: nowrap;
      /* 防止文字换行 */
    }

    .flex {
      display: flex;
      align-items: center;
    }

    .titlew {
      width: 400px;
      position: relative;
      height: 56px;
      line-height: 56px;
    }

    .file-name-text {
      max-width: 330px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden
    }

    .file-name .edit-icon {
      opacity: 0;
      transition: opacity 0.3s ease;
      position: absolute;
      left: 345px;
      top: 50%;
      transform: translateY(-50%);
    }

    .file-name:hover .edit-icon {
      opacity: 1;
    }
  }

  .height {
    height: 56px;
    border-bottom: 1px solid #E5E5E5;
  }

  .height1 {
    height: 58px;
    border-bottom: 1px solid #E5E5E5;
    color: #666666;

    &:hover {
      background: rgba(63, 140, 255, 0.05);
    }
  }
}

.pagination {
  margin: 20px 0 12px 0;
  text-align: center;
}

:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}
</style>
