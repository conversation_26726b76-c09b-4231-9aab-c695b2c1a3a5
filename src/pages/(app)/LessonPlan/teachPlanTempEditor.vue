<template>
  <a-spin :spinning="spinning">
    <div class="page">
      <!-- 顶部返回 -->
      <div class="page-header">
        <div class="left">
          <div @click="handleBack" class="back-btn">
            <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg"/>
            <span>返回</span>
          </div>
          <a-tooltip :title="title" placement="bottom">
            <div v-show="!editing" @click="startEditing" class="topTitle">{{ title }}</div>
          </a-tooltip>
          <a-input v-show="editing" v-model:value="teachPlanTitle" @change="handleChange"  @blur="editing=false" ref="inputRef" class="topTitle-input" style="max-width: 300px;" >
            <template #suffix>
              <CheckCircleOutlined  @mousedown.prevent style="color:#C4C4C4;margin-left:10px;" @click="saveTitle"/>
              <CloseCircleOutlined  @mousedown.prevent style="color:#C4C4C4;margin-left:5px;" @click="cancleSaveTitle"/>
            </template>
          </a-input>


          <div class="date">已保存于 {{ currentTime }}</div>
        </div>
        <div class="right">
          <button class="btn1" @click="dialogVisible = true">保存加关联</button>
          <button class="btn2" @click="afterSelect()">下一步-输入描述</button>
        </div>
      </div>


        <div class="page-content">
          <!-- 教案编辑区 -->
          <div class="editor">
            <div v-if="nodata" class="no-data">
              <img src="@/assets/image/lessonPlanSquare/nodata.png"/>
              <span>请选择右侧对应模板</span>
            </div>
            <div v-else class="editor-html" v-html="parseMarkdown(content)"></div>
          </div>
          <!-- 模板选择区 -->
          <div class="template-panel">
            <div class="text">
              <span>选择模板</span>
            </div>
            <div class="toolbar" v-if="toolbarShow">
              <button class="toolbar-button" @click="getListALL">
                全部
                <CaretDownOutlined />
              </button>

              <a-dropdown trigger="click" placement="bottom">
                <button class="toolbar-button">
                  选择课程
                  <CaretDownOutlined />
                </button>
                <template #overlay>
                  <a-menu class="menu">
                    <a-input v-model:value="searchQuery_2" placeholder="搜索" style="margin-bottom: 10px;">
                      <template #suffix>
                        <SearchOutlined style="color:#C4C4C4"/>
                      </template>
                    </a-input>

                    <!-- 全选复选框（单独处理） -->
                    <div style="display: flex;margin-left: 12px; margin-bottom: 10px;">
                      <a-checkbox
                        :checked="checkAll_2"
                        :indeterminate="indeterminate_2"
                        @change="onCheckAllChange_2"
                      >
                      </a-checkbox>
                      <div style="flex:1; display: flex; justify-content: space-between;margin-left: 8px;">
                        <span>全选 </span>
                      </div>
                    </div>

                    <!-- 子项复选框组 -->
                    <div class="checkbox-group-wrapper">
                      <a-checkbox-group :value="checkedList_2" @change="onGroupChange_2">
                        <div style="display: flex; flex-direction: column; gap: 10px; width: 100%;">
                          <div style="display: flex; " v-for="item in filteredItems_2" :key="item.value" >
                            <a-checkbox :value="item.value" />
                            <div style="flex:1; display: flex; justify-content: space-between; align-items: center; margin-left: 8px; ">
                              <span>{{ item.label }}</span>
                            </div>
                          </div>
                        </div>
                      </a-checkbox-group>
                    </div>

                  </a-menu>
                </template>
              </a-dropdown>

              <button class="toolbar-button">系统内置</button>

            </div>
            <div class="template-grid">
              <div
                v-for="(template, index) in templateItems"
                :key="index"
                :class="['template-item', selectedIndex === index ? 'selected' : '']"
                @click="selectTemplate(index)"
              >
                <div class="template-index" :class="{ active: selectedIndex === index }">
                  {{ (index + 1).toString().padStart(2, '0') }}
                </div>
                <div class="template-card">
                  <div style="width: 100%;height: 100%;background: rgba(255, 255, 255, 1);padding: 15px;overflow: hidden;">
                    <div class="card-html">
                      <a-tooltip  :title="template.title" placement="bottom">
                        <h3 class="card-content">标题：{{ template.title ?? '暂无' }}</h3>
                      </a-tooltip>
                      <a-tooltip  :title="template.description" placement="bottom">
                        <p class="card-content">描述：{{ template.description ?? '暂无' }}</p>
                      </a-tooltip>
                      <a-tooltip  :title="template.template_type" placement="bottom">
                        <p class="card-content">模板类型：{{ template.template_type ?? '暂无' }}</p>
                      </a-tooltip>
                      <a-tooltip  :title="template.subject" placement="bottom">
                        <p class="card-content">适用学科：{{ template.subject ?? '暂无' }}</p>
                      </a-tooltip>
                      <a-tooltip  :title="template.difficulty" placement="bottom">
                        <p class="card-content">适用难度：{{ template.difficulty ?? '暂无' }}</p>
                      </a-tooltip>
                    </div>
                    <!-- <div class="card-html" v-html="parseMarkdown(
                      `- 标题：${template.title ?? '暂无'}\n- 描述：${template.description ?? '暂无'}\n- 模板类型：${template.template_type ?? '暂无'}\n- 适用学科：${template.subject ?? '暂无'}\n- 适用难度：${template.difficulty ?? '暂无'}`
                    ) "></div> -->

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>

    <a-modal v-model:open="dialogVisible" title="关联课程" :footer="null" width="700px">
      <a-input v-model:value="searchQuery_1" placeholder="搜索" style="margin-bottom: 10px;">
        <template #suffix>
          <SearchOutlined style="color:#C4C4C4"/>
        </template>
      </a-input>
      <!-- 全选复选框（单独处理） -->
      <div style="display: flex;margin-left: 12px; margin-bottom: 10px;">
        <a-checkbox
          :checked="checkAll_1"
          :indeterminate="indeterminate_1"
          @change="onCheckAllChange_1"
        >
        </a-checkbox>
        <div style="flex:1; display: flex; justify-content: space-between;margin-left: 8px;">
          <span>全选 </span>
        </div>
      </div>

      <!-- 子项复选框组 -->
      <div class="checkbox-group-wrapper" style="max-height: 300px;">
        <a-checkbox-group :value="checkedList_1" @change="onGroupChange_1">
          <div style="display: flex; flex-direction: column; gap: 10px; width: 100%;">
            <div style="display: flex; " v-for="item in filteredItems_1" :key="item.value" >
              <a-checkbox :value="item.value" />
              <div style="flex:1; display: flex; justify-content: space-between; align-items: center; margin-left: 8px; ">
                <span>{{ item.label }}</span>
              </div>
            </div>
          </div>
        </a-checkbox-group>
      </div>

    <div style="display: flex; justify-content: flex-end; align-items: center;margin-top: 10px;">
        <a-space>
            <a-button @click="dialogVisible = false">取消</a-button>
            <a-button type="primary" html-type="submit" @click="relationCourse">确定</a-button>
        </a-space>
    </div>
    </a-modal>
  </a-spin>
</template>

<script lang="ts" setup>
import { ref,onMounted,nextTick} from 'vue'
import { useRouter } from 'vue-router'
import { teachPlanTempList, saveTeachPlanTemp,associateCourse,editTeachPlanTemp } from '@/services/api/LessonPlan'
import { parseMarkdown } from "@/utils/markdownParser";
import { message } from 'ant-design-vue';
import { CaretDownOutlined,SearchOutlined,CheckCircleOutlined,CloseCircleOutlined } from "@ant-design/icons-vue"
import { courseOwner } from '@/services/api/course';

definePage({
  meta: {
    hideUserInfo: true
  }
})

const spinning = ref(false)

const router = useRouter()
const handleBack = () => {
  router.back()
}

const title = ref('这是一个可编辑标题')
const teachPlanTitle = ref('')
const editing = ref(false)
const inputRef = ref<HTMLInputElement | null>(null)
const nodata = ref(true)
const MAX_LENGTH = 50;

const handleChange = () => {
  if (teachPlanTitle.value.length > MAX_LENGTH) {
    message.error(`输入不能超过 ${MAX_LENGTH} 个字符`);
    // 截断超长字符（防止后续逻辑异常）
    teachPlanTitle.value = teachPlanTitle.value.slice(0, MAX_LENGTH);
  }
};

function startEditing() {
  teachPlanTitle.value = title.value
  editing.value = true
  // 等 DOM 渲染完成后聚焦
  nextTick(() => {
    inputRef.value?.focus()
  })
}

function saveTitle() {
  title.value = teachPlanTitle.value.trim() || title.value
  editing.value = false
  saveTempTitle().then(() => {
    message.success('标题保存成功！')
    getTeachPlanTempList().then(() => {
      selectTemplate(selectedIndex.value)
    })
  }).catch(() => {
    message.error('标题保存失败！')
  })

}

function cancleSaveTitle() {
  teachPlanTitle.value = title.value
  editing.value = false
}

const currentTime = ref('')

// 格式化当前时间为 YYYY-MM-DD HH:mm:ss
function formatTime(date: Date): string {
  const Y = date.getFullYear()
  const M = String(date.getMonth() + 1).padStart(2, '0')
  const D = String(date.getDate()).padStart(2, '0')
  const h = String(date.getHours()).padStart(2, '0')
  const m = String(date.getMinutes()).padStart(2, '0')
  const s = String(date.getSeconds()).padStart(2, '0')
  return `${Y}-${M}-${D} ${h}:${m}:${s}`
}

const templateItems = ref<any>([])
const toolbarShow = ref(true)
const route = useRoute() //获取路由参数

onMounted(() => {
  getTeachPlanTempList() // 获取教案列表
  getcourseOwner() // 获取课程列表

  if(route.query.type === 'aiSpace'){
    toolbarShow.value = true// 个人空间进入展示按钮
  }else{
    toolbarShow.value = false// 课程进入不展示按钮
  }

})

// 获取全部教案列表
async function getTeachPlanTempList () {
  const params = {
    is_deleted:'false',
    course_id: checkedList_2.value.join()
  }
  try {
    spinning.value = true
    const res = await teachPlanTempList(params)
    console.log('获取模板列表:', res.data);
    templateItems.value = [];
    for(const item of res.data){
      templateItems.value.push(item)
    }

    // 移除空字符串和null值
    // templateItems.value = templateItems.value.filter(item => item !== "" && item !== null && item !== undefined)

    // content.value = templateItems.value[selectedIndex.value].content ?? '暂无'
    // const match = content.value.match(/^# (.+)$/m);
    // title.value = match ? match[1] : '无标题';

    spinning.value = false
  } catch (error) {
    console.error('获取模板列表失败:', error)
  }
}

const selectedIndex = ref()
const content = ref('')
const tempId = ref<number[]>([])
const relation = ref([])

function selectTemplate(index: number) {
  checkedList_1.value = []
  nodata.value = false
  selectedIndex.value = index
  content.value = templateItems.value[selectedIndex.value].content.replace(/^# (.+)$/m, '') ?? '暂无'
  // const match = content.value.match(/^# (.+)$/m);
  // title.value = match ? match[1] : '无标题';

  title.value = templateItems.value[selectedIndex.value].title ?? '暂无'

  tempId.value=[];
  tempId.value.push(templateItems.value[selectedIndex.value].id);

  // relation.value = templateItems.value[selectedIndex.value].courses;
  // console.log(relation.value,'relation.value')
  checkedList_1.value = templateItems.value[selectedIndex.value].courses

}

const saveTempTitle = async() => {

  const params = {
      title: title.value,
      // course_id:  checkedList_1.value //课程id
  }
  try {
    spinning.value = true
    const res = await editTeachPlanTemp(tempId.value, params)
    console.log(res);
    spinning.value = false //加载
    // message.success('保存成功！')//保存成功提示
    currentTime.value = formatTime(new Date())
  } catch (error) {
    // message.error('保存失败')
  }
}


const saveTemp = async() => {
  const params = {
      title: title.value,
      content: content.value.replace(/^# (.+)$/m, `# ${title.value}\n`),
  }
  try {
    spinning.value = true
    const res = await saveTeachPlanTemp(params)
    spinning.value = false
    message.success('保存成功！')
    currentTime.value = formatTime(new Date())
  } catch (error) {
    console.error('获取PPT列表失败:', error)
  }
}

const afterSelect = () => {
  if(!content.value){
    message.error('请选择模板！')
  }
  else if(route.query.type === 'aiSpace'){
    router.push({
      path:'/LessonPlan/afterSelectTemp',
      state: {
        content:  `# ${title.value}\n${content.value}`
      },
      query: { type: 'aiSpace' }
    })
  }else{
        router.push({
      path:'/LessonPlan/afterSelectTemp',
      state: {
        content:  `# ${title.value}\n${content.value}`
      }
    })
  }
}

const courseOptions = ref([{
    value: 0,
    label: '未关联课程'
}])
const filteredItems_1 = ref(); // 存储过滤后的结果
const filteredItems_2 = ref(); // 存储过滤后的结果

function getcourseOwner() {
    // const data = [{ id: 1, title: '建筑学' }]
    courseOwner().then((res:any) => {
      // console.log(res,"datadatadata")
        courseOptions.value = []
        res.data.forEach((item: any) => {
            courseOptions.value.push({
                value: item.id,
                label: item.title
            })
        })

        filteredItems_1.value = courseOptions.value
        filteredItems_2.value = courseOptions.value

    })
}

// 教案模板关联课程
async function relationCourse() {
  if(checkedList_1.value.length === 0){
    message.error(`请选择要关联的课程`);
  }else if(tempId.value.length === 0 ){
    message.error(`请选择要关联的模板`);
  }else{
    const params = {
      teachplan_ids: tempId.value,  //教案模板id
      course_id:  checkedList_1.value  //课程id
    }
    associateCourse(params).then(res => {
        message.success('关联成功')
        getTeachPlanTempList()
        dialogVisible.value = false
        // checkedList_1.value = []
    })
    .catch(error => {
        message.error(`关联失败: ${error.message || '未知错误'}`);
    });
  }
}

const searchQuery_1 = ref(""); // 搜索框的输入内容
// 监听 searchQuery 的变化
watch(searchQuery_1, (newQuery) => {
  filteredItems_1.value = courseOptions.value.filter(item =>
    item.label.toLowerCase().includes(newQuery.toLowerCase())
  );
});


// 关联课程弹窗
const dialogVisible = ref(false)

const checkedList_1 = ref<any[]>([])

const checkAll_1 = computed(() => checkedList_1.value.length === courseOptions.value.length && courseOptions.value.length != 0)

const indeterminate_1 = computed(
  () => checkedList_1.value.length > 0 && checkedList_1.value.length < courseOptions.value.length
)

const onGroupChange_1 = (list: any) => {
  checkedList_1.value = list
    console.log(list,checkedList_1.value,'list')
}

const onCheckAllChange_1 = (e: any) => {
  checkedList_1.value = e.target.checked ? courseOptions.value.map(item => item.value) : []
}

const searchQuery_2 = ref(""); // 搜索框的输入内容
// 监听 searchQuery 的变化
watch(searchQuery_2, (newQuery) => {
  filteredItems_2.value = courseOptions.value.filter(item =>
    item.label.toLowerCase().includes(newQuery.toLowerCase())
  );
});

// 下拉菜单
const checkedList_2 = ref<any[]>([])

const checkAll_2 = computed(() => checkedList_2.value.length === courseOptions.value.length && courseOptions.value.length != 0)

const indeterminate_2 = computed(
  () => checkedList_2.value.length > 0 && checkedList_2.value.length < courseOptions.value.length
)

const onGroupChange_2 = (list: any) => {
  checkedList_2.value = list
  console.log(list,checkedList_2.value,'list')
  getTeachPlanTempList()
}

const onCheckAllChange_2 = (e: any) => {
  checkedList_2.value = e.target.checked ? courseOptions.value.map(item => item.value) : []
  getTeachPlanTempList()
}

const getListALL = () => {
  checkedList_2.value = [];
  getTeachPlanTempList()
}

</script>

<style scoped lang="scss">

.menu{
  width: 185px;
  height: 248px;
  padding: 10px;
  opacity: 1;
  border-radius: 10px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.1);
}

.checkbox-group-wrapper{
  width: 100%;
  padding-left: 34px;
  max-height: 150px;
  overflow: auto;
}

.checkbox-group-wrapper::-webkit-scrollbar {
  width: 4px; /* 滚动条宽度 */
}

.checkbox-group-wrapper::-webkit-scrollbar-thumb {
  background-color: #C7DDFC; /* 滑块颜色 */
  border-radius: 136px; /* 圆角 */
}

.checkbox-group-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #C7DDFC; /* 滑块 hover 时颜色 */
}

.page {
  width: 100%;
  min-width: 800px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header{
    position: relative; /* 创建层叠上下文 */
    z-index: 1; /* 确保阴影在上层 */
    min-width: 800px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    box-shadow: 0px 2px 10px  rgba(67, 143, 254, 0.1);
    padding: 0 20px;

    .left {
      display: flex;
      align-items: center;
      // gap:20px
    }
    .right {
      display: flex;
      align-items: center;
      gap:10px
    }

    .back-btn {
      cursor: pointer;
      // margin-right: 20px;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .topTitle {
      margin-left:20px;
      color: #333;
      // width: 300px;
      font-size: 20px;
      font-weight: 700;
      max-width: 300px;
      white-space: nowrap;      /* 不换行 */
      overflow-x: hidden;         /* 超出时显示横向滚动条 */
    }

    .topTitle-input{
      padding-left:10px;
      margin-left:20px;

      border-radius: 5px;
      border: 1px solid #3F8CFF;
      box-shadow: 0px 1px 4px  #3F8CFF;

      color: #333;
      // width: 300px;
      font-size: 20px;
      font-weight: 700;
      white-space: nowrap;      /* 不换行 */
      overflow-x: auto;         /* 超出时显示横向滚动条 */
    }

    .date {
      margin-left:20px;
      color: gray;
    }

    .btn1{
      width: 95px;
      height: 32px;
      background: white;
      padding: 10px;
      border-radius: 130px;
      border: 1px solid rgba(229, 229, 229, 1);
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0px;
      line-height: 14px;
      color: rgba(102, 102, 102, 1);
    }

    .btn2{
      width: 143px;
      height: 32px;
      background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
      color: white;
      padding: 10px;
      border-radius: 100px;
      border: none;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      font-size: 14px;
      font-weight: 700;
      letter-spacing: 0px;
      line-height: 14px;
    }
  }

  .page-content {
    flex:1;
    height: 100%;
    min-width: 800px;
    background: url(@/assets/image/bg1.png) no-repeat center;
    background-size: cover;
    padding: 30px;
    display: flex;
    gap:20px;
    // align-items: center;
    justify-content: center;

    .editor {
      width: 900px;
      min-width: 600px;
      max-height: 900px;
      background: white;
      padding: 24px 80px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      overflow: auto;
      .editor-html{
        line-height: 50px;
      }
      .no-data {
        height: 100%;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
          width: 200px;
        }

        .text {
          color: #999999;
        }
      }
    }

    .template-panel {
      max-height: 900px;
      width: 470px;
      background: white;
      padding: 16px;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .text {
        font-size: 16px;
        font-weight: 700;
        letter-spacing: 0px;
        line-height: 16px;
        color: rgba(51, 51, 51, 1);
      }

      .toolbar {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
        gap:16px
      }

      .toolbar-button{
        min-width: 94px;
        height: 32px;
        color: #3f8cff;
        border: 1px solid #3f8cff;
        border-radius: 5px;
        padding: 10px;
        font-size: 14px;
        font-weight: 500;
        background: rgba(255, 255, 255, 1);
        display: inline-flex;
        gap: 5px;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .img{
          color:#3f8cff;
        }
      }
      .toolbar-button:hover {
        background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
        color: #ffffff;
      }

      .template-grid {
        max-height: 800px;
        min-width: 453px;
        flex-wrap: wrap; /* 允许子项换行 */
        display: flex;
        // grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        overflow: auto;
        padding-top: 12px;
      }

      .template-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        cursor: pointer;
        user-select: none;
      }

      .template-index {
        font-weight: bold;
        font-size: 18px;
        color: #666;
        width: 28px;
        flex-shrink: 0;
      }

      .template-index.active {
        color: #409EFF;
      }

      .template-card {
        width: 150px;
        height: 180px;
        border-radius: 5px;
        background: rgba(232, 241, 255, 1);
        box-shadow: 0px 2px 10px  rgba(67, 143, 254, 0.1);
        border-radius: 8px;
        padding: 12px;
      }

      .template-item.selected .template-card {
        background-color: rgba(201, 223, 255, 1);
      }

      .card-html {
        width: 100%;
        height: 100%;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
        color: #333;
        overflow: hidden;
        .card-content {
          // max-width: 330px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }

  }


}
</style>
