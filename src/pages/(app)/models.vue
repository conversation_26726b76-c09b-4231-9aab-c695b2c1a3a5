<script lang="ts">
  import type { ShallowRef } from 'vue'

  type ModelId = string

  const modelsContextInjectionKey = Symbol('models') as InjectionKey<{
    selectedModels: Readonly<ShallowRef<ModelId[]>>
    setSelectedModels(value: ModelId[]): void

    deployFormRawModel: Readonly<ShallowRef<Partial<API.Models.Model>>>
    setDeployFormRawModel(value: Partial<API.Models.Model>): void

    isDeployModalOpen: Readonly<Ref<boolean>>
    setIsDeployModalOpen(value: boolean): void

    formApi: Readonly<Ref<'create' | 'update'>>
    setFormApi(value: 'create' | 'update'): void

    rawModels: Readonly<ShallowRef<API.Models.Model[]>>
  }>

  export function injectModelsContext() {
    const context = inject(modelsContextInjectionKey)
    if (!context) {
      throw new Error('modelsContext is not provided')
    }
    return context
  }
</script>

<script setup lang="ts">
  import { message, Modal } from 'ant-design-vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

  import { useFuse } from '@vueuse/integrations/useFuse'
  import type { TableItem } from '@/components/models/table/ModelTable.vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  import ImgBackground from '@/assets/img/models-bg.jpg'
  import IconSearch from '~icons/ant-design/search-outlined'
  import IconTrash from '~icons/lucide/trash-2'
  import IconLoading from '~icons/lucide/loader-circle'

  definePage({
    meta: {
      roles: ['admin'],
    },
  })

  const isDeployModalOpen = ref(false)
  const deployFormRawModel = shallowRef<Partial<API.Models.Model>>({})
  const selectedModels = ref<string[]>([])
  const formAction = ref<'create' | 'update'>('create')

  const searchString = shallowRef('')
  const searchStringDebounced = refDebounced(searchString, 200)

  function deployNewModel() {
    deployFormRawModel.value = {}
    formAction.value = 'create'
    isDeployModalOpen.value = true
  }

  const { queryOptions } = useModelList()
  const { data: rawModels, suspense } = useQuery(queryOptions)

  await useNavigationSuspense({ delay: 500, suspense })

  const modelsTransformed = computed<TableItem[]>(
    () =>
      rawModels.value?.map((item, rawIndex) => ({
        id: String(item.id),
        name: item.name,
        source: item.local_path!,
        replicaNum: item.replicas,
        activeReplicaNum: item.ready_replicas,
        createTime: new Date(item.created_at),
        framework: item.backend,
        modelType: item.categories[0],
        rawIndex,
      })) ?? [],
  )

  const { results } = useFuse(searchStringDebounced, modelsTransformed, {
    fuseOptions: { keys: ['name'], threshold: 0.1 },
  })

  const models = computed(() => {
    if (!searchStringDebounced.value) {
      return modelsTransformed.value
    }
    return results.value.map((result) => result.item)
  })

  provide(modelsContextInjectionKey, {
    isDeployModalOpen: readonly(isDeployModalOpen),
    setIsDeployModalOpen(value) {
      isDeployModalOpen.value = value
    },

    deployFormRawModel: shallowReadonly(deployFormRawModel),
    setDeployFormRawModel(value) {
      deployFormRawModel.value = value
    },

    selectedModels: shallowReadonly(selectedModels),
    setSelectedModels(value) {
      selectedModels.value = value
    },

    formApi: readonly(formAction),
    setFormApi(value) {
      formAction.value = value
    },

    rawModels: computed(() => rawModels.value ?? []),
  })

  const formActionStatus = ref<MutationStatus>('idle')
  watchEffect(() => {
    if (formActionStatus.value === 'success') {
      isDeployModalOpen.value = false
    }
  })

  const invalidate = useModelsInvalidation()
  const deleteAll = useMutation({
    mutationFn() {
      const requestPromises = selectedModels.value.map((id) =>
        modelsFetcher(`/models/${id}/`, {
          method: 'delete',
        }),
      )
      return Promise.all(requestPromises)
    },
    onSuccess() {
      selectedModels.value = []
      return invalidate.allModels()
    },
  })

  watch(deleteAll.error, (err) => {
    if (err) {
      message.error(err.message)
    }
  })

  function onDeleteAllClicked() {
    Modal.confirm({
      content: `确定删除 ${selectedModels.value.length} 个模型吗？`,
      onOk() {
        deleteAll.mutate()
      },
      okButtonProps: {
        danger: true,
      },
      icon: h(ExclamationCircleOutlined),
      centered: true,
      wrapClassName: 'reset-ant-modal',
    })
  }
</script>

<template>
  <div
    class="h-full overflow-y-auto bg-cover bg-right bg-no-repeat [--page-px:40px]"
    :style="{ 'background-image': `url(${ImgBackground})` }"
  >
    <div class="px-(--page-px) pt-22.5 text-xl font-bold">模型管理</div>

    <div class="flex flex-wrap items-center gap-y-4 px-(--page-px) pt-7.5">
      <AInput
        class="max-w-[300px] rounded-full!"
        placeholder="搜索模型名称..."
        v-model:value="searchString"
      >
        <template #suffix>
          <IconSearch class="text-foreground-4" />
        </template>
      </AInput>

      <div class="text-foreground-3 ml-4">
        共有
        <span class="text-primary">{{ models.length }}</span>
        个筛选结果
      </div>

      <div class="ml-auto flex min-w-max items-center">
        <AButton
          type="primary"
          class="gradient-a-button w-[110px]"
          @click="deployNewModel"
        >
          部署模型
        </AButton>

        <AButton
          type="primary"
          ghost
          class="outline-a-button ml-2.5 flex! w-[110px] items-center gap-2"
          :disabled="selectedModels.length === 0"
          @click="onDeleteAllClicked"
        >
          <IconLoading
            v-if="deleteAll.status.value === 'pending'"
            class="animate-spin"
          />
          <IconTrash v-else />
          批量删除
        </AButton>
      </div>
    </div>

    <div class="px-(--page-px) py-5">
      <ModelTable :models="models" />
    </div>

    <AModal
      v-model:open="isDeployModalOpen"
      destroy-on-close
      title="部署模型"
      :ok-button-props="{
        htmlType: 'submit',
        // @ts-expect-error antdv poor typing
        form: 'model-deploy-form',
        loading: formActionStatus === 'pending',
      }"
      wrap-class-name="reset-ant-modal [&_.ant-modal-body]:overflow-y-auto [&_.ant-modal-body]:max-h-[610px]"
      :width="700"
    >
      <ModelDeployForm
        id="model-deploy-form"
        @update:status="formActionStatus = $event"
      />
    </AModal>
  </div>
</template>
