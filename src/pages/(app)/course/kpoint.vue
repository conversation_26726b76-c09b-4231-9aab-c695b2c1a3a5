<template>
  <div class="mian">
    <back :url="'-1'" /> 
    <div class="font-bold my-2 text-[24px] max-w-[40%] flex">
        <ATooltip 
            :title="getCourse().title" 
            placement="top"
            :mouseEnterDelay="0.3"
            >
            <div class="truncate cursor-pointer">{{ getCourse().title }}</div>
        </ATooltip>
        <div class="shrink-0">-知识点</div>
    </div>

    <div class="content gap-y-4 min-w-[217px]">
      <div class="item cursor-pointer" @click="openChoose">
        <img src="@/assets/image/course/kp1.png" style="width: 255px;height: 189px;margin-top: 33px;" />
        <div class="itemtitle">章节知识图谱</div>
        <div class="remark">查看课程章节、知识点关系 </div>
      </div>
      <div class="item cursor-pointer" @click="buchong">
        <img src="@/assets/image/course/kp2.png" style="width: 255px;height: 189px;margin-top: 33px;" />
        <div class="itemtitle">补充知识图谱</div>
        <div class="remark">可以使用大模型找寻知识点关系，用于学习</div>
      </div>
    </div>


    <a-modal v-model:open="open" :footer="null" :wrap-class-name="twMerge(
      '[&_.ant-modal-content]:p-[0px]!'
    )
      " width="700px">
      <div class="modal">
        <div style="height: 77px;"></div>
        <div class="modaltitle flex justify-center items-center">生成作业方式</div>
        <div class="flex justify-center flex-wrap" style="margin-top: 59px;">
          <img src="@/assets/image/course/aichoose.png" @click="aihandchoose" class="cursor-pointer" />
          <img src="@/assets/image/course/handlechoose.png" class="cursor-pointer ml-[50px]" @click="handchoose" />
        </div>
      </div>
    </a-modal>

  </div>
</template>

<script lang="ts" setup>
import { useCourseStore } from '@/stores/course'
const courseStore = useCourseStore()
import { getCourse } from '@/utils/auth'

import { creatExam } from '@/services/api/exam'
import { twMerge } from 'tailwind-merge'
import back from '@/components/ui/back.vue';
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router'
const router = useRouter()
const open = ref(false);
function openChoose() {
  // 跳转到知识图谱页面
  router.push({
    path: `/graphInfo`,
    query: { courseID: getCourse().id ,title: getCourse().title }
  })
}

async function handchoose() {
  
}
async function aihandchoose() {
  
}

function buchong(){
  // 跳转到知识图谱页面
  router.push( '/aiSpace/graph',)
}

</script>
<style lang="scss" scoped>
.mian {
  background: #F0F9FF;
  display: flex;
  flex-direction: column;
  padding: 0 40px;
  background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center bottom;
  min-height:100vh;
}

.top {
  display: flex;
  // align-items: center;
  // justify-content: space-between;
  font-size: 24px;
  font-weight: 700;
  margin: 7px 0; 
  .title {
    max-width: 40%;
  }
}

.content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  min-height: calc(100vh - 118px);
}

.item {
  width: 372px;
  height: 361px;
  flex-shrink: 0;
  flex-wrap: wrap;
  opacity: 1;
  border-radius: 10px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;

  .itemtitle {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 12px;
    color: rgba(0, 0, 0, 1);
    margin-top: 26px;
  }

  .remark {
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 30px;
    color: rgba(102, 102, 102, 1);
    margin-top: 21px;
  }
}

.hover {
  filter: grayscale(100%);
}

.modal {
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('@/assets/image/course/modelbg.png');
  width: 700px;
  height: 445px;
  opacity: 1;

  .modaltitle {
    margin: 0 auto;
    background-image: url('@/assets/image/course/xubg.png');
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.14) 0%, rgba(255, 255, 255, 0.6) 22.91%, rgba(255, 255, 255, 1) 67.72%, rgba(255, 255, 255, 0) 100%);

    // border: 1px solid rgba(255, 255, 255, 1);

    width: 378px;
    height: 47px;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 39px;
    color: rgba(41, 86, 150, 1);
  }

  img {
    width: 180px;
    height: 150px;
    cursor: pointer;
  }

  // background: linear-gradient(210.8deg, rgba(250, 252, 255, 1) 0%, rgba(191, 222, 255, 1) 100%);
}
</style>