<script setup lang="ts">
  import { message } from 'ant-design-vue'

  import IconLeft from '~icons/lucide/arrow-left'
  import ImgBg from '@/assets/img/recommendation-bg.png'
  import ImgStudent from '@/assets/img/student-recommendation.svg'
  import ImgTeacher from '@/assets/img/teacher-recommendation.svg'

  const courseStore = useCourseStore()
  const userStore = useUserStore()
  const route = useRoute('/(app)/course/[id]/recommendation')

  const { queryOptions } = useCourseRecommendations({ courseId: route.params.id })
  const { data, status, error } = useQuery(queryOptions)

  watch(error, (err) => {
    if (err) {
      message.error(err.message)
    }
  })
</script>

<template>
  <div
    class="h-full bg-cover bg-top-left [--page-px:40px]"
    :style="{ backgroundImage: `url(${ImgBg})` }"
  >
    <div class="px-(--page-px) pt-[50px] pb-5">
      <button
        class="text-foreground-2 flex cursor-pointer items-center gap-1.5"
        @click="$router.go(-1)"
      >
        <IconLeft />
        返回
      </button>
    </div>

    <div class="text-foreground-2 px-(--page-px) pb-[70px] text-2xl font-bold">
      {{ courseStore.courseDetails.title }}-资源推荐
    </div>

    <div class="relative z-0 mr-[80px] ml-(--page-px) px-13 pt-18 pb-23 xl:mr-[140px]">
      <div
        class="absolute inset-0 -z-3 rounded-[90px] bg-linear-135 from-white via-[rgba(245,249,255,1)] via-45% to-white shadow-[0_2px_19px_rgba(29,79,153,0.05)] blur-lg"
      />
      <div
        class="absolute right-3/10 -bottom-6 -z-1 size-[210px] rounded-full bg-[rgba(61,223,255,0.56)] blur-xl"
      />
      <div
        class="absolute -top-6 -right-6 -z-2 size-[310px] rounded-full bg-[rgba(63,140,255,0.1)]"
      />

      <template v-if="status === 'pending'">
        <div class="flex h-[400px] items-center justify-center">
          <ASpin
            spinning
            size="large"
          />
        </div>
      </template>

      <template v-else>
        <div class="flex space-x-14 contain-inline-size">
          <div class="grow">
            <div
              v-if="userStore.role === 'student'"
              class="flex items-center space-x-2.5 pb-4"
            >
              <img
                :src="ImgStudent"
                class="pointer-events-none"
              />
              <div class="text-foreground-2 text-xl font-bold">学习路径</div>
            </div>

            <div
              v-else
              class="flex items-center space-x-2.5 pb-4"
            >
              <img
                :src="ImgTeacher"
                class="pointer-events-none"
              />
              <div class="text-foreground-2 text-xl font-bold">教学内容</div>
            </div>

            <div class="max-h-[400px] overflow-y-auto">
              <RecommendationMarkdown :markdown="data?.advice ?? '暂无建议'" />
            </div>
          </div>

          <div
            class="max-h-[600px] w-100 shrink-0 rounded-[30px] bg-white px-11 py-20 shadow-[0px_2px_19px_rgba(29,79,153,0.05)]"
          ></div>
        </div>
      </template>
    </div>
  </div>
</template>
