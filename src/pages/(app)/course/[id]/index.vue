<template>
    <div class="details">
        <back :url="'/course'" />

        <div class="font-bold text-[24px] max-w-[40%] flex title">
            <ATooltip :title="getCourse().title" placement="top" :mouseEnterDelay="0.3">
                <div class="truncate cursor-pointer">{{ getCourse().title }}</div>
            </ATooltip>
        </div>

        <div class="">
            <div class="teacher flex flex-wrap">
                <div class="flex items-center">
                    <el-icon style="width: 12px;height: 12px;margin-right: 3px;">
                        <User />
                    </el-icon>
                    <div class="" style="margin-right: 20px;">授课老师：{{ userInfo.name }}</div>
                </div>
                <div style="margin-right: 34px;" class="flex items-center">
                    <el-icon style="width: 12px;height: 12px;margin-right: 3px;">
                        <Calendar />
                    </el-icon>
                    教学学期：{{ getSemester().label }}
                </div>
                <div class="flex items-center" style="color: #3F8CFF;cursor: pointer;" @click="() => $router.push('/teacherteam')">
                    <div>管理教学团队</div>
                    <el-icon style="width: 12px;height: 12px;margin-left: 3px;">
                        <Right />
                    </el-icon>
                </div>
            </div>

            <!--  -->
            <div class="konwledge mt-[20px]">
                <div class="titlek">知识库</div>
                <div class="grid grid-cols-1 gap-5  lg:grid-cols-2">
                    <div class="num2 flex-1" @click="resouress">
                        <div class="numTite">资源数量</div>
                        <div class="numBer">{{ konwledge_point?.document_count || 0 }}</div>
                    </div>
                    <div class="num1 flex-1" @click="toKonwledge">
                        <div class="numTite">知识点数量</div>
                        <div class="numBer">{{ courseDetails.knowledge_point_count || 0 }}</div>
                    </div>
                </div>
            </div>

            <div class="konwledge" style="margin-top: 20px;">
                <div class="titlek">资料库</div>
                <div class="grid grid-cols-1 gap-5  lg:grid-cols-3">
                    <div class="homewoke1 homewokeCom" @click="interTeachPlanManage()">
                        <div class="numTite">教案数量</div>
                        <div class="numBer">{{ courseDetails.knowledge_point_count || 0 }}</div>
                    </div>
                    <div class="homewoke2 homewokeCom" @click="interPptManage()">
                        <div class="numTite">PPT数量</div>
                        <div class="numBer">{{ courseDetails.ppt_count || 0 }}</div>
                    </div>
                    <div class="homewoke3 homewokeCom" @click="() => router.push('/quetionList')">
                        <div class="numTite">题库数量</div>
                        <div class="numBer">{{ courseDetails.question_count || 0 }}</div>
                    </div>
                </div>
            </div>

            <div class="items">
                <div class="item" v-for="(item, index) in items" :key="index" @click="handleClick(item)">
                    <img :src="getImageUrl(index)" />
                    <div class="titleItem">{{ item.title }}</div>
                </div>
            </div>
        </div>

    </div>
</template>

<script lang="ts" setup>
import axios from 'axios'
import { getSemester, getCourse } from '@/utils/auth'
import { courseDetail } from '@/services/api/course'
import { ref, reactive } from 'vue'
import back from '@/components/ui/back.vue';
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
const userInfo = storeToRefs(useUserStore())
console.log(userInfo, 'userInfo')

import { env } from '@/../env'

import imgUrl0 from '@/assets/image/img/homeworkitem/item1.png'
import imgUrl1 from '@/assets/image/img/homeworkitem/item2.png'
import imgUrl2 from '@/assets/image/img/homeworkitem/item3.png'
import imgUrl4 from '@/assets/image/img/homeworkitem/item4.png'
import imgUrl5 from '@/assets/image/img/homeworkitem/item5.png'
import imgUrl6 from '@/assets/image/img/homeworkitem/item6.png'
import imgUrl7 from '@/assets/image/img/homeworkitem/item7.png'
import imgUrl8 from '@/assets/image/img/homeworkitem/item8.png'
import imgUrl9 from '@/assets/image/img/homeworkitem/item9.png'
import { message } from 'ant-design-vue';

const getImageUrl = (index: number) => {
    return [imgUrl0, imgUrl1, imgUrl2, imgUrl4, imgUrl5, imgUrl6, imgUrl7, imgUrl8, imgUrl9][index]
}

const router = useRouter()
const route = useRoute()
function interTeachPlanManage() {
    router.push('/LessonPlan/TeachPlanManage')
};

const courseDetails = ref({
    knowledge_point_count: 0,
    ppt_count: 0,
    question_count: 0,
    resource_count: 0,
    teachplan_count: 0
})
interface RouteParams {
    id?: string; // 将 id 设为可选属性
}
const params = route.params as RouteParams;
const id = params.id ? String(params.id) : undefined;

interface konwledgePoint {
    document_count: string;
    kb_id: string;
}
const konwledge_point = ref<konwledgePoint>()
onMounted(async () => {
    try {
        const res = await courseDetail(id)
        console.log(res,'res----------')
        courseDetails.value = res.data
    } catch (error) {
        console.log(error)
    }
    const { data } = await  axios.get(`/api/v1/courses/${getCourse().id}/knowledge-bases/`)
    konwledge_point.value = data.data.data.knowledge_bases[0]
    
})
 
function toKonwledge(){
    router.push('/course/kpoint')
}

async function resouress(){
    console.log('resouress',konwledge_point.value)
    if(konwledge_point.value == undefined){
        // message.warning('没有关联的知识点')
        router.push('/aiSpace/kb')
        return
    }
    router.push(`/aiSpace/kb/${konwledge_point.value.kb_id}`)
}

function interPptManage() {
    router.push('/pptSquare/pptManage')
};

function handleClick(item: any) {
    if (item.title === '教案生成') {
        router.push('/LessonPlan/teachPlanTempEditor')
    } else if (item.title === 'PPT生成') {
        window.open(`${env.VITE_PPT_FRONTEND_URL}?type=course/${id}&courseId=${id}`, '_blank');
    } else if (item.title === '作业管理') {
        if (userInfo.role.value == "student") {
            router.push('/homework/student?type='+'homework')
            return
        }
        router.push('/homework?type='+'homework')
    } else if (item.title === '考试管理'){
        if (userInfo.role.value == "student") {
            router.push('/homework/student?type='+'exam')
            return
        }
        router.push('/homework?type='+'exam')

    } else if (item.title === '学情分析') {
        router.push('/course/courseAnalysis')
    } else if (item.title === '摘要提取') {
        router.push('/abstract')
    } else if (item.title === '章节管理') {
        console.log(userInfo.role.value,'-------------')
        // return
        if (userInfo.role.value == "student") {
            router.push('/course/chapter/student')
            return
        }
        router.push(`/course/chapter/${id}`)
        
    } else if (item.title === '课程班级') {
        router.push(`/courseClass`)
    } else if (item.title === '资源推荐') {
        router.push(`/course/${id}/recommendation`)
    }
}


const items = reactive([
    { title: '作业管理' },
    { title: '考试管理' },
    { title: '章节管理' },
    { title: '学情分析' },
    { title: 'PPT生成' },
    { title: '教案生成' },
    { title: '课程班级' },
    { title: '资源推荐' },
    { title: '摘要提取' }
])

</script>
<style lang="less" scoped>
.details {
    background-image: url('@/assets/image/img/coursebg1.png');
    background-size: cover;
    background-repeat: no-repeat;
    min-height: 100vh;
    padding: 0 40px;
    overflow: auto;
    width: 100%;



    .title {
        font-size: 24px;
        font-weight: 700;
        line-height: 24px;
        margin: 20px 0;
    }

    // .flex {
    //     display: flex;
    //     align-items: center;
    // }

    .teacher {
        color: #666666;
        font-size: 12px;
    }

    .konwledge {
        border-radius: 5px;
        background: #FFFFFF;
        border: 2px solid #FFFFFF;
        box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
        padding: 20px;


        .titlek {
            margin-bottom: 20px;
            font-weight: 700;
            font-size: 20px;
            line-height: 12px;
        }

        .num1 {
            background-image: url('@/assets/image/img/courseNum1.png');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: right;
            // width: 781px;
            height: 126px;
            opacity: 1;
            border-radius: 5px;
            padding: 30px 0 0 53px;
            cursor: pointer;
        }

        .numTite {
            color: rgba(102, 102, 102, 1);
            font-size: 16px;
        }

        .numBer {
            font-size: 25px;
            font-weight: 700;
            line-height: 34.3px;
            margin-top: 11px;
        }

        .num2 {
            background-image: url('@/assets/image/img/courseNum2.png');
            background-size: cover;
            background-position: right;
            background-repeat: no-repeat;
            // width: 781px;
            height: 126px;
            opacity: 1;
            border-radius: 5px;
            padding: 30px 0 0 53px;
            cursor: pointer;
        }


        .homewoke1 {
            background-image: url('@/assets/image/img/homework1.png');
        }

        .homewoke2 {
            background-image: url('@/assets/image/img/homework2.png');
        }

        .homewoke3 {
            background-image: url('@/assets/image/img/homework3.png');
        }

        .homewokeCom {
            background-size: cover;
            background-position: right;
            background-repeat: no-repeat;
            // width: 514px;
            flex: 1;
            height: 126px;
            border-radius: 5px;
            padding: 30px 0 0 30px;
            cursor: pointer;
        }
    }

    .items {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(253px, 1fr));
        gap: 20px;
        margin: 20px 0;
        // max-width: 1622px;
        width: 100%;

        .item {
            // width: 253px;
            height: 95px;
            border-radius: 4.54px;
            background: rgba(255, 255, 255, 1);
            border: 2px solid rgba(255, 255, 255, 1);
            box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
            display: flex;
            align-items: center;
            // justify-content: center;
            cursor: pointer;


            .titleItem {
                font-weight: 600;
                // margin-left: 25px;？
                font-size: 14px;
            }

            img {
                width: 48.06px;
                height: 51.21px;
                margin: 0 30px 0 50px;
            }
        }
    }
}
</style>
