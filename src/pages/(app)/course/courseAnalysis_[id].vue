<template>
  <div class="page">

      <div @click="handleBack" class="back-btn">
        <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
        <span style="font-size: 14px;line-height: 14px;">返回</span>
      </div>
      <h1 class="text">{{`${title}-学情分析-${analysis?.student_name}`}}</h1>

      <div class="summary">
        <div class="summary_header">
          <div class="summary_title">
            <img src="@/assets/image/course/analysis.png" style="width: 17px; height: 17px; margin-right: 10px;"/>
            学生学情分析报告
          </div>
          <div class="summary_but">
            <a-button type="primary" class="btn" @click="downPDF">导出pdf</a-button>
            <a-button type="primary" class="btn" @click="downWord">导出word</a-button>
          </div>
        </div>
        <div style="flex:1;  display: flex; width: 100%;">
            <div style="flex:1; width: 100%;">
                <div class="summary_content" style="padding-top: 20px;">
                  <div style="display: flex;align-items: center;gap:5px">
                    <div class="circle"></div>
                    <div style="color:black;font-weight: bold;">学习进度</div>
                  </div>
                    <div style="max-height: 100px;  word-wrap: break-word; overflow-y: auto;">{{ analysis?.learn_progress || '无' }}</div>
                </div>
                <div class="summary_content">
                  <div style="display: flex;align-items: center;gap:5px">
                    <div class="circle"></div>
                    <div style="color:black;font-weight: bold;">作业情况</div>
                  </div>
                    <div style="max-height: 100px;  word-wrap: break-word; overflow-y: auto;">{{ analysis?.homework_situation|| '无'  }}</div>
                </div>
                <div class="summary_content">
                  <div style="display: flex;align-items: center;gap:5px">
                    <div class="circle"></div>
                    <div style="color:black;font-weight: bold;">考试成绩</div>
                  </div>
                    <div style="max-height: 100px;  word-wrap: break-word; overflow-y: auto;">{{ analysis?.exam_grade || '无'  }}</div>
                </div>
            </div>
        </div>
        <div style="flex:1;  display: flex; flex-direction: row;">
            <div style="flex:1">
                <div class="summary_content">
                  <div style="display: flex;align-items: center;gap:5px">
                    <div class="circle"></div>
                    <div style="color:black;font-weight: bold;">评语</div>
                  </div>
                    <div style="max-height: 100px;  word-wrap: break-word; overflow-y: auto;">{{ analysis?.course_comment || '无'  }}</div>
                </div>
            </div>
            <div style="position: relative; width: 366px;">
              <img src="@/assets/image/course/ai.png" style="position: absolute;z-index:1;height: 366px; width: 366px;bottom:-70px"/>
            </div>
        </div>
      </div>
      
  </div>
</template>

<script lang="ts" setup>
import { studentAnalysis } from '@/services/api/analysis'
import { useRouter,useRoute } from 'vue-router'
import { downloadWord, downloadPDF } from '@/utils/downloadFile'

const route = useRoute() //获取路由参数
const id = (route.params as { id: string }).id

const router = useRouter()
const handleBack = () => {
  //需要动态配置课程id
  router.back()
}
const spinning = ref(false)

interface file {
  id: number;
  student_name: string;
  student_number: number;
  learn_progress: string;
  homework_situation: string;
  exam_grade: string;
  course_comment: string;
}

const analysis = ref<file>()
const summary = ref('')
const title = ref('')

onMounted(() => {
  // 从 localStorage 中获取数据
  const storedCourse = JSON.parse(localStorage.getItem('course') as string);
  title.value = String(storedCourse?.title);

  getAnalysis() //  获取详情分析
})

// 获取课程学情分析
async function getAnalysis() {
  try {
    spinning.value = true
    const res = await studentAnalysis(id)
    analysis.value = res.data
    summary.value =  `- ${title}-学情分析-${analysis.value?.student_name}\n- 学习进度：${analysis.value?.learn_progress}\n- 作业情况：${analysis.value?.homework_situation}\n- 考试成绩：${analysis.value?.exam_grade}\n- 评语：${analysis.value?.course_comment}`
    spinning.value = false
  } catch (error) {
    console.error('获取学生学情分析详情失败:', error)
  }
}

async function downPDF() {
  if(summary.value){
    downloadPDF(summary.value, `学情分析总结-${analysis.value?.student_name}.pdf`);
  }else{
    console.log('暂无总结')
  }
}

async function downWord() {
  if(summary.value){
    downloadWord(summary.value, `学情分析总结-${analysis.value?.student_name}.doc`);
  }else{
    console.log('暂无总结')
  }
}

</script>

<style scoped lang="scss">

.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0 40px;

  .back-btn {
    cursor: pointer;
    width: 60px;
    margin-top: 50px;
    color: #333;
    display: flex;
    align-items: center;
  }

  .text {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 12px;
    margin: 20px 0 20px;
  }

  .summary{
    min-height: 800px;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4.79px;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .summary_header{
      display: flex;
      align-items: center;
      justify-content: space-between; 
      padding-bottom: 20px;
      border-bottom: 1px solid rgba(229, 229, 229, 1);
      .summary_title{
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: 700;
        letter-spacing: 0px;
        line-height: 24px;
      }
      .summary_but{
        display: flex; 
        justify-content: 
        space-between; gap:15px
      }
    }

    .summary_content {
        width: 100%;
        height: 150px;
        padding-top: 10px;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0px;
        line-height: 20px;
        color: rgba(102, 102, 102, 1);
        text-align: left;
        overflow: auto;
        .circle {
          width: 10px; /* 圆的宽度 */
          height: 10px; /* 圆的高度 */
          background-color: #3f8cff; /* 圆的背景颜色 */
          border-radius: 50%; /* 将方形变为圆形 */
        }
    }
    .btn{
        background: rgba(63, 140, 255, 1); 
        width: 71px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
  }

}

</style>
