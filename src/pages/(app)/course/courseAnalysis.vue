<template>
  <div class="page">

      <back :url="'-1'" />
      <h1 class="text">{{ `${title}-学情分析` }}</h1>
      <div class="toolbar gap-y-2">
        <div class="toolbar-left">
            <a-select
                ref="select"
                v-model:value="valueCalss"
                style="width: 100px;"
                :options="classOption"
                placeholder="班级选择"
                allowClear
                @change="handleChange"
            ></a-select>
            <a-input v-model:value="searchValue" style="border-radius: 94px;width: 302px;min-height: 32px;" placeholder="输入学生姓名查询" @pressEnter="getList">
              <template #suffix>
                <SearchOutlined style="color:#C4C4C4" @click="getList"/>
              </template>
            </a-input>
        </div>
        <div class="toolbar-right">
            <a-button type="text" class="addbut" >作业</a-button>
            <a-button type="text" class="addbut" >考试</a-button>
            <a-button type="text" class="addbut" >章节学习</a-button>
        </div>
      </div>
      <a-spin :spinning="spinning">
        <div class="table">
          <div class="table-head height" style="font-weight:600">
            <!-- <div class="item" style="max-width: 56px;">
              <a-checkbox v-model:checked="state.checkAll" :indeterminate="state.indeterminate"
                @change="onCheckAllChange" />
            </div> -->
            <div class="item" style="width: 200px;">姓名</div>
            <div class="item" style="width: 200px;">学号</div>
            <div class="item" style="width: 200px;">学习状态</div>
            <div class="item" style="max-width: 120px;">操作</div>
          </div>

          <div class="center" v-if="files?.length === 0">
            <img src="@/assets/image/zanwu/nodata.png" style="width: 385px;height: 379px;" />
          </div>
          <div v-else>
            <div class="table-head height1" v-for="item in files" :key="item.id">
              <!-- <div class="item" style="max-width: 58px;">
                <a-checkbox :checked="selectedIds.includes(item.id)"
                  @change="(e: any) => handleCheckboxChange(item.id, e.target.checked)" />
              </div> -->
              <div class="item file-name titlew">
                <div style="" class="file-name-text">
                  {{ item.student_name }}
                </div>
                <!-- <span class="edit-icon" style="">
                  <a-tooltip placement="bottom">
                    <template #title>
                      <span>编辑</span>
                    </template>
                    <img style="cursor: pointer;width: 14px;height: 14px;" src="@/assets/image/img/edittable.png" @click="editTeachPlan(item)" />
                  </a-tooltip>
                </span> -->
              </div>
              <div class="item" style="width: 200px;">
                {{ item.student_number || '无' }}
              </div>
              <div class="item" style="width: 200px;">
                {{ item.analysis_status || '无'  }}
              </div>
              <div class="item" style="max-width: 118px;display: flex;justify-content: center;">
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>查看</span>
                  </template>
                  <img style="cursor: pointer;" src="@/assets/image/graph/eye.png" @click="watchID(item.id)"/>
                </a-tooltip>
              </div>
            </div>
          </div>

          <div class="pagination">
            <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
              layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </div>
      </a-spin>


  </div>
</template>

<script lang="ts" setup>
import { formatDate } from '@/utils/util'
import { EyeOutlined, SearchOutlined} from '@ant-design/icons-vue';
//引入接口
import { courseAnalysis,courseClassList} from '@/services/api/analysis'
import { usePagination } from '@/hooks/usePagination'; //解构分页参数
import { ref, computed, onMounted, reactive, } from 'vue'
import { Search,} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { message, Modal} from 'ant-design-vue';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import type { SelectProps } from 'ant-design-vue';

const router = useRouter()

//定义列表数据结构
interface fileList {
  id: number;
  student_name: string;
  student_number: number;
  analysis_status: string;
}

// 模拟数据
const files = ref<fileList[]>()
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getList);
const spinning = ref(false)

const valueCalss = ref();  //班级选择
const classOption = ref<{value: any; label: string}[]>([]);
// 监听 classOption 的变化，设置默认值为第一个班级
watch(classOption, (newVal) => {
  if (newVal.length > 0) {
    valueCalss.value = newVal[0].value
    getList()
  }
})
const handleChange = (value: any) => {
  console.log(`selected ${value}`);
  getList()
};

// 获取作业学情分析班级列表
async function getClassList() {
  const params = {
    course_semester_id: course_semester_ID.value
  }
  try {
    const res = await courseClassList(params)
    classOption.value = []
    res.data.forEach((item: any) => {
        classOption.value.push({
            value: item.id,
            label: item.name
        })
    })
  } catch (error) {
    console.error('获取作业学情分析列表失败:', error)
  }
}

const searchValue = ref('')

// const courseId = ref('');
// const semesterId = ref('');
const title = ref('')
const course_semester_ID = ref('')

onMounted(() => {
  // 从 localStorage 中获取课程数据
  const storedCourse = JSON.parse(localStorage.getItem('course') as string);
  // 获取 title 值
  title.value = String(storedCourse?.title);
  // 获取 course_semester_ID 值
  course_semester_ID.value = storedCourse.course_semester_id

  // courseId.value = String(storedCourse?.id);
  // // 从 localStorage 中获取学期数据
  // const storedSemester = JSON.parse(localStorage.getItem('semester') as string);
  // semesterId.value = String(storedSemester?.value);

  console.log(typeof(course_semester_ID.value));
  console.log('课程学期id',course_semester_ID.value);
  getClassList()
  getList() //  获取教案列表
})

// 获取课程学情分析列表
async function getList() {
  const params = {
    page: currentPage.value,
    page_size: pageSize.value,
    class_id: valueCalss.value,
    student_name: searchValue.value,
    course_semester_id: course_semester_ID.value
  }
  try {
    spinning.value = true
    console.log(params,'params')
    const res = await courseAnalysis(params)
    files.value = res.data.results
    total.value = res.data.count
    spinning.value = false
  } catch (error) {
    console.error('获取作业学情分析列表失败:', error)
  }
}

const watchID = (id:number) => {
  // 跳转到查看页面
  router.push(`courseAnalysis_${id}`)
}


</script>

<style scoped lang="scss">

:deep(.ant-select-selector){
    border-radius: 20px;
}


.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0 40px;

  .back-btn {
    cursor: pointer;
    width: 60px;
    margin-top: 50px;
    color: #333;
    display: flex;
    align-items: center;
  }

}

.text {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 12px;
  margin: 20px 0 20px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  min-width: 900px;
  flex-wrap: wrap;
  .toolbar-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }

  .toolbar-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;

    .addbut {
      width: 90px;
      color: #fff;
      background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
    }
  }
}

:deep(.el-select__wrapper) {
  border-radius: 95px;
}


:deep(.el-input__wrapper) {
  border-radius: 94px;
}


.table {
  background: #fff;
  border-radius: 4.79px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 0 20px 20px;
  min-width: 900px;
  width: 100%;


  .table-head {
    // display: flex;
    align-items: center;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    /* 禁止换行 */
    overflow-x: auto;
    /* 允许水平滚动（如果需要） */
    font-size: 14px;
    padding-left: 12px;

    .item {
      flex: 1 0 auto;
      white-space: nowrap;
      /* 防止文字换行 */
    }

    .flex {
      display: flex;
      align-items: center;
    }

    .titlew {
      width: 200px;
      position: relative;
      height: 56px;
      line-height: 56px;
    }

    .file-name-text {
      max-width: 200px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden
    }

    .file-name .edit-icon {
      opacity: 0;
      transition: opacity 0.3s ease;
      position: absolute;
      left: 345px;
      top: 50%;
      transform: translateY(-50%);
    }

    .file-name:hover .edit-icon {
      opacity: 1;
    }
  }

  .height {
    height: 56px;
    border-bottom: 1px solid #E5E5E5;
  }

  .height1 {
    height: 58px;
    border-bottom: 1px solid #E5E5E5;
    color: #666666;

    &:hover {
      background: rgba(63, 140, 255, 0.05);
    }
  }
}

.pagination {
  margin: 20px 0 12px 0;
  text-align: center;
}

:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}
</style>
