<template>
    <div class="chapter">
        <back :url="'-1'"></back>
        <div class="text-[24px] leading-[12px] my-[20px] font-bold">{{getCourse().title}}-章节</div>
        <div class="gap-[10px] flex justify-between">
            <!-- <el-select v-model="valueSelect" placeholder="班级选择" style="width: 100px;" suffixIcon="CaretBottom"
                clearable>
                <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
            </el-select> --> 

            <div class=" w-[302px] h-30px">
                <AInput
                    class="rounded-full!"
                    placeholder="请输入..."
                    v-model:value="searchValue"
                    @change="getchapterList"
                >
                    <template #suffix>
                        <IconSearch class="text-foreground-4"  @click="getchapterList"/>
                    </template>
                </AInput>
            </div>
        </div>

        <a-spin :spinning="spining">
            <div v-if="chapter.length">
                <div class="bg-white mt-[10px] rounded-[0.4px] pt-[20px] pb-[20px] pl-[20px] pr-[32px]" v-for="item in chapter"
                    >
                    <div class=" flex justify-between pr-[20px]" :class="{ 'mb-[20px]': item.open }">
                        <div class="flex items-center gap-[10px]">
                            <div class="w-[14px] cursor-pointer relative" @click="item.open = !item.open">
                                <CaretDownOutlined v-if="item.open" />
                                <CaretRightOutlined v-else />
                                <div v-if="item.open" class="absolute left-1/2 top-full  w-px bg-[#E5E5E5] -translate-x-1/2"
                                    :style="{ height: `calc(66px * ${item.children.length})` }"></div>
                            </div>
                            <img src="@/assets/image/course/analysis.png" class="w-[17px] h-[17px]">
                            <div class="font-bold text-[16px] leading-[16px]">{{ item.title }}</div>
                        </div>
                        <div class="flex items-center gap-[10px]">
                            <div class="text-[#666666] text-[14px]">章节学习情况：</div>
                            <div class="w-[138px] mr-[60px] text-[#333333] font-[500] flex items-center">
                                <div class="w-[7px] h-[7px] rounded-full bg-[#FF5733] mr-[8px]" v-if="item.completions?.percentage == null"></div>
                                {{ item.completions?.percentage == null ?'未学习':'已学习'}}
                                <!-- <a-progress :percent="item.completions?.percentage || 0" style="margin: 0;" strokeColor="#1890FF"/> -->
                            </div>
                            <div class="flex items-center gap-[20px]">
                                <a-tooltip>
                                    <template #title>查看</template>
                                    <img src="@/assets/image/img/viewIcon.png" class="w-[14px] h-[14px] cursor-pointer"
                                        @click="viewChapter(item,item.id)" />
                                </a-tooltip>
                            </div>
                        </div>
                    </div>



                    <div v-if="item.open" class=" bg-[rgba(204,204,204,0.07)] p-[20px] ml-[24px] mt-[10px] 
                        border border-[#F2F2F2] rounded-[5px] relative" v-for="i in item.children">

                        <div
                            class="absolute left-[-18px] top-1/2 h-px w-[18px] bg-[#E5E5E5] before:absolute before:content-[''] before:left-0 before:top-1/2 before:w-full before:h-px before:bg-[#E5E5E5]">
                        </div>

                        <div class="flex items-center justify-between h-[14px]">
                            <div class="flex items-center gap-[10px]">
                                <div class="w-[10px] h-[10px] bg-[#3F8CFF] rounded-[50%]"></div>
                                <div class="font-bold text-[14px] leading-[14px]">{{ i.title }}</div>
                            </div>
                            <div class="flex gap-[10px]">
                                <div class="w-[138px] mr-[60px] text-[#333333] font-[500] flex items-center">
                                    <div class="w-[7px] h-[7px] rounded-full bg-[#FF5733] mr-[8px]" v-if="item.completions?.percentage == null"></div>
                                    {{ item.completions?.percentage == null ?'未学习':'已学习'}}
                                    <!-- <a-progress :percent="i.completions?.percentage || 0" style="margin: 0;" strokeColor="#1890FF"/> -->
                                </div>
                                <div class="flex items-center gap-[20px]">
                                    <a-tooltip>
                                        <template #title>删除</template>
                                        <img src="@/assets/image/img/viewIcon.png" class="w-[14px] h-[14px]"  @click="viewChapter(item,i.id)"/>
                                    </a-tooltip>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="flex items-center justify-center flex-col h-[calc(100vh-156px)]" v-else>
                <img src="@/assets/image/zanwu/zanwuIcon.png" class="w-[309px] h-[221px]" />
                <div class="text-[#666666] text-[14px]">暂无章节目录</div>
            </div>
        </a-spin>
        



        

        
    </div>
</template>
<script lang="ts" setup>
import IconSearch from '~icons/ant-design/search-outlined'
import { chapterAdd, chapterDelete } from '@/services/api/course'
import { CaretUpOutlined, CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons-vue';
import { chapterList } from '@/services/api/course'
import back from '@/components/ui/back.vue';
import { useRouter, useRoute } from 'vue-router'
import { Form } from 'ant-design-vue';
const useForm = Form.useForm;
const router = useRouter()
const route = useRoute() //获取路由参数
import { useCourseStore } from '@/stores/course'
const courseStore = useCourseStore()
import { getSemester,getCourse } from '@/utils/auth'
import { flattenChildren } from '@/utils/util'
import { message } from 'ant-design-vue';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'

const valueSelect = ref('');
const searchValue = ref('');
const options = [
    {
        id: '1',
        name: '2021级软件工程1班'
    }
]
interface Completion {
    percentage: number;
}
interface Chapter {
    id: number,
    title: string,
    progress: number,
    completions:Completion | null,
    open: boolean,
    children: Chapter[]
}
const chapter = ref<Chapter[]>([])



// 获取章节列表
const spining = ref(true)
function getchapterList() {
    const params = {
        course_id: getCourse().id,
        title:searchValue.value
    }
    spining.value = true
    chapterList(params).then((res: any) => {
        console.log(res)
        spining.value = false
        if (res.code == 200) {
            // chapter.value 
            const chapters = res.data.map((item: any) => { 
                return flattenChildren(item)
            })
            chapter.value = chapters
            console.log(chapter.value,'chapterchapter')
        }
    })
}


function viewChapter(item: any,i: any) {
    console.log(item, i,'item');
    // return
    router.push({
        path: '/chapter/view',
        query: {
            id: i,
            chapterId: item.id,
            courseId: getCourse().id
        }
    })
}


getchapterList()
</script>
<style scoped lang="scss">
:deep(.el-select__wrapper) {
    border-radius: 50px;
    min-height: 30px !important;
}

:deep(.el-input__wrapper) {
    border-radius: 50px !important;
}

.chapter {
    min-height: 100vh;
    background: #F0F9FF;
    padding: 0 40px;
    background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
    background-repeat: no-repeat;
    background-size: 100% 50%;
    background-position: center bottom;
}
</style>
