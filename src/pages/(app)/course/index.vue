<template>
    <div class="course flex flex-col overflow-hidden!">
        <div class="title shrink-0">我的课程</div>
        <div class="flex shrink-0 mt-[20px] mr-[40px] items-center justify-between flex-wrap gap-y-2">
            <div class="flex items-center flex-wrap gap-y-2">
                <a-select v-model:value="valueSelect" style="width: 186px;border-radius: 120px;" class="round-120  mr-[16px]!"
                    :allowClear="false" placeholder="课程学期" :options="options" @change="getCourseList(valueSelect)" />
                <div class="w-[186px] h-30px mr-[10px]">
                    <AInput class="rounded-full!" placeholder="请输入..." v-model:value="searchValue"
                        @change="getCourseList(valueSelect)">
                        <template #suffix>
                            <IconSearch class="text-foreground-4" @click="getCourseList(valueSelect)" />
                        </template>
                    </AInput>
                </div>
                <div style="font-size: 14px;">共有<span style="color: rgba(63, 140, 255, 1);">{{ courseList.length
                }}</span>条结果</div>
            </div>
            <div style="font-size: 14px;width: 82px;height: 32px;">
                <el-button @click="createCourse" round>创建课程</el-button>
            </div>
        </div>
        <div class=" overflow-auto hide-scrollbar">
            <a-spin :spinning="loadingCourse">
                <div class="flex gap-5 flex-wrap pt-[20px]">
                    <div class="course-item" v-for="(item, index) in courseList" :key="index" @click="clickCourse(item)">
                        <div>
                            <img :src="item.image" class="rounded-t-[5px]"
                            style="width: 336px; height: 180px; object-fit:contain ;margin: auto;" />
                        </div>
                        <div style="height: 89px;">
                            <div class="course-title">{{ item.title }}</div>
                            <div class="course-info">
                                <div style="color: #666;">
                                    {{ item.semestername?.label }}
                                </div>
                                <el-popover v-model:visible="item.visible" trigger="hover" placement="bottom-end"
                                    :width="87" :popper-style="{ minWidth: '87px', padding: '0', width: 'auto' }">
                                    <div class="popover">
                                        <div class="flexa" style="">
                                            <el-icon>
                                                <Edit />
                                            </el-icon>
                                            <div style="margin-left: 6px;" @click.stop="clickPopover(item)">编辑</div>
                                        </div>
                                        <div class="flexa">
                                            <el-icon>
                                                <CircleClose />
                                            </el-icon>
                                            <div style="margin-left: 6px;" @click.stop="deteleCourse(item)">删除</div>
                                        </div>
                                    </div>
                                    <template #reference>
                                        <el-icon class="popover-icon" style="cursor: pointer;">
                                            <MoreFilled />
                                        </el-icon>
                                    </template>
                                </el-popover>
                            </div>
                        </div>
                    </div>
                </div>
            </a-spin>
        </div>
        <a-modal v-model:open="open" :title="modelTitle" :footer="null" width="700px">
            <div style="margin: 50px 0 0 0;">
                <a-form :model="course" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }"
                    autocomplete="off">
                    <a-form-item label="课程名称" v-bind="validateInfos.title">
                        <a-input v-model:value="course.title" placeholder="请输入课程名称" />
                    </a-form-item>
                    <a-form-item label="课程学期" v-bind="validateInfos.semester" v-if="modelTitle == '创建课程'">
                        <a-select v-model:value="course.semester" show-search placeholder="请选择课程学期"
                            :options="options"></a-select>
                    </a-form-item>
                    <a-form-item label="知识库" v-if="modelTitle == '创建课程'">
                        <a-select v-model:value="course.konwelage" show-search placeholder="请选择绑定知识库"
                            :options="kbsTransformed" />
                    </a-form-item>
                    <a-form-item label="课程封面" v-bind="validateInfos.image">
                        <uploadAnt :urlList="imageCourse" @beforeUploadgetfile="getfile" @deleteFile="delFile" />
                    </a-form-item>
                    <div style="display: flex;justify-content: flex-end">
                        <a-form-item>
                            <a-space>
                                <a-button @click=" open = false">取消</a-button>
                                <a-button type="primary" html-type="submit" @click="onSubmit" style=""
                                    :loading="loadingSubmit">确定</a-button>
                            </a-space>
                        </a-form-item>
                    </div>
                </a-form>
            </div>
        </a-modal>
    </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/stores/user'
import IconSearch from '~icons/ant-design/search-outlined'
import { useCourseStore } from '@/stores/course'
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import uploadAnt from '@/components/ui/uploadant.vue'
import { courseAdd, courseListapi, courseDelete, courseEdit, semesterList } from '@/services/api/course'
import { Search } from '@element-plus/icons-vue'
import { CaretUpOutlined } from '@ant-design/icons-vue'
import { Form } from 'ant-design-vue';
import { message } from 'ant-design-vue';
const useForm = Form.useForm
import { reactive, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()

const modelTitle = ref('创建课程') //弹窗标题
const imageCourse = ref('') //课程图片

const valueSelect = ref()
const searchValue = ref('')

//读取pinia里买呢存储的课程id以及用户id
const courseStore = useCourseStore()
const { semester_id } = storeToRefs(courseStore)

const userInfo = storeToRefs(useUserStore())
onMounted(async () => {
    console.log(userInfo,'用userInfo户信息')
    getsemesterList()
})



interface optionsType {
    name: string;
    id: number;
}

interface optionsTypek {
    label: string;
    value: number;
}
const options = ref<optionsType[]>([]) //学期列表数据
// const konwelageOptions = ref<optionsTypek[]>([]) //知识库列表数据
import { useFuse } from '@vueuse/integrations/useFuse'
const searchString = ref('')
const searchStringDebounced = refDebounced(searchString, 200)
const { data: rawKbs } = useQuery({
    queryKey: kbQueryKey.kbList(),
    async queryFn() {
        const { data } = await kbFetcher<{ data: { datasets: API.Kb.Kb[] } }>('/datasets/', {
            query: {
                page_size: 9999,
                source: 'local',
            },
        })
        return data.datasets
    },
})

const kbsTransformed = computed(
    () =>
        rawKbs.value?.map((kb) => ({
            value: kb.id,
            label: kb.name, // 修复：将 lable 改为 label
        })) ?? [],
)

console.log(kbsTransformed, '知识库列results表')


interface Course {
    title: string;
    description: string;
    semester: number;
    semestername?: { label: string };
    konwelage: string;
    image: string;
    teacher: number;
    role: string;
    visible: boolean;
}
const courseList = ref<Course[]>([])
//课程列表
const loadingCourse = ref(false)
function getCourseList(semester: number) {
    console.log(semester, '课程列表')
    loadingCourse.value = true
    const params = {
        semester_id: semester,//course.semester || semester,//学期id
        // user_id:user_id.value,
        title: searchValue.value
    }
    courseListapi(params).then(res => {
        loadingCourse.value = false
        courseList.value = res.data.results.map((item: any) => {
            return {
                ...item,
                semestername: options.value.find((option: any) => option.value === semester) || '未知学期',
                visible: false // 控制是否显示 popover
            }
        })

        console.log(res.data, courseList.value, '课程列表')
    })
}



function onSearch() {
    getCourseList(valueSelect.value)
}


//创建课程弹窗
const open = ref<boolean>(false);
watch(open, (newValue) => {
    if (!newValue) {
        resetFields()
        imageCourse.value = ''
    }
})

const course = reactive({
    title: '',
    description: '',//描述
    semester: '',
    konwelage: '',
    image: '',
    teacher: '',
    role: '',
});
const courseRules = reactive({
    title: [{ required: true, message: '请输入课程名称', trigger: 'change' }],
    semester: [{ required: true, message: '请输入学期名称', trigger: 'change' }],
    image: [{ required: true, message: '请上传课程图片', trigger: 'change' }],
})

//获取封面文件流
const getfile = (file: any) => {
    course.image = file
}
const delFile = (file: any) => {
    course.image = ''
}

//创建课程弹窗
function createCourse() {
    modelTitle.value = '创建课程'

    imageCourse.value = ''
    resetFields()
    open.value = true
}

const loadingSubmit = ref(false)
const { resetFields, validate, validateInfos } = useForm(course, courseRules);//验证课程表单提交
const onSubmit = () => {
    validate().then(() => {
        onSubmitSuccess()
    }).catch(err => {
        console.log('error', err);
    });
};
const onSubmitSuccess = () => {
    loadingSubmit.value = true
    const formData = new FormData();
    formData.append('title', course.title);
    formData.append('semester', JSON.stringify(course.semester)); // 转为字符串
    formData.append('knowledge_uid', course.konwelage);//course.konwelage'4fea0a6662dd11f0b0db4ad5a33f453a'
    formData.append('image', course.image);
    formData.append('teacher', JSON.stringify(userInfo.id.value));
    formData.append('role', userInfo.role.value);

    if (modelTitle.value === '编辑课程') {
        formData.append('id', courseEditId.value);
        if (typeof course.image == 'string') {
            formData.delete('image');
        }
        courseEdit(formData).then(res => {
            loadingSubmit.value = false
            open.value = false
            course.image = ''
            message.success('编辑成功')
            getCourseList(valueSelect.value)
            resetFields();
        }).catch(err => {
            loadingSubmit.value = false
            message.error('编辑失败')
        })
        return
    }

    try {
        courseAdd(formData).then(res => {
            loadingSubmit.value = false
            open.value = false
            course.image = ''
            message.success('创建成功')
            getCourseList(valueSelect.value)
            resetFields();
        }).catch(err => {
            loadingSubmit.value = false
            message.error('课程名称不可重复或创建失败')
            // console.log(err,'----')
        })
    } catch (error) {
        message.error('编辑失败')
    }
};

const courseEditId = ref('')
function clickPopover(item: any) {
    console.log(item, '点击了')
    item.visible = false // 关闭 popover
    open.value = true
    modelTitle.value = '编辑课程'
    course.title = item.title
    course.image = item.image
    course.semester = item.semestername.value
    imageCourse.value = item.image //课程图片封面
    courseEditId.value = item.id
}
//删除课程
async function deteleCourse(item: any) {
    const confirmed = await showDeleteConfirm('确定删除本记录吗?删除后无法恢复!');
    console.log(item.id, 'item');
    if (confirmed) {
        courseDelete(item.id).then(res => {
            message.success('删除成功')
            getCourseList(valueSelect.value)
        })
    }
}
function clickCourse(item: any) {
    courseStore.setCourseId(item.id)
    courseStore.setCourseDetails(item) //存储课程详情
    const semester = options.value.filter((item: any) => item.value == valueSelect.value)[0]
    semester_id.value = semester.id


    localStorage.setItem('course', JSON.stringify(item))
    localStorage.setItem('semester', JSON.stringify(semester))
    router.push({
        path: `course/${item.id}`,
    })
}


async function getsemesterList() {
    const data = await semesterList()
    options.value = data.data.map((item: any) => {
        return {
            value: item.id,
            label: item.name
        }
    })
    console.log(getSemester(), '学期列表')
    valueSelect.value = getSemester() == null ? data.data.slice(-1)[0].id : getSemester().value
    course.semester = getSemester() == null ? data.data.slice(-1)[0].id : getSemester().value
    //  获取课程列表
    getCourseList(valueSelect.value)
}




</script>
<style scoped lang="scss">
:deep(.el-select__wrapper) {
    border-radius: 50px;
}

:deep(.ant-modal) {
    height: 539px !important;
}

:deep(.el-input__wrapper) {
    border-radius: 50px !important;
}

.round-120 :deep(.ant-select-selector) {
    border-radius: 120px;
}


:deep(.ant-select-lg) {
    border-radius: 40px !important;
}

:deep(.el-button) {
    background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
    color: #fff;
    font-size: 14px !important;
}

:deep(.el-input__suffix-inner) {
    cursor: pointer;
}

:deep(.el-popover .el-popper) {
    min-width: 100px !important;
    width: 100px !important;
}

.course {
    background-image: url('@/assets/image/img/bg.png');
    background-repeat: no-repeat;
    background-size: cover;
    height: 100vh;
    width: 100%;
    padding: 0% 0 0 40px;
    :deep(.ant-spin-container) {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .title {
        padding: 90px 0 0 0%;
        font-size: 20px;
        font-weight: 700;
        letter-spacing: 0px;
        line-height: 12px;
        // height: 12px;
    }

    .course-list {
        // display: flex;
        // flex-wrap: wrap;
        // gap:20px;
    }

    .course-item {
        width: 336px;
        height: 270px;
        border-radius: 5px;
        box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
        background-color: #fff;
        transition: transform 0.1s ease, box-shadow 0.1s ease;

        &:hover {
            transform: translateY(-5px); 
            cursor: pointer;
            //  box-shadow: 0 12px 30px rgba(67, 143, 254, 0.3);
            box-shadow: 0px 2px 10px  rgba(67, 143, 254, 0.2); // 加深阴影
        }

        .course-title {
            font-size: 16px;
            font-weight: 700;
            margin: 20px 20px 0 20px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            overflow: hidden;
            line-height: 16px;
            height: 16px;
        }

        .course-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 20px;
            font-size: 12px;
            line-height: 12px;
            height: 12px;
        }
    }
}

.popover {
    display: flex;
    justify-content: center;
    padding: 10px 0;
    flex-direction: column;
    align-items: center;

    .flexa {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        width: 67px;
        height: 34px;
    }

    .flexa:hover {
        border-radius: 3px;
        background: rgba(63, 140, 255, 0.1);
        // width: 67px;
        height: 34px;
        text-align: center;
        color: rgba(63, 140, 255, 1);
        font-weight: 600;
    }
}

.popover-icon {
    color: #666; // 默认颜色继承父级

    &:hover {
        color: rgb(63, 140, 255); // 悬停时的颜色background: ;
    }
}
</style>
