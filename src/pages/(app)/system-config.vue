<script lang="ts">
  const TABS = ['admins', 'permissions', 'filters']
</script>

<script setup lang="ts">
  import { TabsRoot, TabsContent } from 'reka-ui'
  import { useRouteQuery } from '@vueuse/router'

  import ImgBg from '@/assets/img/system-config-bg.webp'

  definePage({
    meta: {
      roles: ['admin'],
    },
  })

  const persistedTab = useRouteQuery<string | undefined>('tab')
  const modelValue = ref<string>('admins')
  watch(
    persistedTab,
    (newVal, oldVal) => {
      if (TABS.includes(newVal as string)) {
        modelValue.value = newVal as string
      } else {
        modelValue.value = oldVal ?? 'admins'
      }
    },
    { immediate: true },
  )
  watch(modelValue, (newVal) => {
    persistedTab.value = newVal
  })
</script>

<template>
  <TabsRoot
    class="flex h-full flex-col overflow-y-auto bg-[rgba(240,249,255,1)] bg-size-[100%_auto] bg-bottom bg-no-repeat [--page-px:40px]"
    :style="{ 'background-image': `url(${ImgBg})` }"
    :unmount-on-hide="false"
    v-model="modelValue"
    default-value="admins"
  >
    <div class="flex shrink-0 items-center px-(--page-px) pt-22.5">
      <div class="text-foreground-2 mr-5 text-2xl font-bold">系统配置</div>
      <TabsList
        :items="[
          { label: '管理员组配置', value: 'admins' },
          { label: '权限管理', value: 'permissions' },
          { label: '围栏配置', value: 'filters' },
        ]"
        :item-width="105"
      />
    </div>

    <TabsContent
      value="admins"
      class="grow"
    >
      <SystemAdminConfigPage />
    </TabsContent>

    <TabsContent
      value="permissions"
      class="grow"
    >
      <SystemPermissionConfigPage />
    </TabsContent>

    <TabsContent
      value="filters"
      class="grow"
    >
      <SystemFilterConfigPage />
    </TabsContent>
  </TabsRoot>
</template>
