<template>
    <div class="" style="background: #F0F9FF;">
        <div class="flex top">
            <!-- <div class="flex items-center cursor-pointer w-[60px] text-[#333] ml-[20px]" @click="() => router.push('')">
                <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
                <span style="font-size: 14px;line-height: 14px;">返回</span>
            </div> -->
            <div class="title flex items-center justify-center">试卷批阅</div>
        </div>
        <div class="h-[90px]"></div>
        <a-spin :spinning="spinning" :class="spinning ? 'spin-container' : ''">
            <div class="exam flex exam-container mx-auto gap-[20]" v-if="examList.length > 0">
                <Answer :examList="examList" :isGrading="true" :totalMinutes="totalMinutes" />
                <QuestionsIndex :examList="examList" :isGrading="true" :examInfo="examInfo" :end_time="end_time" :total_score="total_score" 
                :userInfo="userInfo" @submitExam="submitExam" />
            </div> 
        </a-spin>
        
    </div>
</template>

<script lang="ts" setup name="Exam">
// import { emitter } from '@/utils/mitter';
import { examDetail, publishExamQuestion } from '@/services/api/exam'
import Answer from '@/components/exam/answer.vue'
import Examtime from '@/components/exam/examtime.vue'
import QuestionsIndex from '@/components/exam/questions-index.vue'
import { formatDateTime } from '@/utils/util'

import { ref, reactive,onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const totalMinutes = ref(0)
import { message } from 'ant-design-vue'
import {env} from '@/../env'

//考试信息
const examInfo = ref<any>({})
const examList = ref<any[]>([])
const userInfo = ref<any>({})
const total_score = ref(0)
const end_time = ref('')
const user_take_exam = ref()
const spinning = ref(true)
//获取考试详细信息
onMounted(async () => { 
    // spinning.value = false
    const data = await examDetail(route.query.id) // 作业id
    spinning.value = false
    examInfo.value = data.data.exam_info
    examList.value = data.data.questions.map((item: any) => { 
        if(item.content.type == '多选题'){
            item.feedback = item.feedback==null?'': item.feedback.split(',')
        }
        if(item.content.type == '填空题'){
            item.content.answer.map((it:any,index:number) => {
                it.value =  item.feedback==null?'': item.feedback.split(',')[index]
            })
        }
        return item
    })
    userInfo.value = data.data.user_info
    end_time.value = data.data.end_time
    total_score.value = data.data.total_score
    user_take_exam.value = data.data.user_take_exam
})

function submitExam() { 
    const totalScore = examList.value.reduce((sum: number, item: any) => {
        return sum + item.get_score;
    },0)
    const params = {
        course_id:getCourse().id,
        semester_id:getCourse().semestername.value,
        exam_info:examInfo.value,
        user_info:userInfo.value,
        questions:examList.value,
        end_time:end_time.value,
        total_score:totalScore,
        user_take_exam:user_take_exam.value
    }
    console.log(params,'www')
    publishExamQuestion(params).then((res:any) => {
        message.success(res.message || '发布成功')
        setTimeout(()=>{
            emitter.emit('refreshHomeworkList')
            window.close()
        },2000)
        // window.location.href = `/homework/grade/?examid=${res.data.exam_id}&examname=${examInfo.value.exam_name}`
    }).catch(err => { 
        console.log(err)
        message.error(err)
        // window.location.href = `/homework/grade/?examid=${examInfo.value.exam_id}&examname=${examInfo.value.exam_name}`
    })
}






</script>
<style lang="scss" scoped>
.top {
    height: 60px;
    opacity: 1;
    background: #FFFFFF;
    box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
    position: fixed;
    width: 100%;
    // z-index: 9999;??
}

.title {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 12px;
    width: calc(100% - 60px - 20px);
}

.exam {
    // background-image: url('@/assets/image/quetion/aishengcbg.png');
    height: calc(100vh - 90px);
    background-repeat: no-repeat;
    background-size: cover;
    // overflow: auto;
}
.spin-container {
    height: calc(100vh - 90px); // 或者根据你的页面内容动态设置高度
    display: flex;
    align-items: center;
    justify-content: center;
}


.exam-container {
  margin-left: auto;
  margin-right: auto;
  max-width: calc(100% - 440px); /* 默认值（小屏） */
}

@media (min-width: 1920px) {
  .exam-container {
    max-width: calc(100% - 520px); /* 大屏 */
  }
}
</style>
