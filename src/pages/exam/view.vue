<template>
    <div class="" style="background: #F0F9FF;">
        <div class="flex top">
            <div class="title flex items-center justify-center">答卷结果</div>
        </div>
        <div class="h-[90px]"></div>

        <a-spin :spinning="spinning" :class="spinning ? 'spin-container' : ''">
            <div class="exam flex exam-container mx-auto gap-[20]" v-if="examList.length > 0">
                <Answer :examList="examList" :isGrading="true" :totalMinutes="totalMinutes" @submitExam="submitExam" />
                <QuestionsIndex :examList="examList" :isGrading="true" :type="'stuView'" :submit="submitStatus"
                    :end_time="end_time" :examInfo="examInfo" :userInfo="userInfo" @submitExam="submitExam" />
            </div>
        </a-spin>
    </div>
</template>

<script lang="ts" setup name="Exam">
// import { emitter } from '@/utils/mitter';
import { takePartExamStudentAnswer, takePartExamSubmit } from '@/services/api/exam'
import Answer from '@/components/exam/answer.vue'
import Examtime from '@/components/exam/examtime.vue'
import QuestionsIndex from '@/components/exam/questions-index.vue'
import { formatDateTime } from '@/utils/util'

import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const totalMinutes = ref(0)
import { message } from 'ant-design-vue'
import { env } from '@/../env'
const spinning = ref(false)
//是否提交
const submitStatus = ref(false)

//考试信息
const examInfo = ref<any>({})
const examList = ref<any[]>([])
const end_time = ref()
const userInfo = ref<any>({})
const user_take_exam = ref()

//获取考试详细信息
onMounted(async () => {
    console.log(route, 'sssssssss')
    spinning.value = true
    const data = await takePartExamStudentAnswer(route.query.id) // 作业id
    examInfo.value = data.data.exam_info
    // examList.value = data.data.questions
    examList.value = data.data.questions.map((item: any) => {
        if (item.content.type == '多选题') {
            item.feedback = item.feedback == null ? '' : item.feedback.split(',')
        }
        if (item.content.type == '填空题') {
            item.content.answer.map((it: any, index: number) => {
                it.value = item.feedback == null ? '' : item.feedback.split(',')[index]
            })
        }
        return item
    })
    userInfo.value = data.data.user_info
    totalMinutes.value = data.data.exam_info.duration
    user_take_exam.value = data.data.user_take_exam
    end_time.value = data.data.end_time
    spinning.value = false

    //接受改变的试题数据
    emitter.on('answerhandleChange', (data: any) => {
        const index = examList.value.findIndex(item => item.question_version_id === data.question_version_id);
        if (index !== -1) {
            examList.value[index] = data;
        }
    })
})



const isComplete = ref(true)

// 交卷
async function submitExam() {

    const submitExamList = examList.value.map((item: any) => {
        let newItem = { ...item }; // 创建原对象的浅拷贝
        if (newItem.content.type === '多选题') {
            newItem.feedback = Array.isArray(newItem.feedback) ? newItem.feedback.join(',') : newItem.feedback;
        } else if (newItem.content.type === '填空题') {
            newItem.feedback = (newItem.content.answer || []).map((it: any) => it.value).join(',');
        }
        return newItem;
    })
    isComplete.value = submitExamList.some(item => item.feedback == '' || item.feedback == null)
    // if (isComplete.value) {
    //     message.warning('还有题目未完成')
    //     return
    // }
    const params = {
        exam_info: examInfo.value,
        user_info: userInfo.value,
        questions: submitExamList,
        end_time: formatDateTime(Date.now()),
        user_take_exam: user_take_exam.value,
    }
    console.log(params, 'params')
    try {
        const res = await takePartExamSubmit(params)
        message.success('提交成功')
        submitStatus.value = true
        // router.push('/')
        // window.open(`/homework/student`, '_blank');
        window.close()
    } catch (error) {
        console.log(error)
    }

}

onUnmounted(() => {
    emitter.off('answerhandleChange')
})


</script>
<style lang="scss" scoped>
.top {
    height: 60px;
    opacity: 1;
    background: #FFFFFF;
    box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
    position: fixed;
    width: 100%;
    // z-index: 9999;??
}

.title {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 12px;
    // width: calc(100% - 60px - 20px);
    width: 100%;
}

.exam {
    // background-image: url('@/assets/image/quetion/aishengcbg.png');
    height: calc(100vh - 90px);
    background-repeat: no-repeat;
    background-size: cover;
    // overflow: auto;
}

.spin-container {
    height: calc(100vh - 90px); // 或者根据你的页面内容动态设置高度
    display: flex;
    align-items: center;
    justify-content: center;
}

.exam-container {
  margin-left: auto;
  margin-right: auto;
  max-width: calc(100% - 220px); /* 默认值（小屏） */
}

@media (min-width: 1920px) {
  .exam-container {
    max-width: calc(100% - 520px); /* 大屏 */
  }
}
</style>
