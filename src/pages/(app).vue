<script setup lang="ts">
  import { message } from 'ant-design-vue'

  import { appSiderInjectionKey } from '@/components/app/AppSider.vue'

  const isSiderCollapsed = ref(false)
  provide(appSiderInjectionKey, {
    isSiderCollapsed: readonly(isSiderCollapsed),
    setSiderCollapsed(value) {
      isSiderCollapsed.value = value
    },
  })

  const windowSize = useWindowSize()
  const triggerX = computed(() => 0.95 * windowSize.width.value)
  const triggerY = computed(() => 0.9 * windowSize.height.value)

  const userStore = useUserStore()
  const { error } = await userStore.suspense()
  if (error) {
    message.error(error.message)
  } else {
    userStore.removeRouteByRole()
  }
</script>

<template>
  <ALayout class="h-screen max-h-screen bg-transparent!">
    <AppSider v-model:collapsed="isSiderCollapsed" />

    <ALayoutContent class="relative! h-screen! overflow-auto!">
      <RouterView v-slot="{ Component }">
        <Transition
          mode="out-in"
          enter-active-class="animate-in fade-in ease-out"
          leave-active-class="animate-out fade-out ease-out"
        >
          <Suspense>
            <component :is="Component" />
          </Suspense>
        </Transition>
      </RouterView>
    </ALayoutContent>

    <ChatFloatButton
      :x="triggerX"
      :y="triggerY"
    >
      <template #iframe="{ center }">
        <ChatFloatIframe :origin="center" />
      </template>
    </ChatFloatButton>

    <AppUserInfo class="absolute! top-10 right-6" />
  </ALayout>
</template>
