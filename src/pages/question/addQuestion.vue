<template>
    <div class="main1">
        <div class="addQuestion">
            <el-radio-group v-model="radio">
                <el-row>
                    <el-col :span="24" v-if="isType === 'add' || typeQuestion == '单选题'">
                        <el-radio :value="1">单选题</el-radio>
                    </el-col>
                    <el-col :span="24" v-if="isType === 'add' || typeQuestion == '多选题'">
                        <el-radio :value="2" style="height: 100px;">多选题</el-radio>
                    </el-col>
                    <el-col :span="24" v-if="isType === 'add' || typeQuestion == '判断题'">
                        <el-radio :value="3">判断题</el-radio>
                    </el-col>
                    <el-col :span="24" v-if="isType === 'add' || typeQuestion == '填空题'">
                        <el-radio :value="4" style="height: 100px;">填空题</el-radio>
                    </el-col>
                    <el-col :span="24" v-if="isType === 'add' || typeQuestion == '问答题'">
                        <el-radio :value="5">问答题</el-radio>
                    </el-col>
                </el-row>
            </el-radio-group>
        </div>

        <div class="select" v-if="isType === 'add'">
            <singleSelect :questionDetails="questionDatails" :isType="isType" v-if="radio == 1"></singleSelect>
            <multipleSelect :questionDetails="questionDatails" :isType="isType" v-if="radio == 2"></multipleSelect>
            <judgeSelect :questionDetails="questionDatails" :isType="isType" v-if="radio == 3"></judgeSelect>
            <fillEmpty :questionDetails="questionDatails" :isType="isType" v-if="radio == 4"></fillEmpty>
            <essay :questionDetails="questionDatails" :isType="isType" v-if="radio == 5"></essay>
        </div>

        <div class="select" v-if="isType === 'edit'">
            <singleSelect :questionDetails="questionDatails" :isType="isType" v-if="Object.keys(questionDatails).length&&radio == 1"></singleSelect>
            <multipleSelect :questionDetails="questionDatails" :isType="isType" v-if="Object.keys(questionDatails).length&&radio == 2"></multipleSelect>
            <judgeSelect :questionDetails="questionDatails" :isType="isType" v-if="Object.keys(questionDatails).length&&radio == 3"></judgeSelect>
            <fillEmpty :questionDetails="questionDatails" :isType="isType" v-if="Object.keys(questionDatails).length&&radio == 4"></fillEmpty>
            <essay :questionDetails="questionDatails" :isType="isType" v-if="Object.keys(questionDatails).length&&radio == 5"></essay>
        </div>
    </div>
</template>
<script lang="ts" setup>
import singleSelect from '@/components/question/singleSelect.vue'; // 引入单选题组件
import fillEmpty from '@/components/question/fillEmpty.vue'; // 引入填空题组件
import multipleSelect from '@/components/question/multipleSelect.vue'; // 引入多选题组件
import judgeSelect from '@/components/question/judgeSelect.vue';
import essay from '@/components/question/essay.vue';
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { selectQuestion } from '@/services/api/question'



const radio = ref(1)
const router = useRoute()
const questionId = ref(router.query.qid)
const questionDatails = ref({})
const isType = ref('add')//判断是查看还是编辑 add edit
const typeQuestion = ref(router.query.type)

onMounted( async () => {
    //判断是什么题型
    if (typeQuestion.value == '单选题') {
        radio.value = 1
    } else if (typeQuestion.value == '多选题') {
        radio.value = 2
    } else if (typeQuestion.value == '判断题') {
        radio.value = 3
    } else if (typeQuestion.value == '填空题') {
        radio.value = 4
    } else if (typeQuestion.value == '问答题') {
        radio.value = 5
    }
    
    //获取题目详情信息
    if (!questionId.value) {
        isType.value = 'add'
        return
    }else{
        isType.value = 'edit'
    }
    
    const res = await selectQuestion(questionId.value)
    questionDatails.value = res.data
})


</script>
<style lang="scss" scoped>
.main1{
    display: flex;
    padding: 40px;
}
.addQuestion {
    padding:0 20px;
    // border: 1px solid #ccc;
    border-radius: 4px;
    width: 15%;
    text-align: center;
    flex-shrink: 0;
}
.select{
    // border: 1px solid #000;
    width: 60%;
}
</style>