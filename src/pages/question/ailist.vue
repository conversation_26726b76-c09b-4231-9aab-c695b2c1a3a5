<template>
    <div class="containerq">
        <div class="flex top">
            <div class="back-btn" @click="() => router.go(-1)">
                <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
                <span style="font-size: 14px;line-height: 14px;">返回</span>
            </div>
            <div class="title flex items-center justify-center pr-[80px]">AI生成</div>
        </div>

        <div class="quetionai">
            <div class="flex contentai">
                <div class="hide-scrollbar flex flex-col justify-between w-[580px]  h-[calc(100vh-60px-30px-30px)] overflow-auto rounded-[5px] bg-[#fff] shadow-[0px_2px_19px_rgba(29,_79,_153,_0.05)] p-[20px_30px_0px_30px]">
                    <div class="flex flex-col">
                        <div class="setting">出题设置</div>
                        <a-select v-model:value="params.knowledge_point" show-search placeholder="知识点选择"
                            :options="konwledgeList" style="margin-top: 20px;" size="large" mode="tags"></a-select>
                        <div style="width: 100%;" class="mt-[20] upload-demo" v-if="type != '3'">
                            <div class="text-[#666] text-[16px] font-medium leading-[22px]">参考资料

                                <span></span>
                            </div>
                            <div class="mt-[10px] relative">
                                <a-upload :file-list="fileList" :before-upload="beforeUpload" :max-count="1"
                                    @remove="handleRemove" :multiple="false" accept=".doc,.docx">
                                    <div class=" h-[52px]">
                                        <img
                                            src="@/assets/image/quetion/ziliaoupload.png"
                                            class="absolute left-1/2 -translate-x-1/2 w-[56px] h-[52px]" 
                                        />
                                    </div>
                                </a-upload>
                            </div>
                        </div>

                        <div class="flex gap-[20px] mt-[20] flex-wrap">
                            <a-input-number id="inputNumber" v-model:value="params.single_question_count" :min="0"
                                :max="10">
                                <template #prefix>
                                    <div class="numchoose">单选题</div>
                                </template>
                            </a-input-number>
                            <a-input-number id="inputNumber" v-model:value="params.multiple_question_count" :min="0"
                                :max="10">
                                <template #prefix>
                                    <div class="numchoose">多选题</div>
                                </template>
                            </a-input-number>
                            <a-input-number id="inputNumber" v-model:value="params.boolean_question_count" :min="0"
                                :max="10">
                                <template #prefix>
                                    <div class="numchoose">判断题</div>
                                </template>
                            </a-input-number>
                            <a-input-number id="inputNumber" v-model:value="params.blank_filling_question_count"
                                :min="0" :max="10">
                                <template #prefix>
                                    <div class="numchoose">填空题</div>
                                </template>
                            </a-input-number>
                            <a-input-number id="inputNumber" v-model:value="params.faq_question_count" :min="0"
                                :max="10">
                                <template #prefix>
                                    <div class="numchoose">简答题</div>
                                </template>
                            </a-input-number>
                        </div>


                        <a-select v-model:value="params.difficulty" placeholder="难度" class="leading-[48px]!"
                            :options="optionsdifficulty" style="margin-top: 20px;" size="large"></a-select>

                        <div class="text-[16px] font-medium text-[#666] mt-[40px] leading-[22px]">考核目标</div>
                        <div class="mt-[20] flex flex-wrap gap-[20]">
                            <div class="relative p-[2px] rounded-lg cursor-pointer" v-for="i in iconList"
                                @click="handleDirection(i.name)"
                                :class="params.direction == i.name ? ' bg-gradient-to-r from-[#4794FE] to-[#15C0E6]' : 'bg-[#E6EFFF]'">
                                <div
                                    class="bg-white w-[111] h-[50]  rounded-[5] flex items-center justify-center gap-[12] ">
                                    <img :src="i.icon" style="width: 20px;height: 20px;" />
                                    <div class="text-base text-[#666] font-medium">{{ i.name }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=" flex justify-end mb-[30px]" @click="createQuetionAi">
                        <a-button type="primary" class="fixed" style="background: linear-gradient(90deg, #3F8CFF 0%, #15C0E6 100%);border-radius: 150px;color: #fff;width: 92px;height: 32px;">
                            <div class="flex items-center justify-center gap-[10px]">
                                <img src="@/assets/image/img/magicIcon.png" class="w-[14px] h-[14px]" />
                                生成
                            </div>
                        </a-button>
                    </div>
                </div>

                <div class="rightai shadow-[0px_2px_19px_rgba(29,_79,_153,_0.05)]">
                    <div class="flex justify-between items-center">
                        <div class="setting">生成结果</div>
                        <a-button type="primary" :disabled="questionList.length==0" class="gradient-a-button rounded-full!" @click="addQuestionBank">
                            {{ type == '1' ? '一键加入题库' : '一键加入作业'}}
                        </a-button>
                    </div>
                    <div v-if="questionList.length > 0">
                        <div class="mt-[20] text-[14] text-[#666666]">
                            共<span>{{ questionList.length }}</span>道题目
                        </div>
                        <div style="height: calc(100vh - 244px);" class="overflow-auto">
                            <aiItem :itemQuetione="questionList" @deleteQue="delQuestion"   @editQue="editQuetion" :isEdit="type == '3'?false:true" />
                        </div>
                    </div>

                    <div class="w-[100%] flex items-center justify-center h-[100%]" v-if="questionList.length == 0 && !loading">
                        <img src="@/assets/image/quetion/kongai.png"
                            style="width: 341px;height: 151px;" class="mb-[60px]" />
                    </div>

                    <div v-if="loading" class="w-[100%] flex items-center justify-center h-[100%] flex-col mb-[60px]">
                        <img src="@/assets/image/quetion/ailoading.svg" style="width: 281px;height: 121px;"  class="" />
                        <div class="text-[#666666] text-[16px] text-center mt-[44]">正在生成中，请稍后...</div>
                    </div>
                </div>
            </div>
        </div>


        <EditQuetion :open="showEdit" :editType="'isAiEdit'"  :quetionDetails="quetionDetails" @update:open="val => showEdit = val"  @update:success="getquestionList"/>

    </div>
</template>
<script lang="ts" setup name="List">
import jiyi from '@/assets/image/quetion/aiMubiaoIcon/icon1.png'
import liji from '@/assets/image/quetion/aiMubiaoIcon/icon2.png'
import yingyong from '@/assets/image/quetion/aiMubiaoIcon/icon3.png'
import chuangzao from '@/assets/image/quetion/aiMubiaoIcon/icon4.png'
import fenxi from '@/assets/image/quetion/aiMubiaoIcon/icon5.png'
import pingjia from '@/assets/image/quetion/aiMubiaoIcon/icon6.png'

const iconList = [
    { name: '记忆', icon: jiyi },
    { name: '理解', icon: liji },
    { name: '应用', icon: yingyong },
    { name: '创造', icon: chuangzao },
    { name: '分析', icon: fenxi },
    { name: '评价', icon: pingjia }
]

import { emitter } from '@/utils/mitter'

import { useRoute } from 'vue-router'
const route = useRoute()
const type = route.query.type; //获取参数 1为从题库进入AI生成，加入题库   2为从作业进入AI生成，加入作业以及题库  3 //创建作业时候，选择AI生成作业后，进入AI生成
import { knowledgeList } from '@/services/api/course';
import { aiQuestions,batchCreate,aiWorkQuestions } from '@/services/api/question'
import aiItem from '@/components/question/aiItem.vue';
import { reactive, onMounted, ref, watch, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()

import { useQuestionStore } from '@/stores/question'
const questionStore = useQuestionStore() //将选择的试题先保存到store中

import { message } from 'ant-design-vue';
import type { UploadProps } from 'ant-design-vue';
const fileList = ref();
const beforeUpload: UploadProps['beforeUpload'] = file => {
    console.log(fileList.value,'file')
    fileList.value = [...(fileList.value || []), file];
    console.log(fileList.value,'fileList')
    params.documents = fileList.value;
    console.log(params.documents, 'params.documents.value')
    return false;
};
const handleRemove: UploadProps['onRemove'] = file => {
    const index = fileList.value?.indexOf(file);
    const newFileList = fileList.value?.slice() || [];
    if (typeof index === 'number' && index != -1) {
        newFileList.splice(index as number, 1);
    }
    fileList.value = newFileList as UploadProps['fileList'];
    params.documents = fileList.value;
};

interface Option {
    value: number;
    label: string;
}
const konwledgeList = ref<Option[]>([])
function getknowledgeList() {
    const params = {
        course_id: getCourse().id
    }
    console.log(params, 'params')
    knowledgeList(params).then((data:any) => {
        data.data.forEach((item: any) => {
            konwledgeList.value.push({
                value: item.id,
                label: item.name
            })
        })
    })
}


//难度
const optionsdifficulty = [
    {
        value: 0,
        label: '简单',
    },
    {
        value: 1,
        label: '中等',
    },
    {
        value: 2,
        label: '困难',
    }
]

interface ExamParams {
    knowledge_point: any[]; // 知识点
    single_question_count: number; // 单选题数量
    multiple_question_count: number; // 多选题数量
    boolean_question_count: number; // 判断题数量
    blank_filling_question_count: number; // 填空题数量
    faq_question_count: number; // 主观题数量
    difficulty: number; // 难度
    direction: string; // 考核目标
    documents: File | File[] | string; // 明确只接受文件数组
}
//AI生成试题的参数
const params = reactive<ExamParams>({
    knowledge_point: [],//知识点
    documents: [],//文档
    single_question_count: 0,//单选题数量
    multiple_question_count: 0,//多选题数量
    boolean_question_count: 0,//判断题数量
    blank_filling_question_count: 0,//填空题数量
    faq_question_count: 0,//主观题数量
    difficulty: 0,//难度
    direction: '记忆',//考核目标
})
const loading = ref(false)
const questionList = ref([]) // 题目列表
function createQuetionAi() {
    //生成之前清空数据
    questionList.value = []

    if(type == '3'){//抽题
        const data = {
            "knowledge_point_ids": params.knowledge_point,  // 传递知识点id
            "question_counts":{
                "单选题":params.single_question_count,
                "多选题":params.multiple_question_count,
                "判断题":params.boolean_question_count,
                "填空题":params.blank_filling_question_count,
                "问答题":params.faq_question_count
            },  //传入试题类型以及数量
            "difficulty": params.difficulty, //试题难度，可选((0, '简单'), (1, '中等'), (2, '困难')),
            "direction":  params.direction,  //考核目标
        }
        const total = params.single_question_count+params.multiple_question_count+params.boolean_question_count+params.blank_filling_question_count+params.faq_question_count
        loading.value = true
        aiWorkQuestions(data).then((res:any)=>{
            console.log(res,'res')
            loading.value = false
            if (res.code == 200) {
                console.log(res.data.details,res.data.count,total,'res.data.details')
                questionList.value = res.data.question
                if(res.data.count < total ){
                    message.warning(res.data.warning)
                }else{
                    message.success(res.message)
                }
            }
        })
        return
    }
    
    // ai生题目
    const total = params.single_question_count+params.multiple_question_count+params.boolean_question_count+params.blank_filling_question_count+params.faq_question_count
    if(total==0){
        message.error('最少生成一道试题！')
        return
    }else if(total>100){
        message.error('最多生成一百道试题！')
        return
    }
    const formData = new FormData()
    formData.append('knowledge_point_ids', JSON.stringify(params.knowledge_point))
    if (params.documents instanceof File) {
        formData.append('documents', params.documents);
    } else if (Array.isArray(params.documents)) {
        params.documents.forEach(file => {
            formData.append('documents', file);
        });
    } else {
        formData.append('documents', params.documents);
    }
    formData.append('single_question_count', JSON.stringify(params.single_question_count))
    formData.append('multiple_question_count', JSON.stringify(params.multiple_question_count))
    formData.append('boolean_question_count', JSON.stringify(params.boolean_question_count))
    formData.append('blank_filling_question_count', JSON.stringify(params.blank_filling_question_count))
    formData.append('faq_question_count', JSON.stringify(params.faq_question_count))
    formData.append('difficulty', JSON.stringify(params.difficulty))
    formData.append('direction', params.direction)
    loading.value = true
    aiQuestions(formData).then((res: any) => {
        loading.value = false
        if (res.code === 200) {
            questionList.value = res.data.map((item: any) => { 
                return{
                    ...item,
                    deleteShow: false
                }
            })
        }
        message.success(res.message)
        console.log(res)
    }).catch((err: any) => {
        loading.value = false
        message.error(err.message)
    })
}

const handleDirection = (e: any) => {
    if (params.direction == e) {
        params.direction = ''
        return
    }
    params.direction = e
}

//接受改变的试题数据
emitter.on('changeQuestion', (data: any) => {
    console.log(data, '接受改变的试题数据')
    showEdit.value = false
    questionList.value = questionList.value.map((questionItem: any) => {
        if (questionItem.aiid === data.aiid && questionItem.type === data.type) {
            return data;
        }else{
            return questionItem
        }
    })as any;
    message.success('修改成功')
})
onUnmounted(() => {
    emitter.off('changeQuestion')
})

//加入题库

function addQuestionBank() {
    if(type == '3'){
        questionStore.setSelectedQuestions(questionList.value)
        questionStore.setSelectedQuestionIds(new Set(questionList.value.map((item: any) => item.id)))
        router.replace('/homework/handquetion')
        return
    }
    console.log('加入题库', questionList.value)
    const params = {
        data: questionList.value
    }
    batchCreate(params).then((res: any) => {
        console.log(res,'res加入题库成功！')
        if(res.code === 200){
            message.success(res.message || '加入题库成功！');
            questionList.value = []
            if(type == '2'){
                const queslist = res.data.results.map((item: any) => item.content)
                questionStore.setAiSelectedQuestions(queslist)
                const ids = queslist.map((item: any) => item.id)
                questionStore.setAiSelectedQuestionIds(ids)
                router.back()
            }
        }
    })
    
    
}


//点击编辑弹窗
const showEdit = ref(false)
const quetionDetails = ref() //编辑试题的数据
function editQuetion(item: any) {
    console.log('编辑',item);
    quetionDetails.value = item
    showEdit.value = true
}
function delQuestion(item: any) {
    questionList.value = questionList.value.filter((question: any) => question.aiid !== item.aiid || question.type !== item.type);
    message.success('删除成功')
}
function getquestionList() {
    // console.log('修改成功');
}

getknowledgeList()

</script>
<style lang="scss" scoped>
:deep(.custom-upload .ant-upload-list) {
    text-align: right !important;
}
:deep(.ant-upload-list){
    max-height: 65px !important;
    overflow: auto !important;
    scrollbar-color: #C7DDFC transparent;
//  scrollbar-width: none;
//  -ms-overflow-style: none;
}
:deep(.ant-select-selector){
    height: 50px !important;
    padding-top: 3px !important;
    border-radius: 5px !important;
    border: 1.5px solid #e6efff !important;
    font-size: 16px !important;
    color: #666 !important;
    
}


:deep(.ant-input-number-input) {
    height: 50px;
    text-align: center;
}

:deep(.ant-input-number-affix-wrapper) {
    width: 160px;
    height: 50px;
    border: 1.5px solid #e6efff;
    border-radius: 5px !important;
}

.h-custom-height {
    height: 50px;
}


.back-btn {
    cursor: pointer;
    width: 60px;
    color: #333;
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.title {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 12px;
    width: calc(100% - 60px - 20px);
}

.containerq {
    background: rgba(240, 249, 255, 1);
    min-height: 100vh;
    width: 100%;
    overflow: auto;
}

.top {
    height: 60px;
    opacity: 1;
    background: #FFFFFF;
    box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
    position: fixed;
    width: 100%;
    // z-index: 9999;
}

.quetionai {
    background-image: url('@/assets/image/quetion/aishengcbg.png');
    height: calc(100vh - 60px);
    margin-top: 60px;
    background-repeat: no-repeat;
    background-size: cover;
    overflow: auto;

    .contentai {
        display: flex;
        justify-content: center;
        margin: 30px 0 0;
        // min-width: 1380px;

    }

    .leftai {
          width: 580px;
        // min-height: 830px;
        // max-height: 960px;

        height: calc(100vh - 60px - 30px - 30px);
        overflow: auto;
        opacity: 1;
        border-radius: 6.33px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
        padding: 20px 30px 0px 30px; 
        display: flex;
        flex-direction: column;

    }

    .rightai {
        margin-left: 20px;
        width: 800px;
        height: calc(100vh - 60px - 30px - 30px);
        opacity: 1;
        background: rgba(255, 255, 255, 1);
        padding: 20px 30px 30px 30px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border-radius: 5px;
    }

    .setting {
        font-size: 16px;
        font-weight: 700;
        line-height: 16px;
    }

    .upload-demo {
        border-radius: 5px;
        // height: 163px;
        width: 100%;
        border: 1.5px solid #e6efff;
        padding: 14px 31px 31px 31px;
        display: flex;
        flex-direction: column;
    }

    .numchoose {
        font-size: 16px;
        font-weight: 500;
        letter-spacing: 0px;
        line-height: 21.95px;
        border-right: 1px solid rgba(229, 229, 229, 1);
        padding-right: 10px;
        color: #666;
    }
}
</style>