import { uniq } from 'lodash-es'

import { env } from '@/../env'

export function useModelList({ categories }: { categories?: MaybeRefOrGetter<string[]> } = {}) {
  return {
    queryOptions: queryOptions({
      queryKey: ['model', 'list', { categories }],
      async queryFn() {
        const { items } = await modelsFetcher<{ items: API.Models.Model[] }>('/models/', {
          query: { categories: toValue(categories) },
        })
        return items
      },
    }),
  }
}

export function useModelInstanceList({ modelId }: { modelId: MaybeRefOrGetter<string> }) {
  return {
    queryOptions: queryOptions({
      queryKey: ['model', modelId, 'instance', 'list'],
      async queryFn() {
        const { items } = await modelsFetcher<{ items: API.Models.ModelInstance[] }>(
          `/models/${toValue(modelId)}/instances/`,
        )
        return items
      },
    }),
  }
}

export function useOpenaiModelList({
  categories,
}: { categories?: MaybeRefOrGetter<string[]> } = {}) {
  return {
    queryOptions: queryOptions({
      queryKey: ['model', 'list', { categories }, 'openai'],
      async queryFn() {
        const { data } = await modelsFetcher<{ data: API.Models.OpenAIModel[] }>('/models/', {
          baseURL: `${env.VITE_API_BASE_URL}/v1/v1-openai`,
          query: { categories: toValue(categories) },
        })
        return data
      },
    }),
  }
}

export function useModelFilePaths() {
  return {
    queryOptions: queryOptions({
      queryKey: ['model', 'file-paths'],
      async queryFn() {
        const { items } = await modelsFetcher<{ items: API.Models.ModelFile[] }>('/model-files/')
        return uniq(items.map((item) => item.local_path))
      },
    }),
  }
}

export function useModelsInvalidation() {
  const queryClient = useQueryClient()

  return {
    allModels: () => queryClient.invalidateQueries({ queryKey: ['model'] }),
  }
}
