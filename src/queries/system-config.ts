export const systemConfigQueryKey = {
  adminList: ({
    page,
    pageSize,
    search,
  }: {
    page: MaybeRefOrGetter<number>
    pageSize: MaybeRefOrGetter<number>
    search: MaybeRefOrGetter<string | undefined>
  }) => ['system', 'admins', 'list', { page, pageSize, search }],
  filterWordList: ({ search }: { search: MaybeRefOrGetter<string | undefined> }) => [
    'system',
    'filter-words',
    'list',
    { search },
  ],
  permissionList: () => ['system', 'permissions', 'list'],
}
