export const kbQ<PERSON><PERSON><PERSON>ey = {
  kbList: () => ['kb', 'list'],
  kbDetail: (kbId: MaybeRefOrGetter<string>) => ['kb', kbId, 'detail'],
  kbFiles: (kbId: MaybeRefOrGetter<string>) => ['kb', kbId, 'files'],
  kbChunks: (
    kbIds: MaybeRefOrGetter<string[]>,
    query: MaybeRefOrGetter<string>,
    page: MaybeRefOrGetter<number>,
  ) => ['kb', 'chunks', { kbIds, query, page }],
}

export function useKbList() {
  return {
    queryOptions: queryOptions({
      queryKey: ['kb', 'list'],
      async queryFn() {
        const { data } = await kbFetcher<{ data: { data: API.Kb.Kb[] } }>('/datasets/', {
          query: {
            page_size: 9999,
            source: 'local',
          },
        })
        return data.data
      },
    }),
  }
}

export function useKbDetail({ kbId }: { kbId: MaybeRefOrGetter<string> }) {
  return {
    queryOptions: queryOptions({
      queryKey: ['kb', kbId, 'detail'],
      async queryFn() {
        const { data } = await kbFetcher<{ data: { data: API.Kb.Kb[] } }>('/datasets/', {
          query: {
            id: toValue(kbId),
            source: 'external',
          },
        })
        return data.data[0]
      },
    }),
  }
}

export function useKbFiles({ kbId }: { kbId: MaybeRefOrGetter<string> }) {
  return {
    queryOptions: queryOptions({
      queryKey: ['kb', kbId, 'files'],
      async queryFn() {
        const { data } = await kbFetcher<{ data: { data: { docs: API.Kb.File[] } } }>(
          `/datasets/${toValue(kbId)}/documents/`,
          {
            query: {
              page_size: 99999,
            },
          },
        )
        return data.data.docs
      },
    }),
  }
}

export function useKbChunks({ kbId }: { kbId: MaybeRefOrGetter<string> }) {
  const query = ref('')
  const page = ref(1)

  return {
    query,
    page,
    queryOptions: queryOptions({
      queryKey: ['kb', kbId, 'chunks', { query, page }],
      async queryFn() {
        const { data } = await kbFetcher<{
          data: {
            chunks: API.Kb.RetrievalChunk[]
            total: number
          }
        }>('/chunk-retrievals/', {
          method: 'post',
          body: {
            question: toValue(query),
            dataset_ids: [toValue(kbId)],
            page: toValue(page),
            page_size: 7,
            highlight: true,
          },
        })

        return data
      },
    }),
  }
}

export function useKbQas({ kbId }: { kbId: MaybeRefOrGetter<string> }) {
  const search = ref('')

  return {
    search,
    queryOptions: queryOptions({
      queryKey: ['kb', kbId, 'qas', { search }],
      async queryFn() {
        const { data } = await kbFetcher<{ data: API.Kb.QaChunk[] }>(
          `/datasets/${toValue(kbId)}/documents/all/chunks/`,
          {
            query: {
              source: 'pg',
              question: toValue(search),
              page_size: 9999,
            },
          },
        )

        return data
      },
    }),
  }
}

export function useKbInvalidation() {
  const queryClient = useQueryClient()

  return {
    allKbs: () => queryClient.invalidateQueries({ queryKey: ['kb'] }),
    kbFiles: (kbId: string) => queryClient.invalidateQueries({ queryKey: ['kb', kbId, 'files'] }),
    kbQas: (kbId: string) => queryClient.invalidateQueries({ queryKey: ['kb', kbId, 'qas'] }),
  }
}
