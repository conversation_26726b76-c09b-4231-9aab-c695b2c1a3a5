export const courseQueryKey = {
  courseList: (semesterId: MaybeRefOrGetter<string>) => ['course', 'list', { semesterId }],
  courseDetail: (courseId: MaybeRefOrGetter<string>) => ['course', courseId],
}

type SemesterCourse = {
  semesterId: number
  semesterName: string
  courses: Array<{
    courseId: number
    courseName: string
  }>
}

export function useSemestersCourses() {
  const showInactive = ref(false)

  return {
    showInactive,
    queryOptions: queryOptions({
      queryKey: ['course', 'list', { showInactive }],
      async queryFn() {
        const { data } = await baseFetcher<{
          data: Array<{
            id: number
            course: { id: number; title: string }
            semester: { id: number; name: string }
            is_active: boolean
          }>
        }>('/course_semester/owner/')

        const semesters = new Map<number, SemesterCourse>()
        for (const relation of data) {
          if (toValue(showInactive) === false && relation.is_active === false) {
            continue
          }
          const s = semesters.get(relation.semester.id)
          if (s) {
            s.courses.push({
              courseId: relation.course.id,
              courseName: relation.course.title,
            })
          } else {
            semesters.set(relation.semester.id, {
              semesterId: relation.semester.id,
              semesterName: relation.semester.name,
              courses: [
                {
                  courseId: relation.course.id,
                  courseName: relation.course.title,
                },
              ],
            })
          }
        }

        return Array.from(semesters.values()).sort((a, b) => b.semesterId - a.semesterId)
      },
    }),
  }
}

export function useCourseRecommendations({ courseId }: { courseId: MaybeRefOrGetter<string> }) {
  return {
    queryOptions: queryOptions({
      queryKey: ['course', courseId, 'recommendations'],
      async queryFn() {
        const res = await baseFetcher<{
          错题详情: []
          知识点分析: []
          建议: [
            {
              课程资源推荐: []
              '学习/教学路径': [string[]]
              联网搜索: Array<{
                id: string
                name: string
                url: string
                displayUrl: string
                snippet: string
                summary: string
                siteName: string
                siteIcon: string
                datePublished: string
                dateLastCrawled: string
                cachedPageUrl: null
                language: null
                isFamilyFriendly: null
                isNavigational: null
              }>
            },
          ]
        }>(`/recommendation/`, {
          query: {
            course_id: toValue(courseId),
          },
          timeout: 1000 * 60 * 5,
        })

        return {
          advice: res.建议[0]['学习/教学路径'][0]?.at(-1),
          files: res.建议[0]['课程资源推荐'],
          web: res.建议[0]['联网搜索'],
        }
      },

      staleTime: 1000 * 60 * 60 * 8,
    }),
  }
}
