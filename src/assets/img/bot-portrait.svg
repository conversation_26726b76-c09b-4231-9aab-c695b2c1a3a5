<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="400" height="400" viewBox="0 0 400 400" fill="none">
<g filter="url(#filter_5_2)">
<path d="M156.741 334.177L157.192 297.524C158.994 276.574 165.851 236.783 179.356 234.523C179.452 234.507 179.549 234.5 179.646 234.5L222.127 234.5C222.684 234.5 223.211 234.724 223.581 235.14C232.27 244.918 245.5 268.651 245.5 297.524L246.841 333.882C247.092 340.678 241.65 346.324 234.849 346.324L218.528 346.324C212.504 346.324 207.414 341.859 206.63 335.886L202.782 306.569C202.673 305.743 201.969 305.124 201.135 305.124C200.265 305.124 199.542 305.796 199.478 306.663L197.368 335.209C196.905 341.476 191.685 346.324 185.401 346.324L168.74 346.324C162.055 346.324 156.659 340.861 156.741 334.177Z" fill="url(#linear_fill_5_3)">
</path>
</g>
<path d="M282.092 141C291.092 141 313.3 166.875 282.5 198.075C286.573 191.615 284.122 157.308 282.092 141Z" fill="url(#linear_fill_5_4)">
</path>
<path d="M118.191 141C109.19 141 86.9825 166.875 117.782 198.075C113.709 191.615 116.16 157.308 118.191 141Z" fill="url(#linear_fill_5_5)">
</path>
<g filter="url(#filter_5_6)">
<path d="M245.19 293.925C243.756 276.42 232.73 247.633 222.17 235.415C220.988 234.048 221.785 231.621 223.584 231.449L238.599 230.019C239.224 229.96 239.835 230.191 240.26 230.653C249.493 240.682 270.664 262.522 273.69 283.425C277.092 306.925 246.994 315.942 245.19 293.925Z" fill="url(#linear_fill_5_7)">
</path>
</g>
<path d="M168.6 255C172 247 178.6 237.1 181.6 234C175.6 233.6 164 232.4 160 231.2L151.468 237.894C149.291 239.602 149.464 242.952 151.806 244.426L168.6 255Z" fill="url(#linear_fill_5_8)">
</path>
<path d="M233.701 255C231 247.5 223.901 237 220.901 233.9C226.901 233.5 238.301 232.4 242.301 231.2L250.832 237.894C253.009 239.602 252.836 242.952 250.495 244.426L233.701 255Z" fill="url(#linear_fill_5_9)">
</path>
<path d="M201.666 284.253C227.481 259.53 212.893 256.287 200.797 256.035C188.106 255.531 170.841 260.266 199.869 284.317C200.384 284.744 201.183 284.716 201.666 284.253Z" fill="url(#linear_fill_5_10)">
</path>
<g filter="url(#filter_5_11)">
<path d="M238.316 64.4258C239.012 64.4898 239.713 64.3702 240.349 64.079L259.958 55.0935C262.278 54.0305 264.986 55.3821 265.532 57.875L270.05 78.5247C270.203 79.2266 270.543 79.8742 271.033 80.3994L285.472 95.866C287.223 97.7416 286.78 100.757 284.564 102.051L276.157 106.956C274.215 108.088 273.599 110.593 274.651 112.579C276.489 116.046 278.084 119.821 279.413 123.925C285.215 141.838 286.066 163.592 286.066 178.925C286.066 231.425 245.123 236.425 199.574 236.425C168.867 236.425 123.318 233.625 117.176 202.425C109.5 163.425 106.941 88.4249 187.291 77.9249C194.679 77.4848 201.925 77.4011 208.918 77.7774C211.445 77.9134 213.534 75.7686 213.246 73.2551L212.517 66.893C212.228 64.3718 214.33 62.2223 216.857 62.4545L238.316 64.4258Z" fill-rule="evenodd" fill="url(#linear_fill_5_12)">
</path>
</g>
<g filter="url(#filter_5_17)">
<g mask="url(#mask-5_14)">
<path d="M264.127 159.229C274.127 209.229 240.02 210.228 200.627 210.228C165.52 210.228 130.627 207.729 133.127 173.228C134.486 154.461 141.608 127.728 200.627 127.728C249.627 127.727 261.46 148.728 264.127 159.229Z" fill="url(#linear_fill_5_18)">
</path>
</g>
</g>
<g mask="url(#mask-5_14)" opacity="0.2">
<path d="M259.356 162.122C268.687 208.777 236.862 209.709 200.103 209.709C167.345 209.709 134.786 207.378 137.118 175.184C138.387 157.673 145.032 132.728 200.103 132.728C245.826 132.727 256.868 152.324 259.356 162.122Z" fill="url(#linear_fill_5_19)">
</path>
</g>
<g opacity="0.6" style="mix-blend-mode:soft-light">
<g mask="url(#mask-5_14)">
<path d="M226.123 95.6968L190.319 101.845C187.814 102.275 185.418 103.192 183.266 104.546L120.582 143.966C116.551 146.501 114.952 151.584 116.807 155.97L149.482 233.237C153.81 243.471 162.438 251.152 172.902 254.345L226.123 95.6968Z" fill-rule="evenodd" fill="url(#linear_fill_5_21)" fill-opacity="0.15">
</path>
</g>
</g>
<g mask="url(#mask-5_14)" opacity="0.2">
<ellipse cx="238.31103515625" cy="192.22802734375" rx="8" ry="3.5" fill="#FFFFFF">

                                    <animate attributeName="cx" values="238.31103515625;238.30999755859375;238.31100463867188" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="cy" values="192.22802734375;189.22998046875;192.22802734375" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></ellipse>
</g>
<g mask="url(#mask-5_14)" opacity="0.1">
<ellipse cx="161.00000016438207" cy="193.2924195055109" transform="rotate(-5.999999940167532 153 189.79241943359375)" rx="8.000000164382085" ry="3.5000000719171576" fill="#FFFFFF">

                                    <animate attributeName="cy" values="193.2924195055109;190.2899780992609;193.2924195055109" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animateTransform attributeName="transform" type="rotate" values="-5.999999940167532 153 189.79241943359375;-5.999999940167532 153 186.78997802734375;-5.999999940167532 152.99996948242188 189.79241943359375" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="cx" values="161.00000016438207;161.00000016438207;160.99996964680395" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></ellipse>
</g>
<g filter="url(#filter_5_24)">
<g mask="url(#mask-5_14)">
<rect x="164" y="154.72802734375" width="14" height="30" rx="7" fill="#FFFFFF">

                                    <animate attributeName="y" values="154.72802734375;159;154.72802734375" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="height" values="30;26;30" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></rect>
</g>
</g>
<g filter="url(#filter_6_69)">
<g mask="url(#mask-5_14)">
<rect x="222" y="154.72998046875" width="14" height="30" rx="7" fill="#FFFFFF">

                                    <animate attributeName="y" values="154.72998046875;159;154.72998046875" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="height" values="30;26;30" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></rect>
</g>
</g>
<g filter="url(#filter_5_29)">
<path d="M195.959 335.476C196.65 336.935 196.761 338.636 196.04 340.081C194.064 344.039 189.977 346.724 185.299 346.724L168.491 346.724C164.585 346.724 161.114 344.479 158.923 341.434C157.84 339.928 157.718 337.947 158.463 336.249C159.489 333.912 160.895 331.925 162.641 329.929C166.58 325.429 171.921 322.9 177.491 322.9C183.06 322.9 188.402 325.429 192.34 329.929C193.787 331.584 195.002 333.455 195.959 335.476Z" fill-rule="evenodd" fill="url(#linear_fill_5_30)">
</path>
</g>
<g filter="url(#filter_5_31)">
<path d="M207.951 335.576C207.26 337.035 207.148 338.736 207.87 340.181C209.845 344.139 213.933 346.824 218.61 346.824L235.419 346.824C239.315 346.824 242.463 344.59 244.525 341.557C245.568 340.023 245.68 338.056 245.023 336.321C244.143 333.997 243.007 332.017 241.268 330.029C237.33 325.529 231.989 323 226.419 323C220.85 323 215.508 325.529 211.57 330.029C210.122 331.684 208.908 333.555 207.951 335.576Z" fill-rule="evenodd" fill="url(#linear_fill_5_32)">
</path>
</g>
<g filter="url(#filter_5_33)">
<path d="M143.5 268C134.671 265.793 95.9002 209.7 121.4 188.2C129.234 182.533 145.2 176 146.7 207.5C148.189 238.762 160.862 251.044 168.849 254.479C168.853 254.481 168.857 254.483 168.861 254.484C168.474 257.204 165.179 263.957 160.9 267.2C157.191 270.012 151.5 270 143.5 268Z" fill="url(#linear_fill_5_34)">

                                    <animate attributeName="d" values="M143.5 268C134.671 265.793 95.9002 209.7 121.4 188.2C129.234 182.533 145.2 176 146.7 207.5C148.189 238.762 160.862 251.044 168.849 254.479C168.853 254.481 168.857 254.483 168.861 254.484C168.474 257.204 165.179 263.957 160.9 267.2C157.191 270.012 151.5 270 143.5 268Z;M143.506 268.005C139.485 267 129.248 254.816 121.706 239.815C118.55 233.538 112.668 228.567 111.106 221.915C108.204 209.559 109.179 197.616 118.206 190.005C126.04 184.338 142.006 177.805 143.506 209.305C143.842 216.37 147.95 220.662 149.246 225.905C153.683 243.862 162.674 251.826 168.856 254.485L168.866 254.485C168.479 257.205 165.185 263.962 160.906 267.205C157.197 270.017 151.506 270.005 143.506 268.005Z;M143.5 268C134.671 265.793 95.9003 209.7 121.4 188.2C129.234 182.533 145.2 176 146.7 207.5C148.189 238.762 160.862 251.044 168.849 254.479C168.853 254.481 168.857 254.483 168.861 254.484C168.474 257.204 165.179 263.957 160.9 267.2C157.191 270.012 151.5 270 143.5 268Z" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></path>
</g>
<defs>
<linearGradient id="linear_fill_5_3" x1="158.84600830078125" y1="277.343994140625" x2="188.9510498046875" y2="352.67999267578125" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#E8F1FF"/>
<stop offset="1" stop-color="#D9EBFF"/>

                                    
                                    <animate attributeName="x2" values="188.9510498046875;188.95098876953125;188.9510040283203" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="x1" values="158.84600830078125;158.84600830078125;158.8459930419922" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<filter id="filter_5_2" x="156.7401123046875" y="234.5" width="90.1092529296875" height="111.823974609375" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_5_2">
                                    </feFlood>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_5_2" result="shape_5_2">
                                    
                                    </feBlend>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_5_2">
                                    </feColorMatrix>
<feOffset dx="2.8777" dy="2.8777"/>
<feGaussianBlur stdDeviation="2.51799"/>
<feComposite in2="hardAlpha_5_2" operator="arithmetic" k2="-1" k3="1">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0.8941176470588236 0 0 0 0 0.9215686274509803 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_5_2" result="innerShadow_0_5_2">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="x" values="156.7401123046875;156.74008178710938;156.74008178710938" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="width" values="90.1092529296875;90.10926818847656;90.10926818847656" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
<linearGradient id="linear_fill_5_4" x1="290.18701171875" y1="141" x2="290.18701171875" y2="198.07501220703125" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#0097FF"/>
<stop offset="1" stop-color="#53DEFF"/>

                                    
                                    <animate attributeName="x1" values="290.18701171875;290.18699645996094;290.18701171875" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="x2" values="290.18701171875;290.18699645996094;290.18701171875" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<linearGradient id="linear_fill_5_5" x1="110.094970703125" y1="141" x2="110.094970703125" y2="198.07501220703125" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#0097FF"/>
<stop offset="1" stop-color="#53DEFF"/>

                                    
                                    <animate attributeName="x1" values="110.094970703125;110.09500122070312;110.09500122070312" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="x2" values="110.094970703125;110.09500122070312;110.09500122070312" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<linearGradient id="linear_fill_5_7" x1="272.58197021484375" y1="259.26702880859375" x2="250.2449951171875" y2="309.08599853515625" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#E8F1FF"/>
<stop offset="1" stop-color="#D9EBFF"/>

                                    
                                    <animate attributeName="x1" values="272.58197021484375;272.5820007324219;272.5820007324219" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<filter id="filter_5_6" x="221.611083984375" y="230.00994873046875" width="52.34674072265625" height="76.49920654296875" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_5_6">
                                    </feFlood>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_5_6" result="shape_5_6">
                                    
                                    </feBlend>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_5_6">
                                    </feColorMatrix>
<feOffset dx="2.8777" dy="2.8777"/>
<feGaussianBlur stdDeviation="2.51799"/>
<feComposite in2="hardAlpha_5_6" operator="arithmetic" k2="-1" k3="1">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0.8941176470588236 0 0 0 0 0.9215686274509803 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_5_6" result="innerShadow_0_5_6">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="x" values="221.611083984375;221.61111450195312;221.61111450195312" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="width" values="52.34674072265625;52.34672546386719;52.346710205078125" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
<linearGradient id="linear_fill_5_8" x1="168" y1="233" x2="158" y2="249" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#0093F8"/>
<stop offset="0.3" stop-color="#0097FF"/>
<stop offset="1" stop-color="#29BAFF"/>

                                    </linearGradient>
<linearGradient id="linear_fill_5_9" x1="236" y1="232" x2="243.5" y2="248" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#0093F8"/>
<stop offset="0.3" stop-color="#0097FF"/>
<stop offset="1" stop-color="#29BAFF"/>

                                    </linearGradient>
<linearGradient id="linear_fill_5_10" x1="200" y1="256" x2="200" y2="285.08001708984375" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#0097FF"/>
<stop offset="1" stop-color="#53DEFF"/>

                                    </linearGradient>
<linearGradient id="linear_fill_5_12" x1="282.25" y1="124.34197998046875" x2="238.93304443359375" y2="251.54901123046875" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#E8F1FF"/>
<stop offset="1" stop-color="#D9EBFF"/>

                                    
                                    <animate attributeName="x2" values="238.93304443359375;238.9329833984375;238.93299865722656" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<filter id="filter_5_11" x="113.59197998046875" y="54.72760009765625" width="172.95654296875" height="181.6973876953125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_5_11">
                                    </feFlood>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_5_11" result="shape_5_11">
                                    
                                    </feBlend>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_5_11">
                                    </feColorMatrix>
<feOffset dx="2.8777" dy="2.8777"/>
<feGaussianBlur stdDeviation="2.51799"/>
<feComposite in2="hardAlpha_5_11" operator="arithmetic" k2="-1" k3="1">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0.8941176470588236 0 0 0 0 0.9215686274509803 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_5_11" result="innerShadow_0_5_11">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="x" values="113.59197998046875;113.59194946289062;113.59195709228516" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="width" values="172.95654296875;172.95654296875;172.95653533935547" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
<mask id="mask-5_14" style="mask-type:alpha" maskUnits="userSpaceOnUse">
<rect x="133" y="127.72802734375" width="133" height="83.00000000000001" fill="#FFFFFF">
</rect>
<rect x="133" y="127.72802734375" width="133" height="83.00000000000001" fill="#FFFFFF">
</rect>

                                    </mask>
<linearGradient id="linear_fill_5_18" x1="133" y1="168.72802734375" x2="266" y2="168.72802734375" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#486DFF"/>
<stop offset="0.4" stop-color="#0097FF"/>
<stop offset="1" stop-color="#35EDFF"/>

                                    </linearGradient>
<filter id="filter_5_17" x="133.000244140625" y="127.72802734375" width="132.888427734375" height="82.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_5_17">
                                    </feFlood>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_5_17" result="shape_5_17">
                                    
                                    </feBlend>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_5_17">
                                    </feColorMatrix>
<feOffset dx="0" dy="-2"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha_5_17" operator="arithmetic" k2="-1" k3="1">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0.6745098039215687 0 0 0 0 0.7607843137254902 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_5_17" result="innerShadow_0_5_17">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="x" values="133.000244140625;133.00021362304688;133.00021362304688" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="width" values="132.888427734375;132.88845825195312;132.88845825195312" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
<linearGradient id="linear_fill_5_19" x1="139" y1="168.72802734375" x2="259" y2="170.72802734375" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#1BBBFF"/>
<stop offset="1" stop-color="#FFFFFF"/>

                                    
                                    <animate attributeName="x2" values="259;258.99998474121094;259" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<linearGradient id="linear_fill_5_21" x1="161.89501953125" y1="187.031982421875" x2="229.447998046875" y2="173.0419921875" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#FFFFFF" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FFFFFF" stop-opacity="0.93"/>

                                    
                                    <animate attributeName="x1" values="161.89501953125;161.89501953125;161.89500427246094" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<filter id="filter_5_24" x="150" y="140.72802734375" width="46" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_5_24">
                                    </feFlood>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_5_24">
                                    </feColorMatrix>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha_5_24" operator="out">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.9333333333333333 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="feFloodId_5_24" result="dropShadow_1_5_24">
                                    
                                    </feBlend>
<feBlend mode="normal" in="SourceGraphic" in2="dropShadow_1_5_24" result="shape_5_24">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="y" values="140.72802734375;145;140.72802734375" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="height" values="62;58;62" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
<filter id="filter_6_69" x="208" y="140.72998046875" width="46" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_6_69">
                                    </feFlood>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_6_69">
                                    </feColorMatrix>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha_6_69" operator="out">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.9333333333333333 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="feFloodId_6_69" result="dropShadow_1_6_69">
                                    
                                    </feBlend>
<feBlend mode="normal" in="SourceGraphic" in2="dropShadow_1_6_69" result="shape_6_69">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="y" values="140.72998046875;145;140.72998046875" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="height" values="62;58;62" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
<linearGradient id="linear_fill_5_30" x1="177.20501708984375" y1="322.9000244140625" x2="177.20501708984375" y2="346.72406005859375" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#0097FF"/>
<stop offset="1" stop-color="#53DEFF"/>

                                    
                                    <animate attributeName="x1" values="177.20501708984375;177.20498657226562;177.2050018310547" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="x2" values="177.20501708984375;177.20498657226562;177.2050018310547" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<filter id="filter_5_29" x="157.9901123046875" y="322.9000244140625" width="38.5413818359375" height="23.823974609375" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_5_29">
                                    </feFlood>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_5_29" result="shape_5_29">
                                    
                                    </feBlend>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_5_29">
                                    </feColorMatrix>
<feOffset dx="0" dy="-2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha_5_29" operator="arithmetic" k2="-1" k3="1">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0.011764705882352941 0 0 0 0 0.5764705882352941 0 0 0 0 0.9686274509803922 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_5_29" result="innerShadow_0_5_29">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="x" values="157.9901123046875;157.99008178710938;157.99008178710938" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="width" values="38.5413818359375;38.541351318359375;38.541351318359375" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
<linearGradient id="linear_fill_5_32" x1="226.45501708984375" y1="323" x2="226.45501708984375" y2="346.823974609375" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#0097FF"/>
<stop offset="1" stop-color="#53DEFF"/>

                                    
                                    <animate attributeName="x1" values="226.45501708984375;226.45501708984375;226.4550018310547" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="x2" values="226.45501708984375;226.45501708984375;226.4550018310547" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<filter id="filter_5_31" x="207.378173828125" y="323" width="38.053955078125" height="23.823974609375" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_5_31">
                                    </feFlood>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_5_31" result="shape_5_31">
                                    
                                    </feBlend>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_5_31">
                                    </feColorMatrix>
<feOffset dx="0" dy="-2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha_5_31" operator="arithmetic" k2="-1" k3="1">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0.011764705882352941 0 0 0 0 0.5764705882352941 0 0 0 0 0.9686274509803922 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_5_31" result="innerShadow_0_5_31">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="width" values="38.053955078125;38.05397033691406;38.05397033691406" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="x" values="207.378173828125;207.378173828125;207.37815856933594" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
<linearGradient id="linear_fill_5_34" x1="114.23699951171875" y1="216.4010009765625" x2="140.9969482421875" y2="270.248046875" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#ECF4FF"/>
<stop offset="1" stop-color="#E1EFFF"/>

                                    
                                    <animate attributeName="x1" values="114.23699951171875;114.23703002929688;114.23699951171875" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="x2" values="140.9969482421875;140.99700927734375;140.9969940185547" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="y2" values="270.248046875;270.24798583984375;270.248046875" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<filter id="filter_5_33" x="112.8436279296875" y="183.4725341796875" width="56.017333984375" height="85.94415283203125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_5_33">
                                    </feFlood>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_5_33" result="shape_5_33">
                                    
                                    </feBlend>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_5_33">
                                    </feColorMatrix>
<feOffset dx="2.8777" dy="2.8777"/>
<feGaussianBlur stdDeviation="2.51799"/>
<feComposite in2="hardAlpha_5_33" operator="arithmetic" k2="-1" k3="1">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0.8941176470588236 0 0 0 0 0.9215686274509803 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_5_33" result="innerShadow_0_5_33">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="x" values="112.8436279296875;109.64559936523438;112.84365844726562" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="y" values="183.4725341796875;185.2750244140625;183.4725341796875" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="width" values="56.017333984375;59.218048095703125;56.01734924316406" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="height" values="85.94415283203125;84.1441650390625;85.94415283203125" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
</defs>
</svg>