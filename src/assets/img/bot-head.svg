<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="173" height="182" viewBox="0 0 173 182" fill="none">
<g clip-path="url(#clip-path-2-B_Jyj4BbYi1PbLUH7xr)">
<g filter="url(#filter_G-qeG-bv71P1Hu1t8g4KP)">
<path d="M124.724 9.69825C125.42 9.76219 126.121 9.64261 126.757 9.35143L146.366 0.365903C148.686 -0.697117 151.394 0.654558 151.94 3.1474L156.458 23.7971C156.611 24.499 156.951 25.1466 157.441 25.6718L171.88 41.1384C173.631 43.0141 173.188 46.0299 170.972 47.323L162.565 52.228C160.623 53.3606 160.007 55.8652 161.059 57.8511C162.897 61.3181 164.492 65.0931 165.821 69.1974C171.623 87.1101 172.474 108.864 172.474 124.197C172.474 176.697 131.531 181.697 85.9822 181.697C55.275 181.697 9.72594 178.897 3.58449 147.697C-4.09232 108.697 -6.6512 33.6973 73.6994 23.1973C81.0875 22.7572 88.3328 22.6735 95.3265 23.0499C97.8528 23.1858 99.9416 21.0411 99.6537 18.5275L98.9248 12.1654C98.636 9.64419 100.738 7.49477 103.265 7.72691L124.724 9.69825Z" fill-rule="evenodd" fill="url(#linear_fill_YrS7cYvIsRHMknFSrPMmq)">
</path>
</g>
<g filter="url(#filter_R-qOgT8srvaFr8P4NoNsI)">
<g mask="url(#mask-Hd484lLeE5GgxNkQbW8bm)">
<path d="M150.535 104.501C160.535 154.501 126.428 155.5 87.0347 155.5C51.9282 155.5 17.035 153.002 19.5347 118.5C20.8944 99.7334 28.0158 73.0003 87.0347 73C136.035 72.9998 147.868 94.0009 150.535 104.501Z" fill="url(#linear_fill_ryKHSpYrT-GBjJK9QFeQ3)">
</path>
</g>
</g>
<g mask="url(#mask-Hd484lLeE5GgxNkQbW8bm)" opacity="0.2">
<path d="M145.764 107.394C155.095 154.05 123.27 154.982 86.5112 154.982C53.7529 154.982 21.1937 152.65 23.5262 120.456C24.7949 102.945 31.44 78.0002 86.5112 78C132.234 77.9998 143.276 97.5962 145.764 107.394Z" fill="url(#linear_fill_hkbiBnDFq1AG2LdCPffYq)">
</path>
</g>
<g opacity="0.6" style="mix-blend-mode:soft-light">
<g mask="url(#mask-Hd484lLeE5GgxNkQbW8bm)">
<path d="M112.531 40.9692L76.7274 47.1169C74.2222 47.5471 71.8258 48.4648 69.674 49.818L6.98952 89.2383C2.95849 91.7733 1.35988 96.8569 3.21459 101.243L35.8896 178.51C40.2176 188.744 48.8457 196.424 59.3101 199.617L112.531 40.9692Z" fill-rule="evenodd" fill="url(#linear_fill_5a9htmIyg22dkSywNv9ly)" fill-opacity="0.15">

                                    <animate attributeName="d" values="M112.531 40.9692L76.7274 47.1169C74.2222 47.5471 71.8258 48.4648 69.674 49.818L6.98952 89.2383C2.95849 91.7733 1.35988 96.8569 3.21459 101.243L35.8896 178.51C40.2176 188.744 48.8457 196.424 59.3101 199.617L112.531 40.9692Z;M112.531 40.9692L76.7274 47.1169C74.2222 47.5471 71.8258 48.4648 69.674 49.818L6.98952 89.2383C2.95849 91.7733 1.35988 96.8569 3.21459 101.243L35.8896 178.51C40.2176 188.744 48.8457 196.424 59.3101 199.617L112.531 40.9692Z;M112.531 40.9692L76.7274 47.1169C74.2222 47.5471 71.8258 48.4648 69.674 49.818L6.98951 89.2383C2.95848 91.7733 1.35987 96.8569 3.21458 101.243L35.8896 178.51C40.2176 188.744 48.8457 196.424 59.3101 199.617L112.531 40.9692Z" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></path>
</g>
</g>
<g mask="url(#mask-Hd484lLeE5GgxNkQbW8bm)" opacity="0.2">
<ellipse cx="124.718994140625" cy="137.5" rx="8" ry="3.5" fill="#FFFFFF">

                                    <animate attributeName="cx" values="124.718994140625;124.72000122070312;124.718994140625" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="cy" values="137.5;133.5;137.5" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></ellipse>
</g>
<g mask="url(#mask-Hd484lLeE5GgxNkQbW8bm)" opacity="0.1">
<ellipse cx="47.407959148757094" cy="138.56440742055" transform="rotate(-5.999999940167532 39.407958984375 135.0644073486328)" rx="8.000000164382092" ry="3.5000000719171727" fill="#FFFFFF">

                                    <animate attributeName="cx" values="47.407959148757094;47.41000382649147;47.407974407546156" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="cy" values="138.56440742055;134.55999763051094;138.56440742055" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animateTransform attributeName="transform" type="rotate" values="-5.999999940167532 39.407958984375 135.0644073486328;-5.999999940167532 39.410003662109375 131.05999755859375;-5.999999940167532 39.40797424316406 135.0644073486328" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></ellipse>
</g>
<g filter="url(#filter_g-O9ZwFEiyazzZOPn5GUs)">
<g mask="url(#mask-Hd484lLeE5GgxNkQbW8bm)">
<rect x="50.407989501953125" y="98" width="14" height="30" rx="7" fill="#FFFFFF">

                                    <animate attributeName="x" values="50.407989501953125;50.40800476074219;50.40800476074219" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="y" values="98;105;98" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="height" values="30;22.79302978515625;30" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></rect>
</g>
</g>
<g filter="url(#filter_2osc5ascZCeP7s3O1Ax17)">
<g mask="url(#mask-Hd484lLeE5GgxNkQbW8bm)">
<rect x="106.40798950195312" y="98.27239990234375" width="14" height="30" rx="7" fill="#FFFFFF">

                                    <animate attributeName="x" values="106.40798950195312;106.40799713134766;106.40798950195312" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="y" values="98.27239990234375;105.20697021484375;98.27239990234375" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="height" values="30;22.79302978515625;30" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></rect>
</g>
</g>
</g>
<defs>
<clipPath id="clip-path-2-B_Jyj4BbYi1PbLUH7xr">
<path d="M0 182L173 182L173 0L0 0L0 182Z" fill="white"/>
</clipPath>
<linearGradient id="linear_fill_YrS7cYvIsRHMknFSrPMmq" x1="168.65802001953125" y1="69.61470031738281" x2="125.34100341796875" y2="196.8209991455078" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#E8F1FF"/>
<stop offset="1" stop-color="#D9EBFF"/>

                                    
                                    <animate attributeName="x1" values="168.65802001953125;168.6580047607422;168.6580047607422" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<filter id="filter_G-qeG-bv71P1Hu1t8g4KP" x="0" y="0" width="172.95648193359375" height="181.69700622558594" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_G-qeG-bv71P1Hu1t8g4KP">
                                    </feFlood>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_G-qeG-bv71P1Hu1t8g4KP" result="shape_G-qeG-bv71P1Hu1t8g4KP">
                                    
                                    </feBlend>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_G-qeG-bv71P1Hu1t8g4KP">
                                    </feColorMatrix>
<feOffset dx="2.8777" dy="2.8777"/>
<feGaussianBlur stdDeviation="2.51799"/>
<feComposite in2="hardAlpha_G-qeG-bv71P1Hu1t8g4KP" operator="arithmetic" k2="-1" k3="1">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0.8941176470588236 0 0 0 0 0.9215686274509803 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_G-qeG-bv71P1Hu1t8g4KP" result="innerShadow_0_G-qeG-bv71P1Hu1t8g4KP">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="width" values="172.95648193359375;172.9564971923828;172.9564971923828" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
<mask id="mask-Hd484lLeE5GgxNkQbW8bm" style="mask-type:alpha" maskUnits="userSpaceOnUse">
<rect x="19.407989501953125" y="73" width="133" height="83" fill="#FFFFFF">

                                    <animate attributeName="x" values="19.407989501953125;19.408004760742188;19.408004760742188" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></rect>
<rect x="19.407989501953125" y="73" width="133" height="83" fill="#FFFFFF">

                                    <animate attributeName="x" values="19.407989501953125;19.408004760742188;19.408004760742188" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></rect>

                                    </mask>
<linearGradient id="linear_fill_ryKHSpYrT-GBjJK9QFeQ3" x1="19.40802001953125" y1="114" x2="152.40802001953125" y2="114" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#486DFF"/>
<stop offset="0.4" stop-color="#0097FF"/>
<stop offset="1" stop-color="#35EDFF"/>

                                    
                                    <animate attributeName="x1" values="19.40802001953125;19.408004760742188;19.407997131347656" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="x2" values="152.40802001953125;152.4080047607422;152.40798950195312" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<filter id="filter_R-qOgT8srvaFr8P4NoNsI" x="19.407958984375" y="73" width="132.88873291015625" height="82.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_R-qOgT8srvaFr8P4NoNsI">
                                    </feFlood>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_R-qOgT8srvaFr8P4NoNsI" result="shape_R-qOgT8srvaFr8P4NoNsI">
                                    
                                    </feBlend>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_R-qOgT8srvaFr8P4NoNsI">
                                    </feColorMatrix>
<feOffset dx="0" dy="-2"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha_R-qOgT8srvaFr8P4NoNsI" operator="arithmetic" k2="-1" k3="1">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0.6745098039215687 0 0 0 0 0.7607843137254902 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_R-qOgT8srvaFr8P4NoNsI" result="innerShadow_0_R-qOgT8srvaFr8P4NoNsI">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="x" values="19.407958984375;19.407958984375;19.40795135498047" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="width" values="132.88873291015625;132.88873291015625;132.88874053955078" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
<linearGradient id="linear_fill_hkbiBnDFq1AG2LdCPffYq" x1="25.40802001953125" y1="114" x2="145.40802001953125" y2="116" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#1BBBFF"/>
<stop offset="1" stop-color="#FFFFFF"/>

                                    
                                    <animate attributeName="x1" values="25.40802001953125;25.408004760742188;25.407997131347656" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="x2" values="145.40802001953125;145.4080047607422;145.40798950195312" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<linearGradient id="linear_fill_5a9htmIyg22dkSywNv9ly" x1="48.3031005859375" y1="132.30400848388672" x2="115.85601806640625" y2="118.31401062011719" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#FFFFFF" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FFFFFF" stop-opacity="0.93"/>

                                    
                                    <animate attributeName="x2" values="115.85601806640625;115.85601043701172;115.85600280761719" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></linearGradient>
<filter id="filter_g-O9ZwFEiyazzZOPn5GUs" x="36.407989501953125" y="84" width="46" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_g-O9ZwFEiyazzZOPn5GUs">
                                    </feFlood>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_g-O9ZwFEiyazzZOPn5GUs">
                                    </feColorMatrix>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha_g-O9ZwFEiyazzZOPn5GUs" operator="out">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.9333333333333333 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="feFloodId_g-O9ZwFEiyazzZOPn5GUs" result="dropShadow_1_g-O9ZwFEiyazzZOPn5GUs">
                                    
                                    </feBlend>
<feBlend mode="normal" in="SourceGraphic" in2="dropShadow_1_g-O9ZwFEiyazzZOPn5GUs" result="shape_g-O9ZwFEiyazzZOPn5GUs">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="x" values="36.407989501953125;36.40800476074219;36.40800476074219" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="y" values="84;91;84" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="width" values="46;46.00001525878906;46" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="height" values="62;54.79302978515625;62" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
<filter id="filter_2osc5ascZCeP7s3O1Ax17" x="92.40798950195312" y="84.27239990234375" width="46" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_2osc5ascZCeP7s3O1Ax17">
                                    </feFlood>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_2osc5ascZCeP7s3O1Ax17">
                                    </feColorMatrix>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha_2osc5ascZCeP7s3O1Ax17" operator="out">
                                    </feComposite>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.9333333333333333 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="feFloodId_2osc5ascZCeP7s3O1Ax17" result="dropShadow_1_2osc5ascZCeP7s3O1Ax17">
                                    
                                    </feBlend>
<feBlend mode="normal" in="SourceGraphic" in2="dropShadow_1_2osc5ascZCeP7s3O1Ax17" result="shape_2osc5ascZCeP7s3O1Ax17">
                                    
                                    </feBlend>

                                    
                                    <animate attributeName="x" values="92.40798950195312;92.40799713134766;92.40798950195312" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="y" values="84.27239990234375;91.20697021484375;84.27239990234375" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/>
                                    <animate attributeName="height" values="62;54.79302978515625;62" begin="0s" dur="1.5s" repeatCount="indefinite" fill="freeze" calcMode="linear" keyTimes="0;0.5;1"/></filter>
</defs>
</svg>