.back-btn {
    cursor: pointer;
    width: 60px;
    color: #333;
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.title {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 12px;
    width: calc(100% - 60px - 20px);
}

.containerq {
    background: rgba(240, 249, 255, 1);
    min-height: 100vh;
    width: 100%;
    min-width: 1400px;
    overflow: auto;
}

.top {
    height: 60px;
    opacity: 1;
    background: #FFFFFF;
    box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
    position: fixed;
    width: 100%;
    z-index: 9999;
}

.quetion {
    /* margin-top: 20px; */
    min-width: 880px;
    /* width: 100%; */
    margin: 20px 260px 0;
    border-radius: 8.53px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
    min-height: calc(100vh - 100px);
    padding: 20px 20px 150px 20px;
}

.num {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 14px;
    color: rgba(102, 102, 102, 1);
}
.numchoose{
    font-size: 14px;
font-weight: 400;
letter-spacing: 0px;
line-height: 0px;
color: rgba(102, 102, 102, 1);
}
.search {
    height: 97px;
    opacity: 1;
    border-radius: 5px;
    background: rgba(63, 140, 255, 0.03);
    margin-top: 20px;
    padding: 20px 20px 20px 20px;
}
.allselect{
    height: 54px;
    border-bottom: 1px solid #E5E5E5;
}

.footer {
    height: 80px;
    opacity: 1;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px -7px 10px rgba(67, 143, 254, 0.2);
    position: fixed;
    bottom: 0;
    width: 100%;
}


.search-icon {
    cursor: pointer;
}

.flexInit {
    display: flex;
}

.flex {
    display: flex;
    align-items: center;
}

.flex-s-between {
    justify-content: space-between;
}

.detele {
    flex-shrink: 0;
}

.detele:hover {
    cursor: pointer;
}

.table-header {
    background-color: #f4f1f1 !important;
    color: #000;
    font-weight: bold;
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-top: 20px;
}

.com-cell {
    font-weight: 600;
    text-align: left;
}

.com-cell-d {
    text-align: left;
    padding: 4px 0;

}

.cell-1-0 {
    width: 60px;
    text-align: left;
}

.cell-1-1 {
    width: 100px;
}

.cell-1-2 {
    flex: 1;
    min-width: 140px;
    padding: 0 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
    -moz-line-clamp: 5;
    -ms-line-clamp: 5;
    -o-line-clamp: 5;
    line-clamp: 5;

    overflow: hidden;
}

.cell-1-3 {
    width: 120px;
}

.cell-1-4 {
    width: 100px;
}

.cell-1-5 {
    width: 100px;
}

.cell-1-6 {
    text-align: center;
    width: 120px;
}

.tbodyTr {
    cursor: pointer;
    border-bottom: #efefef 1px solid;
    padding: 6px 20px;
}

.tbodyTr:hover {
    border: #36aadf 1px solid;
    background-color: #fff;
    border-radius: 3px;
}

.tbodyTr-1 {
    background-color: #f7f7f7;
}

.detailsQ-title {
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0 16px 0;
    padding: 0 0 0 10px;
    border-left: #36aadf 4px solid;
    line-height: 16px;
}

.question-details {
    padding: 24px;
    margin: 16px 0 20px 0;
    background-color: #fafafa;
    border-radius: 10px;

    .questionType {
        color: rgb(0, 154, 255);
        border: 1px solid rgb(0, 154, 255);
        font-size: 12px;
        border-radius: 6px;
        display: inline-block;
        margin: 3px 4px 0 0;
        padding: 0 4px;
        flex-shrink: 0;
        height: 22px;
    }

    .question-text {
        display: inline-block;

    }

    .question-answer {
        display: flex;
        font-size: 14px;
        color: #009aff;
        margin: 10px 0;
    }

    .question-explanation {
        font-size: 14px;
    }

    .briVerti {
        float: left;
        height: 27px;
        line-height: 22px;
        margin-right: 6px;
        margin-left: 5px;
        color: #b8b8b8;
    }
}