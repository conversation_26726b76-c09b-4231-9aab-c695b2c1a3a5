.width100{
    width: 100%;
}
.group-title {
    position: relative;
    display: inline-block;
    margin-bottom: 14px;
    font-size: 16px;
    font-weight: 700;
    color: #333333;
    text-indent: 12px;
}

.group-title::after {
    content: '*';
    position: absolute;
    top: 50%;
    right: -16px;
    transform: translateY(-50%);
    color: #d52b2b;
}

.group-title::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background-color: #36aafd;
    border-radius: 2px !important;
}

.group-titlef {
    position: relative;
    display: inline-block;
    margin-bottom: 14px;
    font-size: 16px;
    font-weight: 700;
    color: #333333;
    text-indent: 12px;
}


.group-titlef::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background-color: #36aafd;
    border-radius: 2px !important;
}

.lable{
    border: 1px solid #e3e0e0;width: 100%;
    border-radius: 4px;
    padding: 10px;
    height: 120px;
}

.options-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.aiEditorClass{
    width: 96%;
}

.extra{
    height: unset;
    line-height: unset;
    margin-bottom: 20px;
    padding: 12px 24px 12px 24px;
    text-indent: 0;
    font-size: 14px;
    color: #666666;
    background-color: #fff2e6;
    border-radius: 4px !important;
    width: 70%;
}