@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --font-default: 'aliba<PERSON><PERSON><PERSON><PERSON>';

  --color-foreground-1: #000;
  --color-foreground-2: #333;
  --color-foreground-3: #666;
  --color-foreground-4: #999;
  --color-primary: #3f8cff;

  --color-border: #d9d9d9;
  --color-separator: #e5e5e5;
}

@layer base {
  * {
    @apply font-default border-border;
  }

  *:focus-visible {
    @apply outline-primary;
  }
}

@utility scrollbar-cyan {
  --scrollbar-color: #c7ddfc;
}

@utility scrollbar-gray {
  --scrollbar-color: #e5e5e5;
}
