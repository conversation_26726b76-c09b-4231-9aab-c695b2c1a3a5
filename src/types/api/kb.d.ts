declare namespace API {
  declare namespace Kb {
    interface Kb {
      avatar?: string
      chunk_count: number
      create_date?: string
      create_time?: number
      created_by?: string
      description?: string
      document_count: number
      id: string
      language?: string
      name: string
      chunk_method: string
      pagerank: number
      parser_config: KbParserConfig
      permission: string
      similarity_threshold?: number
      status?: string
      tenant_id?: string
      token_num: number
      update_date?: string
      update_time?: number
      vector_similarity_weight?: number
      embedding_model: string
      nickname?: string
      course_names: string[]
      course_semesters: Array<{
        course_id: number
        course_title: string
        semester: {
          id: number
          name: string
          is_active: boolean
        }
      }>
    }

    interface KbParserConfig {
      auto_keywords?: number
      auto_questions?: number
      chunk_token_num?: number
      delimiter?: string
      html4excel?: boolean
      layout_recognize?: boolean
      pages?: [number, number][]
      raptor?: Raptor
    }

    interface Raptor {
      use_raptor: boolean
    }

    interface File {
      chunk_count: number
      create_date: string
      create_time: number
      created_by: string
      id: string
      kb_id: string
      location: string
      name: string
      parser_config: ParserConfig
      chunk_method: string
      process_begin_at?: string
      process_duation: number
      progress: number
      progress_msg: string
      run: DocumentRunningStatus
      size: number
      source_type: string
      status: string
      thumbnail: string
      token_num: number
      type: string
      update_date: string
      update_time: number
      meta_fields?: Record<string, unknown>
    }

    interface ParserConfig {
      chunk_token_num?: number
      delimiter?: string
      html4excel?: boolean
      layout_recognize?: 'DeepDoc' | 'Plain Text'
      pages?: [number, number][]
      auto_keywords?: number
      auto_questions?: number
      raptor?: {
        use_raptor: boolean
      }
      graphrag?: GraphRag
    }

    interface GraphRag {
      community?: boolean
      entity_types?: string[]
      method?: string
      resolution?: boolean
      use_graphrag?: boolean
    }

    type DocumentRunningStatus =
      | 'UNSTART' // need to run
      | 'RUNNING' // need to cancel
      | 'CANCEL' // need to refresh
      | 'DONE' // need to refresh
      | 'FAIL' // need to refresh

    interface RetrievalChunk {
      /** 文档块内容 */
      content: string
      /** 文档块内容（可能经过处理的版本） */
      content_ltks: string
      /** 关联的文档ID */
      document_id: string
      /** 文档关键词/文档名称 */
      document_keyword: string
      /** 高亮显示的内容（包含HTML标记） */
      highlight: string
      /** 文档块唯一标识符 */
      id: string
      /** 图片ID（可选） */
      image_id: string
      /** 重要关键词列表 */
      important_keywords: string[]
      /** 关联的知识库ID */
      kb_id: string
      /** 位置信息列表 */
      positions: string[]
      /** 总体相似度分数 */
      similarity: number
      /** 词汇相似度分数 */
      term_similarity: number
      /** 向量相似度分数 */
      vector_similarity: number
    }

    interface QaChunk {
      available: boolean
      content: string
      dataset_id: string
      document_id: string
      document_name: string
      id: string
      important_keywords: string[]
      questions: string[]
    }
  }
}
