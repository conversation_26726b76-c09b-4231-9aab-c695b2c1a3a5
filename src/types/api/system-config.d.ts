declare namespace API {
  declare namespace SystemConfig {
    interface Staff {
      id: string
      is_admin: boolean
      username: string
      first_name: string
    }

    interface FilterWord {
      id: string
      keyword: string
      created_at: string
      updated_at: string
    }

    type CreateFilterWord = {
      keyword: string
    }

    type Permission = {
      id: number
      name: string
      has_perm: boolean
    }

    type PermissionModule = {
      permissions: Permission[]
      model_name: string
    }

    type PermissionGroup = {
      id: number
      group_name: string
      models: PermissionModule[]
    }
  }
}
