<script lang="ts">
  export interface FormValues {
    name: string
    source: string
    framework: string
    replicaNum: number
    modelType: string
    vcardNum?: number
    parameters?: string[]
    allowDistributedInference: boolean
    autoRestartOnError: boolean
  }

  const VCARD_NUM_PARAMETER = '--tensor_parallel_size'
  const MAX_REPLICA_NUM = 8
  const MAX_VCARD_NUM = 8

  function rawDataToFormValues(rawModel: Partial<API.Models.Model>): FormValues {
    const [vcardNum, parameters] = (function () {
      const index = rawModel.backend_parameters?.findIndex((parameter) =>
        parameter.startsWith(VCARD_NUM_PARAMETER),
      )

      if (index === undefined || index < 0) {
        return [undefined, rawModel.backend_parameters]
      }

      const vcardNum = Number(rawModel.backend_parameters![index].split('=')[1])
      const parameters = rawModel.backend_parameters!.filter((_, i) => i !== index)

      return [vcardNum, parameters]
    })()

    return {
      name: rawModel.name!,
      source: rawModel.local_path!,
      framework: rawModel.backend!,
      replicaNum: rawModel.replicas ?? 1,
      modelType: rawModel.categories?.[0] ?? 'llm',
      vcardNum,
      parameters,
      allowDistributedInference: rawModel.distributed_inference_across_workers ?? false,
      autoRestartOnError: rawModel.restart_on_error ?? false,
    }
  }

  function formValuesToRawData(formValues: Partial<FormValues>): Partial<API.Models.Model> {
    const backend_parameters = (function () {
      if (formValues.vcardNum === undefined) {
        return formValues.parameters
      }

      const index = formValues.parameters?.findIndex((parameter) =>
        parameter.startsWith(VCARD_NUM_PARAMETER),
      )

      if (index === undefined || index < 0) {
        return [...(formValues.parameters ?? []), `${VCARD_NUM_PARAMETER}=${formValues.vcardNum}`]
      }

      formValues.parameters![index] = `${VCARD_NUM_PARAMETER}=${formValues.vcardNum}`

      return formValues.parameters
    })()

    return {
      name: formValues.name,
      source: 'local_path',
      local_path: formValues.source,
      backend: formValues.framework,
      replicas: formValues.replicaNum,
      categories: formValues.modelType ? [formValues.modelType] : undefined,
      backend_parameters,
      distributed_inference_across_workers: formValues.allowDistributedInference,
      restart_on_error: formValues.autoRestartOnError,
    }
  }
</script>

<script setup lang="ts">
  import { message, AutoComplete as AAutoComplete, type FormProps } from 'ant-design-vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  import { injectModelsContext } from '@/pages/(app)/models.vue'

  import IconHelp from '~icons/lucide/circle-help'
  import IconPlus from '~icons/lucide/plus'
  import IconMinus from '~icons/lucide/minus'

  const props = withDefaults(defineProps<FormProps>(), {
    layout: 'vertical',
  })

  const { deployFormRawModel, formApi: formAction } = injectModelsContext()

  const formValues = ref<Partial<FormValues> & { parameters: string[] }>({
    parameters: [],
  })

  const initialValues = computed(() => rawDataToFormValues(deployFormRawModel.value))
  watchEffect(() => {
    formValues.value = {
      ...initialValues.value,
      parameters: [...(initialValues.value.parameters ?? [])],
    }
  })
  watchEffect(() => {
    formValues.value.framework ??= 'vllm'
  })
  watchEffect(() => {
    formValues.value.modelType ??= 'llm'
  })

  const invalidate = useModelsInvalidation()
  const { mutate, status } = useMutation({
    mutationFn() {
      if (formAction.value === 'create') {
        return modelsFetcher('/models/', {
          method: 'post',
          body: formValuesToRawData(formValues.value),
        })
      } else {
        return modelsFetcher(`/models/${deployFormRawModel.value.id}/`, {
          method: 'put',
          body: {
            ...initialValues.value,
            ...formValuesToRawData(formValues.value),
          },
        })
      }
    },
    onSuccess: invalidate.allModels,
    onError(error) {
      message.error(error.message)
    },
  })

  const emit = defineEmits<{ 'update:status': [MutationStatus] }>()
  watchEffect(() => {
    emit('update:status', status.value)
  })

  const { queryOptions } = useModelFilePaths()
  const { data: modelFilePaths } = useQuery(queryOptions)
  const autoCompleteOptions = computed(
    () => modelFilePaths.value?.map((path) => ({ value: path })) ?? [],
  )
</script>

<template>
  <AForm
    v-bind="props"
    :model="formValues"
    layout="vertical"
    class="space-y-4!"
    @finish="mutate()"
  >
    <AFormItem
      label="名称"
      name="name"
      required
    >
      <AInput
        v-model:value="formValues.name"
        :maxlength="64"
        show-count
      />
    </AFormItem>

    <AFormItem
      label="模型路径"
      name="source"
      required
    >
      <AAutoComplete
        v-model:value="formValues.source"
        :maxlength="4096"
        show-count
        :options="autoCompleteOptions"
      />
    </AFormItem>

    <AFormItem
      label="加速框架"
      name="framework"
      required
    >
      <AInput
        v-model:value="formValues.framework"
        disabled
      />
    </AFormItem>

    <AFormItem
      label="副本数"
      name="replicaNum"
      required
    >
      <AInputNumber
        v-model:value="formValues.replicaNum"
        :min="0"
        :max="MAX_REPLICA_NUM"
        class="w-full!"
        :precision="0"
      />
    </AFormItem>

    <AFormItem
      label="模型类别"
      name="modelType"
      required
    >
      <ASelect
        default-value="llm"
        v-model:value="formValues.modelType"
      >
        <ASelectOption value="llm">LLM</ASelectOption>
        <ASelectOption value="embedding">Embedding</ASelectOption>
      </ASelect>
    </AFormItem>

    <AFormItem
      label="显卡张数"
      name="vcardNum"
    >
      <AInputNumber
        v-model:value="formValues.vcardNum"
        class="w-full!"
        :min="0"
        :max="MAX_VCARD_NUM"
        :precision="0"
      />
    </AFormItem>

    <div>
      <div class="flex items-center pb-2">
        <div>后端参数</div>
        <ATooltip>
          <IconHelp class="ml-1 size-3.5 cursor-help" />
          <template #title>
            <span class="text-xs">
              vLLM 参数请参考
              <a
                href="https://docs.vllm.ai/en/stable/serving/openai_compatible_server.html#cli-reference"
                target="_blank"
                rel="noreferrer"
              >
                文档
              </a>
            </span>
          </template>
        </ATooltip>
      </div>

      <div class="space-y-4 rounded-md border p-5">
        <template v-if="formValues.parameters.length > 0">
          <div
            class="flex items-start gap-2.5"
            v-for="(_, index) in formValues.parameters"
            :key="index"
          >
            <AFormItem
              class="m-0! w-full!"
              :name="['parameters', index]"
              :rules="[{ required: true, message: '请输入参数' }]"
            >
              <AInput
                v-model:value="formValues.parameters[index]"
                :maxlength="256"
                show-count
              />
            </AFormItem>

            <AButton
              shape="circle"
              size="small"
              class="mt-1 flex! items-center justify-center p-0!"
              @click="formValues.parameters.splice(index, 1)"
              html-type="button"
            >
              <IconMinus />
            </AButton>
          </div>
        </template>

        <div class="flex items-center gap-2.5">
          <button
            class="flex w-full cursor-pointer items-center justify-center gap-2 rounded-md bg-[#f0f0f0] p-2 transition-colors hover:bg-[#e0e0e0]"
            @click.prevent="formValues.parameters.push('')"
            type="button"
          >
            <IconPlus />
            添加参数
          </button>

          <div
            class="w-6"
            v-if="formValues.parameters.length > 0"
          />
        </div>
      </div>
    </div>

    <AFormItem name="allowDistributedInference">
      <div class="flex items-center">
        <ACheckbox v-model:checked="formValues.allowDistributedInference">
          允许跨 Worker 分布式推理
        </ACheckbox>
        <ATooltip title="请确认显卡状态后再调整该设置">
          <IconHelp class="ml-1 size-3.5 cursor-help" />
        </ATooltip>
      </div>
    </AFormItem>

    <AFormItem name="autoRestartOnError">
      <ACheckbox v-model:checked="formValues.autoRestartOnError"> 错误时自动重启 </ACheckbox>
    </AFormItem>
  </AForm>
</template>
