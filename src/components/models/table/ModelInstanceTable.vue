<script lang="ts">
  import type { ColumnType } from 'ant-design-vue/es/table'

  export interface TableItem {
    id: string
    name: string
    status: API.Models.ModelInstance['state']
    createTime: Date
  }

  const columns = [
    { width: 80, key: 'expansion-placeholder' },
    {
      title: '实例名称',
      dataIndex: 'name',
      key: 'name',
      width: 560,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 160,
    },
    {
      title: '创建时间',
      key: 'createTime',
      customRender: ({ record }) => record.createTime.toLocaleString('zh-CN'),
      width: 200,
    },
    { key: 'action-placeholder', width: 160 },
  ] as const satisfies ColumnType<TableItem>[]
</script>

<script setup lang="ts">
  import { message } from 'ant-design-vue'

  const props = defineProps<{
    modelId: string
  }>()

  const { queryOptions } = useModelInstanceList({ modelId: props.modelId })
  const {
    data: rawInstances,
    status,
    error,
  } = useQuery({
    ...queryOptions,
    refetchInterval: 3000,
  })

  watch(error, (err) => {
    if (err) {
      message.error(err.message)
    }
  })

  // 状态变化时，刷新模型列表
  {
    const invalidate = useModelsInvalidation()
    watch(rawInstances, (newInstances, oldInstances) => {
      const instanceStatusUpdated = newInstances?.some((newInstance, index) => {
        const oldInstance = oldInstances?.[index]
        return newInstance.state !== oldInstance?.state
      })
      if (instanceStatusUpdated) {
        invalidate.allModels()
      }
    })
  }

  const instancesTransformed = computed(
    () =>
      rawInstances.value?.map((item) => ({
        id: String(item.id),
        name: item.name,
        createTime: new Date(item.created_at),
        status: item.state,
      })) ?? [],
  )
</script>

<template>
  <ATable
    :columns="columns"
    :data-source="instancesTransformed"
    class="[&_.ant-table]:-mx-4! **:[.ant-table-cell]:first:border-none! **:[th]:border-t-0!"
    :pagination="false"
    :scroll="{ x: 1200 }"
    :loading="status === 'pending' || status === 'error'"
    :show-header="false"
  >
    <template
      #bodyCell="// @ts-expect-error antdv poor typing
      { column, record }: { record: TableItem; column: (typeof columns)[number] }"
    >
      <template v-if="column.key === 'status'">
        <ModelInstanceStatusTag :status="record.status" />
      </template>
    </template>
  </ATable>
</template>
