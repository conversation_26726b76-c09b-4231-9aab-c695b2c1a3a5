<script setup lang="ts">
  import type { ButtonProps } from 'ant-design-vue'
  import type { HTMLAttributes } from 'vue'
  import { twMerge } from 'tailwind-merge'
  import {
    DialogRoot,
    DialogTrigger,
    DialogPortal,
    DialogContent,
    DialogOverlay,
    DialogTitle,
    ScrollAreaRoot,
    ScrollAreaViewport,
    DialogClose,
  } from 'reka-ui'

  import { injectExtraChatContext } from '../ChatPage.vue'

  import IconHistory from '~icons/material-symbols/history-rounded'
  import IconX from '~icons/lucide/x'

  const {
    type = 'text',
    size = 'small',
    title = '对话历史',
    class: className,
    ...props
  } = defineProps<ButtonProps & { class?: HTMLAttributes['class'] }>()

  const isDialogOpen = ref(false)

  // onBeforeRouteUpdate(async (to) => {
  //   if (!to.path.startsWith('/chat')) {
  //     return
  //   }
  //   isDialogOpen.value = false
  //   // 使页面过渡稍微自然一些
  //   if (to.query.userNavigation) {
  //     await new Promise((resolve) => setTimeout(resolve, 100))
  //     return { ...to, query: { ...to.query, userNavigation: undefined } }
  //   }
  // })
</script>

<template>
  <DialogRoot v-model:open="isDialogOpen">
    <DialogTrigger as-child>
      <AButton
        v-bind="props"
        :type="type"
        :size="size"
        :title="title"
        :class="twMerge('group size-auto! p-1!', className)"
      >
        <IconHistory class="group-not-disabled:text-foreground-3 size-5 md:size-6" />
      </AButton>
    </DialogTrigger>

    <DialogPortal
      defer
      to="#chat-page-root"
    >
      <DialogOverlay
        class="DialogOverlay fade-in fade-out absolute inset-0 z-30 rounded-b-xl bg-black/80 ease-in-out"
      />

      <DialogContent
        class="DialogContent slide-in-from-bottom slide-out-to-bottom inset-x-0 top-15/100 bottom-0 z-[100] rounded-t-[10px] bg-white ease-in-out"
        aria-describedby="undefined"
        as-child
      >
        <ScrollAreaRoot style="position: absolute">
          <div
            class="absolute inset-x-2.5 top-0 z-10 h-8 bg-gradient-to-t from-transparent to-white"
          />
          <ScrollAreaViewport class="peer max-h-full rounded-xl px-5 py-8 focus-visible:outline-0">
            <div class="text-foreground-2 mb-2 flex items-center text-xl font-bold">
              <DialogTitle class="ml-4"> 历史 </DialogTitle>
              <DialogClose class="mr-3 ml-auto cursor-pointer">
                <IconX />
              </DialogClose>
            </div>
            <ChatHistoryList />

            <ScrollAreaScrollbar orientation="vertical" />
          </ScrollAreaViewport>
          <div
            class="absolute inset-x-2.5 bottom-0 z-10 h-8 bg-gradient-to-b from-transparent to-white"
          />

          <div
            class="peer-focus-visible:inset-ring-primary pointer-events-none absolute inset-0 z-11 rounded-lg peer-focus-visible:inset-ring-2"
          />
        </ScrollAreaRoot>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<style lang="css" scoped>
  .DialogOverlay {
    animation-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
    &[data-state='open'] {
      animation-duration: 0.5s;
      animation-name: enter;
    }

    &[data-state='closed'] {
      animation-duration: 0.3s;
      animation-name: exit;
    }
  }

  .DialogContent {
    animation-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
    &[data-state='open'] {
      animation-duration: 0.5s;
      animation-name: enter;
    }

    &[data-state='closed'] {
      animation-duration: 0.3s;
      animation-name: exit;
    }
  }
</style>
