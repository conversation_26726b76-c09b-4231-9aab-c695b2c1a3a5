<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'
  import { formatDistanceToNowStrict } from 'date-fns'
  import { message } from 'ant-design-vue'

  import { injectExtraChatContext } from '../ChatPage.vue'

  import IconTrash from '~icons/lucide/trash-2'
  import IconLoading from '~icons/lucide/loader-circle'

  export interface Props {
    rawConversation: API.Chat.Conversation
  }

  const { rawConversation } = defineProps<Props>()

  const { useChatStore } = injectExtraChatContext()
  const chatStore = useChatStore()
  const { id: chatId } = storeToRefs(chatStore)

  const isActive = computed(() => rawConversation.id === chatId.value)

  const queryClient = useQueryClient()
  const { mutate: deleteConversation, status } = useMutation({
    async mutationFn() {
      return baseFetcher(`/conversation/${rawConversation.id}/`, {
        method: 'delete',
      })
    },
    async onSuccess() {
      await queryClient.invalidateQueries({ queryKey: chatQueryKey.conversationList() })
      if (rawConversation.id === chatId.value) {
        chatStore.setNewChatId()
      }
    },
    onError(err) {
      message.error(err.message)
    },
  })
</script>

<template>
  <button
    :class="
      twMerge(
        'group flex w-full cursor-pointer rounded-[5px] px-4 py-2 transition-[background-color] duration-150 ease-in-out',
        isActive && 'bg-[rgba(71,148,254,0.1)]',
        isActive === false && 'hover:bg-black/6',
      )
    "
    @click="chatId = rawConversation.id"
  >
    <div class="max-w-8/10">
      <div class="flex h-[30px] items-center space-x-3">
        <TextEllipsis
          :text="rawConversation.title"
          :lines="1"
          :tooltip="{ title: rawConversation.title, placement: 'top' }"
          class="text-foreground-2 font-medium"
        />

        <ATag
          color="error"
          class="rounded-xs"
          v-if="isActive"
        >
          当前会话
        </ATag>
      </div>
    </div>
    <div class="ml-auto flex items-center">
      <AButton
        type="text"
        size="small"
        class="icon-a-button -mr-1.5 not-group-hover:hidden!"
        @click.stop="deleteConversation()"
      >
        <IconLoading
          v-if="status === 'pending'"
          class="animate-spin"
        />
        <IconTrash v-else />
      </AButton>

      <div class="text-foreground-4 group-hover:hidden">
        {{ formatDistanceToNowStrict(new Date(rawConversation.update_at), { addSuffix: true }) }}
      </div>
    </div>
  </button>
</template>
