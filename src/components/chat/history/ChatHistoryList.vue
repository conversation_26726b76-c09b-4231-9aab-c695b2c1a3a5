<script setup lang="ts">
  const { data: rawConversations } = useQuery({
    queryKey: chatQueryKey.conversationList(),
    async queryFn() {
      const { data } = await baseFetcher<{
        data: { data: API.Chat.Conversation[]; page: number; limit: number; total: number }
      }>('/conversation/')
      return data.data
    },
  })
</script>

<template>
  <div class="space-y-2">
    <ChatHistoryItem
      v-for="conversation in rawConversations"
      :key="conversation.id"
      :raw-conversation="conversation"
    />
  </div>
</template>
