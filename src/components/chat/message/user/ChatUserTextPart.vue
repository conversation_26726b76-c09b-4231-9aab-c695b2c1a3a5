<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'
  import type { HTMLAttributes } from 'vue'

  const props = defineProps<{ class?: HTMLAttributes['class']; text: string }>()
</script>

<template>
  <div
    :class="
      twMerge(
        'text-foreground-2 w-max rounded-[10px] bg-[rgba(73,146,255,0.2)] px-4.5 py-3 whitespace-pre-wrap',
        props.class,
      )
    "
  >
    {{ props.text }}
  </div>
</template>
