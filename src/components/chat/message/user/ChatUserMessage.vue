<script setup lang="ts">
  import { injectMessageContext } from '@wicii/chat-primitive'

  const { message } = injectMessageContext()
</script>

<template>
  <ChatContainer class="pb-7 first:pt-8">
    <template
      v-for="(part, index) in message.parts"
      :key="index"
    >
      <ChatUserTextPart
        v-if="part.type === 'text'"
        :text="part.text"
        class="ml-auto"
      />
    </template>
  </ChatContainer>
</template>
