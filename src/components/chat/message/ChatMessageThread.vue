<script setup lang="ts">
  import { injectChatContext, MessageRoot } from '@wicii/chat-primitive'
  import { ScrollAreaRoot, ScrollAreaViewport } from 'reka-ui'

  const { messages, status, error } = injectChatContext()

  watchEffect(() => console.log(error.value))

  const scrollArea = useTemplateRef('scrollArea')
  useAutoScroll(() => scrollArea.value?.viewport)
</script>

<template>
  <ScrollAreaRoot
    class="-mb-1 contain-size"
    ref="scrollArea"
  >
    <div
      class="flex h-full items-center justify-center"
      v-if="messages.length === 0"
    >
      <ChatWelcome />
    </div>

    <ScrollAreaViewport
      v-else
      class="h-full pb-5"
      tabindex="-1"
      data-message-thread
    >
      <MessageRoot
        v-for="(message, index) in messages"
        :key="message.id"
        :message="message"
        :message-index="index"
        as-child
      >
        <ChatUserMessage v-if="message.role === 'user'" />
        <ChatAssistantMessage v-else-if="message.role === 'assistant'" />
      </MessageRoot>

      <ChatContainer
        v-if="status === 'submitted'"
        class="py-7"
      >
        <ChatAssistantLoader class="ml-7" />
      </ChatContainer>

      <ChatContainer v-if="status === 'error'">
        <ChatMessageError :message="`出错了，请开始新聊天：${error?.message}`" />
      </ChatContainer>
    </ScrollAreaViewport>

    <ScrollAreaScrollbar orientation="vertical" />
  </ScrollAreaRoot>
</template>
