<script setup lang="ts">
  const props = defineProps<{ text: string }>()
</script>

<template>
  <div class="flex">
    <div class="border-separator my-1.5 w-[2px] border" />

    <div class="pl-3">
      <ChatDefaultMarkdown
        :markdown="props.text"
        class="text-foreground-4 text-sm [--text-2xl:20px] [--text-3xl:26px] [--text-base:14px] [--text-lg:16px] [--text-sm:12px] [--text-xl:18px] [&_p]:leading-5"
      />
    </div>
  </div>
</template>
