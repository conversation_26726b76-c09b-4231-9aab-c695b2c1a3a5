<script setup lang="ts">
  import { injectMessageContext } from '@wicii/chat-primitive'

  import ImgBotHead from '@/assets/img/bot-head.svg'

  const { message, phase } = injectMessageContext()
</script>

<template>
  <ChatContainer class="relative pb-7">
    <div class="flex space-x-2">
      <img
        :src="ImgBotHead"
        class="size-6"
      />

      <div>
        <div class="flex items-center">
          <div class="h-5" />
          <ChatAssistantLoader
            class="ml-10"
            v-if="phase !== 'complete'"
          />
        </div>
        <template
          v-for="(part, index) in message.parts"
          :key="index"
        >
          <ChatAssistantReasoningPart
            v-if="part.type === 'reasoning'"
            :text="part.reasoning"
          />
          <ChatAssistantTextPart
            v-else-if="part.type === 'text'"
            :text="part.text"
          />
        </template>
      </div>
    </div>

    <ChatAssistantActionRoot>
      <ChatAssistantActionCopy class="ml-6!" />
    </ChatAssistantActionRoot>
  </ChatContainer>
</template>
