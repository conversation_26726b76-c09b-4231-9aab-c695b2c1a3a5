<script setup lang="ts">
  import { MessageActionRoot } from '@wicii/chat-primitive'
  import type { HTMLAttributes } from 'vue'
  import { twMerge } from 'tailwind-merge'

  const props = defineProps<{ class?: HTMLAttributes['class'] }>()
</script>

<template>
  <MessageActionRoot as-child>
    <ChatContainer
      :class="
        twMerge(
          'absolute inset-x-0 -bottom-2 z-2 flex items-center space-x-2 py-2 transition-opacity ease-in-out data-[hidden=true]:opacity-0',
          props.class,
        )
      "
    >
      <slot />
    </ChatContainer>
  </MessageActionRoot>
</template>
