<script setup lang="ts">
  import type { HTMLAttributes } from 'vue'
  import { twMerge } from 'tailwind-merge'
  import { message } from 'ant-design-vue'

  defineOptions({ inheritAttrs: false })

  const props = defineProps<{
    urlpart: string
    filename: string
    class?: HTMLAttributes['class']
  }>()

  const {
    data: chunkContent,
    status,
    error,
  } = useQuery({
    queryKey: chatQueryKey.chunkDetail(props.urlpart),
    async queryFn() {
      const { data } = await baseFetcher<{ data: { data: string } }>(props.urlpart)
      return data.data
    },
  })

  watch(error, (err) => {
    if (err) {
      message.error(err.message)
    }
  })

  const [DefineSup, ReuseSup] = createReusableTemplate()
</script>

<template>
  <DefineSup>
    <sup
      :class="
        twMerge(
          'bg-foreground-4/30 hover:bg-foreground-4/60 text-foreground-2 inline-flex size-4 cursor-default items-center justify-center rounded-full text-[10px] transition-colors',
          status === 'pending' && 'cursor-progress',
          props.class,
        )
      "
      v-bind="$attrs"
    >
      <slot />
    </sup>
  </DefineSup>

  <APopover
    v-if="status === 'success'"
    :title="props.filename"
    :content="chunkContent"
    overlay-class-name="[&_.ant-popover-inner]:max-w-[30svw] [&_.ant-popover-inner]:max-h-[40svh] [&_.ant-popover-inner]:overflow-y-auto"
  >
    <ReuseSup />
  </APopover>

  <ReuseSup v-else />
</template>
