<script setup lang="ts">
  import { injectExtraChatContext } from '../../ChatPage.vue'

  import IconPaperClip from '~icons/ant-design/paper-clip-outlined'

  const { useChatStore } = injectExtraChatContext()
  const { kbs, courses, files } = storeToRefs(useChatStore())

  const hasAttachment = computed(
    () => files.value.length > 0 || kbs.value.length > 0 || courses.value.length > 0,
  )

  function onClick() {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'text/plain,.pdf,.docx'

    input.addEventListener('change', () => {
      if (!input.files?.length) {
        return
      }

      files.value = [
        {
          file: input.files[0],
        },
      ]
    })

    input.click()
  }

  const [DefineButton, ReuseButton] = createReusableTemplate()
</script>

<template>
  <DefineButton>
    <AButton
      type="text"
      size="small"
      class="size-auto! p-1!"
      :disabled="hasAttachment"
      @click="onClick"
    >
      <IconPaperClip class="size-5" />
    </AButton>
  </DefineButton>

  <ATooltip
    v-if="hasAttachment === false"
    title="文件数量最大为1，类型为pdf、docx、txt。只识别文字"
  >
    <ReuseButton />
  </ATooltip>

  <ReuseButton v-else />
</template>
