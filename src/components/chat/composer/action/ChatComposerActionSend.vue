<script setup lang="ts">
  import { injectChatContext } from '@wicii/chat-primitive'
  import { twMerge } from 'tailwind-merge'
  import type { HTMLAttributes } from 'vue'

  import { injectExtraComposerContext } from '../ChatComposer.vue'
  import { injectExtraChatContext } from '../../ChatPage.vue'

  import IconPlane from '~icons/local/paper-plane'

  const props = defineProps<{ class?: HTMLAttributes['class'] }>()

  const { input, status } = injectChatContext()
  const { isUploading } = injectExtraComposerContext()

  const { useChatStore } = injectExtraChatContext()
  const { selectedModel } = storeToRefs(useChatStore())
</script>

<template>
  <AButton
    type="primary"
    html-type="submit"
    :class="twMerge('gradient-a-button rounded-full! px-4! transition-none!', props.class)"
    :disabled="!input || status !== 'ready' || isUploading || selectedModel === undefined"
  >
    <IconPlane class="-ml-0.5 size-4.5" />
  </AButton>
</template>
