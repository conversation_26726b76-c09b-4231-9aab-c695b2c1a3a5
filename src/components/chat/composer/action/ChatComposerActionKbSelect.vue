<script setup lang="ts">
  import type { CascaderOptionType } from 'ant-design-vue/es/cascader'
  import { useRouteQuery } from '@vueuse/router'

  import { injectExtraChatContext } from '../../ChatPage.vue'

  import IconCaretDown from '~icons/ant-design/caret-down-filled'
  import IconChevronRight from '~icons/lucide/chevron-right'
  import IconX from '~icons/material-symbols/cancel-rounded'

  const { useChatStore } = injectExtraChatContext()
  const { kbs, files, courses } = storeToRefs(useChatStore())
  const hasAttachment = computed(
    () => kbs.value.length > 0 || files.value.length > 0 || courses.value.length > 0,
  )

  const persistedSemester = useRouteQuery<string | undefined>('semester')

  const { data: rawCourses, status: fetchRawCoursesStatus } = useQuery({
    queryKey: courseQueryKey.courseList(persistedSemester.value ?? ''),
    async queryFn() {
      const { data } = await baseFetcher<{ data: { results: { id: string; title: string }[] } }>(
        '/course/',
        {
          query: {
            semester_id: persistedSemester.value,
          },
        },
      )
      return data.results
    },
    enabled: persistedSemester.value !== undefined,
  })

  const { queryOptions } = useKbList()
  const { data: rawKbs, status: fetchRawKbsStatus } = useQuery(queryOptions)

  const courseOptions = reactiveComputed(
    () =>
      rawCourses.value?.map((course) => ({
        value: course.id,
        label: course.title,
      })) ?? [],
  )
  const kbOptions = reactiveComputed(
    () =>
      rawKbs.value?.map((kb) => ({
        value: kb.id,
        label: kb.name,
      })) ?? [],
  )

  const options: CascaderOptionType[] = reactiveComputed(() => {
    const opts: CascaderOptionType[] = []

    opts.push({
      value: 'kb',
      label: '知识库',
      children: kbOptions,
    })

    if (persistedSemester.value !== undefined) {
      opts.push({
        value: 'course',
        label: '课程',
        children: courseOptions,
      })
    }

    return opts
  })

  const cascaderValue = ref<string[]>()
  watch(cascaderValue, (values) => {
    if (values === undefined) {
      return
    }

    if (values.length !== 2) {
      throw new Error('Cascader malfunctioning')
    }

    const [type, id] = values
    if (type === 'course') {
      const course = rawCourses.value?.find((course) => course.id === id)!
      if (course) {
        courses.value = [{ id: course.id, name: course.title }]
      }
    } else if (type === 'kb') {
      const kb = rawKbs.value?.find((kb) => kb.id === id)!
      if (kb) {
        kbs.value = [kb]
      }
    }
  })
  watch(
    () => files.value.length,
    (len) => {
      if (len > 0) {
        cascaderValue.value = undefined
      }
    },
  )
  watch(
    () => kbs.value.length,
    (len) => {
      if (len === 0) {
        cascaderValue.value = undefined
      }
    },
  )
</script>

<template>
  <ACascader
    v-model:value="cascaderValue"
    :display-render="({ labels }) => labels[0]"
    :options="options"
    :disabled="hasAttachment"
    expand-trigger="hover"
    placeholder="类别"
    class="reset-ant-cascader w-[100px]!"
    :loading="fetchRawCoursesStatus === 'pending' || fetchRawKbsStatus === 'pending'"
    popup-class-name="[&_.ant-cascader-menu]:max-w-[20svw] [&_.ant-cascader-menu-item-content]:max-w-full [&_.ant-cascader-menu-item-content]:whitespace-nowrap [&_.ant-cascader-menu-item-content]:overflow-hidden [&_.ant-cascader-menu-item-content]:text-ellipsis"
  >
    <template #expandIcon>
      <IconChevronRight class="text-foreground-3" />
    </template>
    <template #suffixIcon>
      <IconCaretDown class="text-foreground-3" />
    </template>
    <template #clearIcon>
      <IconX class="-translate-x-1 -translate-y-px" />
    </template>
  </ACascader>
</template>

<style scoped>
  @reference "tailwindcss";

  .reset-ant-cascader {
    &:deep() {
      .ant-select-selector {
        @apply rounded-full pr-3 pl-4.5;
      }
    }
  }
</style>
