<script setup lang="ts">
  import { ComposerActionStop } from '@wicii/chat-primitive'
  import { twMerge } from 'tailwind-merge'
  import type { HTMLAttributes } from 'vue'

  import IconSquircle from '~icons/lucide/squircle'

  const props = defineProps<{ class?: HTMLAttributes['class'] }>()
</script>

<template>
  <ComposerActionStop as-child>
    <AButton
      type="primary"
      :class="twMerge('gradient-a-button rounded-full! px-4! transition-none!', props.class)"
    >
      <IconSquircle class="size-4.5 animate-pulse **:fill-current" />
    </AButton>
  </ComposerActionStop>
</template>
