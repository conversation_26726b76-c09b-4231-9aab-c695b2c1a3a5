<script lang="ts">
  import type { HTMLAttributes } from 'vue'

  export interface Props {
    class?: HTMLAttributes['class']
    placeholder?: string
    rows?: number
    maxrows?: number
    autoresize?: boolean
  }
</script>

<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'
  import { injectChatContext, injectComposerContext } from '@wicii/chat-primitive'

  const { input } = injectChatContext()
  const { submitForm } = injectComposerContext()

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '今天需要我做些什么？Shift + Enter 换行',
    rows: 3,
    maxrows: 9,
    autoresize: true,
  })

  const textareaRef = useTemplateRef('textareaRef')

  function handleSubmit(ev: KeyboardEvent) {
    if (input.value && !ev.isComposing) {
      submitForm()
    }
  }

  function autoResize() {
    if (props.autoresize && textareaRef.value) {
      textareaRef.value.rows = props.rows
      const overflow = textareaRef.value.style.overflow
      textareaRef.value.style.overflow = 'hidden'

      const styles = window.getComputedStyle(textareaRef.value)
      const paddingTop = Number.parseInt(styles.paddingTop)
      const paddingBottom = Number.parseInt(styles.paddingBottom)
      const padding = paddingTop + paddingBottom
      const lineHeight = Number.parseInt(styles.lineHeight)
      const { scrollHeight } = textareaRef.value
      const newRows = (scrollHeight - padding) / lineHeight

      if (newRows > props.rows) {
        textareaRef.value.rows = props.maxrows ? Math.min(newRows, props.maxrows) : newRows
      }

      textareaRef.value.style.overflow = overflow
    }
  }

  function onInput() {
    autoResize()
  }

  // Watch for changes in input value to trigger autoresize
  watch(input, () => {
    nextTick(autoResize)
  })

  // Auto-resize on mount
  onMounted(() => {
    autoResize()
    textareaRef.value?.focus()
  })

  useResizeObserver(textareaRef, () => {
    nextTick(autoResize)
  })
</script>

<template>
  <textarea
    ref="textareaRef"
    v-model="input"
    :placeholder="props.placeholder"
    :rows="props.rows"
    :class="twMerge('text-foreground-2 w-full resize-none bg-transparent outline-0', props.class)"
    @input="onInput"
    @keydown.enter.exact.prevent="handleSubmit"
  />
</template>
