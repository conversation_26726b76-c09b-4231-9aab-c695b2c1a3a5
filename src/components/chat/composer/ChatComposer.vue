<script lang="ts">
  const extraComposerContextInjectionKey = Symbol('chat.composer') as InjectionKey<{
    isUploading: Readonly<Ref<boolean>>
    setIsUploading(value: boolean): void
  }>

  export function injectExtraComposerContext() {
    const context = inject(extraComposerContextInjectionKey)
    if (!context) {
      throw new Error('extraComposerContext is not provided')
    }
    return context
  }
</script>

<script setup lang="ts">
  import { ComposerForm, injectChatContext } from '@wicii/chat-primitive'

  import { injectExtraChatContext } from '../ChatPage.vue'

  const { status } = injectChatContext()

  const { useChatStore } = injectExtraChatContext()
  const { kbs, files, courses } = storeToRefs(useChatStore())

  const isUploading = ref(false)
  provide(extraComposerContextInjectionKey, {
    isUploading,
    setIsUploading(value) {
      isUploading.value = value
    },
  })
</script>

<template>
  <ComposerForm class="composer-wrapper space-y-2 rounded-[10px] p-4">
    <div
      class="flex flex-wrap gap-5"
      v-if="kbs.length > 0 || files.length > 0 || courses.length > 0"
    >
      <ChatComposerAttachmentCourse
        v-for="(course, index) in courses"
        :key="course.id"
        :name="course.name"
        @remove="courses.splice(index, 1)"
      />
      <ChatComposerAttachmentKb
        v-for="(kb, index) in kbs"
        :key="kb.id"
        :name="kb.name"
        @remove="kbs.splice(index, 1)"
      />
      <ChatComposerAttachmentFile
        v-for="(file, index) in files"
        :key="file.file.name"
        :file="file.file"
        @remove="files.splice(index, 1)"
        @uploaded="file.remotePath = $event.remotePath"
      />
    </div>

    <ChatComposerTextarea />

    <div class="flex items-center">
      <ChatComposerActionKbSelect />

      <ChatComposerActionFileUpload class="ml-4" />

      <ChatComposerWordCount
        :limit="5000"
        class="mr-4 ml-auto"
      />

      <ChatComposerActionStop v-if="status === 'submitted' || status === 'streaming'" />
      <ChatComposerActionSend v-else />
    </div>
  </ComposerForm>
</template>

<style scoped>
  .composer-wrapper {
    --background-color: white;
    background: var(--background-color);
    border-width: 2px;
    border-color: var(--background-color);

    &:has(textarea:focus-visible) {
      border-color: transparent;
      background:
        linear-gradient(var(--background-color), var(--background-color)) padding-box,
        linear-gradient(145deg, rgba(73, 146, 255, 1), rgba(32, 195, 231, 1)) border-box;
    }
  }
</style>
