<script lang="ts">
  function formatBytes(bytes: number, decimals: number = 2): string {
    if (!+bytes) return '0 Bytes'

    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`
  }
</script>

<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'
  import type { HTMLAttributes } from 'vue'
  import Uppy from '@uppy/core'
  import XHRUpload from '@uppy/xhr-upload'
  import zhCN from '@uppy/locales/lib/zh_CN'
  import { injectChatContext } from '@wicii/chat-primitive'
  import { message } from 'ant-design-vue'

  import { env } from '@/../env'
  import { injectExtraComposerContext } from '../ChatComposer.vue'

  import IconX from '~icons/material-symbols/cancel-rounded'
  import IconLoading from '~icons/lucide/loader-circle'
  import '@uppy/core/dist/style.css'

  export type Props = {
    class?: HTMLAttributes['class']
    file: File
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{
    remove: []
    uploaded: [{ uppyId: string; remoteId: string; remotePath: string }]
  }>()

  const { id: chatId } = injectChatContext()
  const { setIsUploading: setComposerIsUploading } = injectExtraComposerContext()

  const csrftoken = useCSRFToken()

  const uppy = new Uppy({
    id: 'kb-uppy',
    locale: zhCN,
    restrictions: { allowedFileTypes: ['text/plain', '.pdf', '.docx', '.doc', '.xls', '.xlsx'] },
    meta: {
      conversation_id: chatId,
    },
  }).use(XHRUpload, {
    endpoint: `${env.VITE_API_BASE_URL}/v1/chat/upload/`,
    headers: {
      'X-CSRFToken': csrftoken.value,
    },
  })

  watchEffect(() => {
    uppy.getPlugin('XHRUpload')?.setOptions({
      headers: {
        'X-CSRFToken': csrftoken.value,
      },
    })
  })

  const fileid = uppy.addFile(props.file)
  const isUploading = ref(false)
  const progress = ref(0)
  uppy.on('upload', () => {
    isUploading.value = true
    setComposerIsUploading(true)
  })
  uppy.on('progress', (value) => {
    progress.value = value
  })
  uppy.on('complete', () => {
    isUploading.value = false
    setComposerIsUploading(false)
  })
  uppy.on('error', (err) => {
    message.error(err.message)
  })
  uppy.on('upload-success', (file, response) => {
    //@ts-expect-error hard to type
    const { data } = response.body
    const res = data?.data?.[0] as { id: string; file_path: string }
    emit('uploaded', { uppyId: file!.id, remoteId: res.id, remotePath: res.file_path })
  })
  uppy.upload()

  const filenameParts = computed(() => getFilenameParts(props.file.name))

  function handleRemove() {
    uppy.removeFile(fileid)
    emit('remove')
  }
</script>

<template>
  <div
    :class="
      twMerge(
        'text-foreground-3 relative w-max rounded-[5px] bg-[rgba(71,148,254,0.05)] py-2 pr-3.5 pl-2.5',
        props.class,
      )
    "
  >
    <div class="flex space-x-2.5">
      <FileTypeImg :file-name="props.file.name" />
      <div :class="twMerge('max-w-[160px] space-y-1')">
        <TextEllipsis
          :text="filenameParts.name"
          :lines="1"
          :tooltip="{ title: props.file.name, placement: 'top' }"
          :class="twMerge('text-xs')"
        />

        <div class="flex space-x-2.5 text-[10px]">
          <div>
            {{ filenameParts.ext }}
          </div>
          <div>{{ formatBytes(props.file.size) }}</div>
        </div>
      </div>
    </div>

    <button
      class="absolute top-0 right-0 z-2 translate-x-1/2 -translate-y-1/2 cursor-pointer"
      @click="handleRemove"
    >
      <IconX class="text-foreground-3 hover:text-foreground-3/80" />
    </button>

    <div
      v-if="isUploading"
      class="absolute inset-0 flex items-center space-x-2 rounded-[5px] bg-black/60 p-3 text-white"
    >
      <IconLoading class="animate-spin" />
      <div>上传中...</div>
    </div>
  </div>
</template>
