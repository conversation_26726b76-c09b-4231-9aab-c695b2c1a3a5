<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'
  import type { HTMLAttributes } from 'vue'

  import IconX from '~icons/material-symbols/cancel-rounded'

  export type Props = {
    class?: HTMLAttributes['class']
    name: string
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{ remove: [] }>()
</script>

<template>
  <div
    :class="
      twMerge(
        'text-foreground-3 relative w-max max-w-[275px] rounded-[5px] bg-[rgba(71,148,254,0.1)] py-2 pr-3.5 pl-2.5',
        props.class,
      )
    "
  >
    <TextEllipsis
      :text="`知识库：${props.name}`"
      :lines="1"
      :tooltip="{ title: props.name, placement: 'top' }"
    />

    <button
      class="absolute top-0 right-0 translate-x-1/2 -translate-y-1/2 cursor-pointer"
      @click="emit('remove')"
    >
      <IconX class="text-foreground-3 hover:text-foreground-3/80" />
    </button>
  </div>
</template>
