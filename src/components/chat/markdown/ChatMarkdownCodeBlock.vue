<script setup lang="ts">
  import { ScrollAreaRoot, ScrollAreaViewport } from 'reka-ui'
  import { twMerge } from 'tailwind-merge'
  import { shikiLanguageIcons, getShikiHighlighter, type ShikiLanguages } from '@/utils/shiki'

  type Props = {
    class?: string
    code?: string
    language?: string
  }
  const props = withDefaults(defineProps<Props>(), {
    code: '',
  })

  defineOptions({
    inheritAttrs: false,
  })

  const languageWithDefault = computed(() => {
    const language = props.language ?? 'text'
    if (language in shikiLanguageIcons) {
      return language
    }
    return 'text'
  })

  const highlighter = await getShikiHighlighter()
  const highlightedCode = computed(() =>
    highlighter.codeToHtml(props.code, {
      lang: languageWithDefault.value,
      themes: {
        light: 'catppuccin-latte',
        dark: 'catppuccin-macchiato',
      },
    }),
  )
</script>

<template>
  <div
    class="group my-5 w-full contain-inline-size"
    :data-parsed-language="props.language"
  >
    <div
      class="flex h-12 items-center space-x-2 rounded-t-md border border-inherit py-2.5 pr-2.5 pl-4"
    >
      <component :is="shikiLanguageIcons[languageWithDefault as ShikiLanguages]" />
      <span class="text-foreground-2">{{ languageWithDefault }}</span>

      <div
        class="ms-auto opacity-0 transition-opacity duration-200 ease-in-out group-hover:opacity-100"
      >
        <CopyButton
          :source="code"
          title="复制"
          class="text-foreground-4!"
        />
      </div>
    </div>

    <ScrollAreaRoot
      :class="
        twMerge(
          'w-full rounded-b-lg border border-t-0 text-sm contain-inline-size',
          '[&_code]:block [&_code]:w-full [&_code]:px-4 [&_code]:py-3',
          '[&_pre]:rounded-b-md not-dark:[&_pre]:bg-white/70!',
        )
      "
    >
      <ScrollAreaViewport class="max-w-full rounded-md">
        <div v-html="highlightedCode" />
      </ScrollAreaViewport>

      <ScrollAreaScrollbar
        orientation="horizontal"
        class="rounded-xl px-1.5"
      />
    </ScrollAreaRoot>
  </div>
</template>
