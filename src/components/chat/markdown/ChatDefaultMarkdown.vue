<script setup lang="ts">
  import { VueMarkdown } from '@crazydos/vue-markdown'
  import remarkGfm from 'remark-gfm'
  import { ScrollAreaRoot, ScrollAreaViewport } from 'reka-ui'

  interface Props {
    markdown: string
  }

  const props = defineProps<Props>()
</script>

<template>
  <VueMarkdown
    :markdown="props.markdown"
    :remark-plugins="[remarkGfm]"
    sanitize
  >
    <template #p="{ children, ...props }">
      <p
        v-bind="props"
        class="my-2 leading-7 first:mt-0 last:mb-0"
      >
        <component :is="children" />
      </p>
    </template>

    <template #a="{ children, ...props }">
      <ChatAssistantChunkSource
        v-if="props.href.includes('/view_chunk_detail/')"
        :urlpart="props.href"
        :filename="props.title"
      >
        <component :is="children" />
      </ChatAssistantChunkSource>
      <a
        v-else
        v-bind="props"
        class="text-primary p-0"
      >
        <component :is="children" />
      </a>
    </template>

    <template #h1="{ children, ...props }">
      <h1
        v-bind="props"
        class="my-4 text-3xl font-extrabold tracking-tight lg:text-4xl"
      >
        <component :is="children" />
      </h1>
    </template>

    <template #h2="{ children, ...props }">
      <h2
        v-bind="props"
        class="mt-4 mb-3 text-2xl font-bold tracking-tight"
      >
        <component :is="children" />
      </h2>
    </template>

    <template #h3="{ children, ...props }">
      <h3
        v-bind="props"
        class="mt-4 mb-2 text-xl font-semibold tracking-tight"
      >
        <component :is="children" />
      </h3>
    </template>

    <template #h4="{ children, ...props }">
      <h4
        v-bind="props"
        class="my-2 text-lg font-medium tracking-tight"
      >
        <component :is="children" />
      </h4>
    </template>

    <template #hr="{ ...props }">
      <div
        v-bind="props"
        class="border-separator my-8 border-b"
      />
    </template>

    <template #inline-code="{ children, ...props }">
      <code
        v-bind="props"
        class="mx-[0.1rem] inline max-w-full rounded bg-black/6 px-[0.3rem] py-[0.2rem] font-mono text-sm font-medium break-all"
      >
        <component :is="children" />
      </code>
    </template>

    <template #li="{ children, ...props }">
      <li
        v-bind="props"
        class="my-2"
      >
        <component :is="children" />
      </li>
    </template>

    <template #ol="{ children, ...props }">
      <ol
        v-bind="props"
        class="mb-4 list-decimal pl-6"
      >
        <component :is="children" />
      </ol>
    </template>

    <template #ul="{ children, ...props }">
      <ul
        v-bind="props"
        class="mb-4 list-disc pl-6"
      >
        <component :is="children" />
      </ul>
    </template>

    <template #table="{ children, ...props }">
      <ScrollAreaRoot class="my-6 w-full contain-inline-size">
        <ScrollAreaViewport class="max-w-full">
          <table
            v-bind="props"
            class="min-w-max text-sm"
          >
            <component :is="children" />
          </table>
        </ScrollAreaViewport>

        <ScrollAreaScrollbar orientation="horizontal" />
      </ScrollAreaRoot>
    </template>

    <template #thead="{ children, ...props }">
      <thead
        v-bind="props"
        class="border-b"
      >
        <component :is="children" />
      </thead>
    </template>

    <template #th="{ children, ...props }">
      <th
        v-bind="props"
        class="px-4 py-3 text-left font-bold [&[align=center]]:text-center [&[align=right]]:text-right"
      >
        <component :is="children" />
      </th>
    </template>

    <template #td="{ children, ...props }">
      <td
        v-bind="props"
        class="max-w-[600px] px-4 py-3 text-left [&[align=center]]:text-center [&[align=right]]:text-right"
      >
        <component :is="children" />
      </td>
    </template>

    <template #tr="{ children, ...props }">
      <tr
        v-bind="props"
        class="not-first:border-t"
      >
        <component :is="children" />
      </tr>
    </template>

    <template #block-code="{ language, content }">
      <ChatMarkdownCodeBlock
        :language="language"
        :code="content"
      />
    </template>
  </VueMarkdown>
</template>
