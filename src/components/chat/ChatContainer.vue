<script setup lang="ts">
  import type { HTMLAttributes } from 'vue'
  import { twMerge } from 'tailwind-merge'

  const props = defineProps<{ class?: HTMLAttributes['class'] }>()
</script>

<template>
  <div
    :class="
      twMerge(
        'mx-auto w-full px-5 @4xl/chat:max-w-[700px] @5xl/chat:max-w-[820px] @6xl/chat:max-w-[960px] @7xl/chat:max-w-[1200px]',
        props.class,
      )
    "
  >
    <slot />
  </div>
</template>
