<script setup lang="ts">
  import type { ButtonProps } from 'ant-design-vue'
  import type { HTMLAttributes } from 'vue'
  import { twMerge } from 'tailwind-merge'

  import { injectExtraChatContext } from './ChatPage.vue'

  import IconPlus from '~icons/lucide/plus'

  const {
    class: className,
    type = 'text',
    size = 'small',
    title = '新建对话',
    ...props
  } = defineProps<
    ButtonProps & {
      class?: HTMLAttributes['class']
    }
  >()

  const { useChatStore } = injectExtraChatContext()
  const chatStore = useChatStore()
</script>

<template>
  <AButton
    v-bind="props"
    :type="type"
    :size="size"
    :title="title"
    :class="twMerge('icon-button size-auto! p-1.5!', className)"
    @click="chatStore.setNewChatId()"
  >
    <div
      class="flex size-4 items-center justify-center rounded-full rounded-bl-none bg-linear-135 from-[rgba(33,159,255,1)] to-[rgba(0,102,255,1)] p-1 md:size-5"
    >
      <IconPlus />
    </div>
  </AButton>
</template>

<style scoped>
  .icon-button {
    &:not(*:disabled) {
      color: white !important;
    }
  }
</style>
