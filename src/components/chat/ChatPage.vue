<script lang="ts">
  import { v4 as uuid } from 'uuid'

  const extraChatContextInjectionKey = Symbol('chat.chat') as InjectionKey<{
    useChatStore: ReturnType<typeof createChatStore>
  }>

  export function injectExtraChatContext() {
    const context = inject(extraChatContextInjectionKey)
    if (!context) {
      throw new Error('extraChatContext is not provided')
    }
    return context
  }
</script>

<script setup lang="ts">
  import { useChat, type UIMessage } from '@ai-sdk/vue'
  import { ChatProvider } from '@wicii/chat-primitive'
  import { goTryRaw } from 'go-go-try'
  import type { FetchError } from 'ofetch'

  import { env } from '@/../env'

  const props = defineProps<{ chatId?: string }>()
  const emit = defineEmits<{ 'update:chatId': [string] }>()

  const chatId = props.chatId || uuid()

  const useChatStore = createChatStore(chatId)
  const chatStore = useChatStore()
  const { id, selectedModel, kbs, courses, files } = storeToRefs(chatStore)

  watch(id, () => emit('update:chatId', id.value))

  provide(extraChatContextInjectionKey, { useChatStore })

  const { data: initialMessages, suspense } = useQuery({
    queryKey: chatQueryKey.conversationMessageList(chatId),
    async queryFn() {
      const [err, result] = await goTryRaw<{ data: { data: UIMessage[] } }, FetchError>(() =>
        baseFetcher(`/conversation/${chatId}/`),
      )
      if (err && err.status !== 404) {
        throw err
      }
      return result?.data.data
    },
  })

  await useNavigationSuspense({ delay: 500, suspense })

  const queryClient = useQueryClient()

  const helpers = useChat({
    api: `${env.VITE_API_BASE_URL}/v1/chat/`,
    id: chatId,
    generateId: uuid,
    initialMessages: initialMessages.value,
    headers: {
      'X-CSRFToken': getCSRFToken(),
    },
    experimental_prepareRequestBody({ messages, id }) {
      return {
        conversation_id: id,
        question: messages.at(-1)?.content,
        model_name: selectedModel.value?.value,
        dataset_ids: kbs.value.length > 0 ? kbs.value.map((kb) => kb.id) : undefined,
        course_id: courses.value.at(0)?.id,
        file_path: files.value.at(0)?.remotePath,
      }
    },
    onFinish() {
      chatStore.swapStopFnBufferIfStale(chatId)
    },
    onResponse() {
      return queryClient.invalidateQueries({ queryKey: chatQueryKey.conversationList() })
    },
  })

  // 维护 stop 函数，允许跨组件实例对同一 id 的对话进行取消
  {
    const { stop, status } = helpers
    if (status.value === 'ready' || status.value === 'error') {
      chatStore.getStopFnBuffer(chatId).swap()
    }
    chatStore.registerStopFn({ chatId, stop })
    onUnmounted(() => {
      chatStore.setStopFnStale(chatId, true)
    })
  }

  // 独立于组件生命周期的 effect scope，允许在标题生成完成前切换路由
  {
    const { data } = helpers
    // @ts-expect-error hard to type
    const titled = computed(() => Boolean(data.value?.find((d) => d?.title !== undefined)))

    const scope = effectScope(true)
    scope.run(() => {
      watchEffect(async () => {
        if (titled.value) {
          await queryClient.invalidateQueries({ queryKey: chatQueryKey.conversationList() })
          scope.stop()
        }
      })
    })
  }
</script>

<template>
  <div
    class="@container/chat relative flex flex-col overflow-hidden"
    id="chat-page-root"
  >
    <ChatProvider :chat-context="{ ...helpers, stop: () => chatStore.getCurrentStopFn(chatId)() }">
      <ChatMessageThread class="grow" />

      <ChatContainer class="flex items-center pb-1.5">
        <ChatModelSelector />
        <ChatHistoryButton class="ml-auto" />
        <ChatNewButton class="-mr-1 ml-2" />
      </ChatContainer>

      <ChatContainer class="pb-8">
        <ChatComposer />
      </ChatContainer>
    </ChatProvider>
  </div>
</template>
