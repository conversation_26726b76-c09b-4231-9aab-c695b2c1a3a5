<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'
  import { message } from 'ant-design-vue'
  import { v4 as uuid } from 'uuid'

  import { injectChatFloatButtonContext } from './ChatFloatButton.vue'
  import { env } from '@/../env'

  import IconX from '~icons/lucide/x'
  import IconReload from '~icons/icon-park-outline/refresh'
  import IconExport from '~icons/icon-park-outline/export'
  import ImgChatBg from '@/assets/img/chat-bg.webp'

  const props = defineProps<{
    semesterid?: string
    courseid?: string
    origin: { x: number; y: number }
  }>()

  const { expanded } = injectChatFloatButtonContext()

  const searchParams = new URLSearchParams({ pt: '60px' })
  watchEffect(() => {
    if (props.semesterid) {
      searchParams.set('semester', props.semesterid)
    } else {
      searchParams.delete('semester')
    }
  })
  watchEffect(() => {
    if (props.courseid) {
      searchParams.set('course', props.courseid)
    } else {
      searchParams.delete('course')
    }
  })

  const container = useTemplateRef('container')
  const handle = useTemplateRef('handle')
  const iframe = useTemplateRef('iframe')

  const isResizing = ref(false)

  const {
    style: dragStyle,
    x,
    y,
    isDragging,
  } = useDraggable(container, {
    preventDefault: true,
    initialValue() {
      const iframeWidth = 0.6 * window.innerHeight
      const iframeHeight = 0.9 * window.innerHeight
      return {
        x: props.origin.x - iframeWidth + 20,
        y: props.origin.y - iframeHeight + 20,
      }
    },
    handle,
    onEnd() {
      restrainX()
      restrainY()
    },
  })

  const windowSize = useWindowSize()

  const RESERVED_MARGIN = 1

  function isLeftInViewport() {
    return x.value >= 0
  }
  function isRightInViewport() {
    return x.value + container.value!.offsetWidth <= windowSize.width.value
  }
  function isTopInViewport() {
    return y.value >= 0
  }
  function isBottomInViewport() {
    return y.value + container.value!.offsetHeight <= windowSize.height.value
  }

  const { restrainX, restrainY } = (function () {
    function restrainLeft() {
      if (isLeftInViewport()) {
        return
      }
      x.value = RESERVED_MARGIN
    }

    function restrainRight() {
      if (isRightInViewport()) {
        return
      }
      x.value = windowSize.width.value - container.value!.offsetWidth - RESERVED_MARGIN
    }

    function restrainTop() {
      if (isTopInViewport()) {
        return
      }
      y.value = RESERVED_MARGIN
    }

    function restrainBottom() {
      if (isBottomInViewport()) {
        return
      }
      y.value = windowSize.height.value - container.value!.offsetHeight - RESERVED_MARGIN
    }

    function restrainX() {
      restrainLeft()
      restrainRight()
    }

    function restrainY() {
      restrainTop()
      restrainBottom()
    }

    return { restrainX, restrainY }
  })()

  const transformOriginStyle = computed(() => {
    const iframeX = x.value
    const iframeY = y.value
    const relativeX = props.origin.x - iframeX
    const relativeY = props.origin.y - iframeY
    return { transformOrigin: `${relativeX}px ${relativeY}px` }
  })

  function hasMessage() {
    return Boolean(iframe.value?.$el.querySelector('[data-message-thread]'))
  }

  function exportChat() {
    if (!chatId) {
      message.error('导出会话失败')
      return
    }
    if (!hasMessage()) {
      message.error('当前会话没有消息，无法导出')
      return
    }

    const a = document.createElement('a')
    a.href = `${env.VITE_API_BASE_URL}/v1/chat/export/${chatId.value}/`
    a.click()
  }

  // 使用防抖来处理resize事件
  const resizeDebounced = useDebounceFn(async () => {
    isResizing.value = false
    // 同步 x, y 的值
    if (!container.value) {
      return
    }
    const rect = container.value.getBoundingClientRect()
    x.value = rect.left
    y.value = rect.top

    if (isTopInViewport() === false) {
      y.value = RESERVED_MARGIN
      await nextTick()
    }
    if (isBottomInViewport() === false) {
      container.value.style.height = `${windowSize.height.value - y.value - RESERVED_MARGIN}px`
    }
    if (isLeftInViewport() === false) {
      await nextTick()
    }
    if (isRightInViewport() === false) {
      container.value.style.width = `${windowSize.width.value - x.value - RESERVED_MARGIN}px`
    }
  }, 150)

  async function onResize() {
    isResizing.value = true
    resizeDebounced()
  }

  const chatId = ref<string>(uuid())
</script>

<template>
  <div
    :class="
      twMerge(
        '@container fixed! z-10 h-[90svh] w-[60svh] scale-0 rounded-[30px] bg-white opacity-0 shadow-[0_2px_10px_rgba(67,143,254,0.1)]',
        expanded && 'scale-100 rounded-lg opacity-100',
      )
    "
    style="
      transition:
        all 0.4s cubic-bezier(0.19, 1, 0.22, 1),
        opacity 0.3s cubic-bezier(0.19, 1, 0.22, 1);
    "
    :style="[dragStyle, transformOriginStyle]"
    ref="container"
    @resize="onResize"
    v-resizable="{ minWidth: 400, minHeight: 600 }"
  >
    <div
      class="flex h-full flex-col rounded-lg bg-size-[1200px_100%] bg-left bg-repeat-x @5xl:bg-[position:-600px_0px]"
      :style="{ 'background-image': `url(${ImgChatBg})` }"
    >
      <div
        ref="handle"
        class="flex h-15 shrink-0 cursor-move items-center space-x-2 rounded-t-lg border-b border-[rgba(186,219,255,1)] pr-4"
        key=""
      >
        <div class="text-foreground-2 grow pl-9 font-[alimamashuheiti] text-lg">数智科教平台AI</div>

        <AButton
          type="text"
          size="small"
          class="size-auto! p-1!"
          title="导出会话"
          @click="exportChat()"
        >
          <IconExport class="text-foreground-3" />
        </AButton>

        <!-- <AButton
          type="text"
          size="small"
          class="size-auto! p-1!"
          title="刷新"
        >
          <IconReload class="text-foreground-3" />
        </AButton> -->

        <AButton
          type="text"
          size="small"
          class="size-auto! p-1!"
          title="关闭"
          @click="expanded = false"
        >
          <IconX class="text-foreground-3" />
        </AButton>
      </div>

      <ChatPage
        :class="['grow rounded-b-lg', (isDragging || isResizing) && 'pointer-events-none']"
        :chat-id="chatId"
        :key="chatId"
        @update:chat-id="chatId = $event"
        ref="iframe"
      />
    </div>
  </div>
</template>
