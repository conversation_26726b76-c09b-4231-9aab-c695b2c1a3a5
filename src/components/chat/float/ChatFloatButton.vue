<script lang="ts">
  const chatFloatButtonContextInjectionKey = Symbol('chat.float-button') as InjectionKey<{
    expanded: Ref<boolean>
  }>

  export function injectChatFloatButtonContext() {
    const context = inject(chatFloatButtonContextInjectionKey)
    if (!context) {
      throw new Error('chatFloatButtonContext is not provided')
    }
    return context
  }
</script>

<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'

  import ImgBotHead from '@/assets/img/bot-head.svg'

  const props = defineProps<{
    x: number
    y: number
  }>()

  const expanded = ref(false)
  provide(chatFloatButtonContextInjectionKey, {
    expanded,
  })

  function onTriggerClicked() {
    expanded.value = true
  }

  const center = computed(() => ({
    x: props.x + 26,
    y: props.y + 26,
  }))

  const style = computed(() => ({
    left: Math.min(props.x, window.innerWidth - 52 - 8) + 'px',
    top: Math.min(props.y, window.innerHeight - 52 - 8) + 'px',
  }))
</script>

<template>
  <Transition
    class="duration-300 ease-[cubic-bezier(0.19,1,0.22,1)]"
    enter-active-class="animate-in fade-in"
    leave-active-class="animate-out fade-out"
  >
    <img
      v-if="expanded === false"
      :src="ImgBotHead"
      :class="twMerge('fixed z-10 size-13 cursor-pointer')"
      @click="onTriggerClicked"
      @dragstart.prevent
      :style="style"
    />
  </Transition>

  <slot
    name="iframe"
    :center="center"
  />
</template>
