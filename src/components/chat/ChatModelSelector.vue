<script setup lang="ts">
  import { message } from 'ant-design-vue'
  import {
    DropdownMenuArrow,
    DropdownMenuContent,
    DropdownMenuPortal,
    DropdownMenuRadioGroup,
    DropdownMenuRadioItem,
    DropdownMenuRoot,
    DropdownMenuTrigger,
    ScrollAreaRoot,
    ScrollAreaViewport,
  } from 'reka-ui'
  import { twMerge } from 'tailwind-merge'
  import { useRouteQuery } from '@vueuse/router'

  import { injectExtraChatContext } from '@/components/chat/ChatPage.vue'

  import IconCaretDown from '~icons/ant-design/caret-down-filled'
  import IconLoading from '~icons/lucide/loader-circle'

  defineOptions({ inheritAttrs: false })

  const { useChatStore } = injectExtraChatContext()
  const { selectedModel } = storeToRefs(useChatStore())

  type Model = {
    value: string
    label: string
  }

  const persistedModel = useRouteQuery<undefined | string>('model')

  const { queryOptions } = useOpenaiModelList({ categories: ['llm'] })
  const { data: rawModels, status, error } = useQuery(queryOptions)
  watch(error, (err) => {
    if (err) {
      message.error(err.message)
    }
  })

  watch(
    rawModels,
    (rawModels) => {
      if (!rawModels?.length) {
        return
      }
      if (persistedModel.value && rawModels.find((item) => item.id === persistedModel.value)) {
        selectedModel.value = { value: persistedModel.value, label: persistedModel.value }
      } else {
        selectedModel.value = { value: rawModels[0].id, label: rawModels[0].id }
      }
    },
    { immediate: true },
  )

  const models = computed<Model[]>(
    () => rawModels.value?.map((model) => ({ value: model.id, label: model.id })) ?? [],
  )

  function onSelectMenuItem(model: Model) {
    selectedModel.value = model
    persistedModel.value = model.value
  }
</script>

<template>
  <DropdownMenuRoot>
    <DropdownMenuTrigger
      :class="
        twMerge(
          'flex cursor-pointer items-center rounded-sm px-2.5 py-1 transition-[background-color] ease-in-out not-disabled:hover:bg-black/6 disabled:cursor-not-allowed',
          $attrs.class as string,
        )
      "
      :disabled="status === 'pending' || !rawModels?.length"
    >
      <template v-if="status === 'pending'">
        <IconLoading class="text-foreground-3 animate-spin" />
        <div class="text-foreground-4 ml-1">选择模型</div>
      </template>

      <template v-else-if="rawModels?.length">
        <div class="text-foreground-2 mr-1">{{ selectedModel?.label }}</div>
        <IconCaretDown class="text-foreground-3" />
      </template>

      <template v-else>
        <div class="text-foreground-4">暂无可用模型</div>
      </template>
    </DropdownMenuTrigger>

    <DropdownMenuPortal>
      <DropdownMenuContent
        class="DropdownMenuContent zoom-in fade-in zoom-out fade-out relative z-999 rounded-lg bg-white shadow-[0_2px_19px_rgba(29,79,153,0.1)]"
        side="top"
        align="start"
        as-child
      >
        <ScrollAreaRoot>
          <div
            class="absolute inset-x-2.5 top-0 z-10 h-2.5 bg-gradient-to-t from-transparent to-white"
          />
          <ScrollAreaViewport class="max-h-[220px] rounded-lg p-2.5">
            <DropdownMenuRadioGroup
              :model-value="selectedModel?.value"
              class="space-y-1.5"
            >
              <DropdownMenuRadioItem
                v-for="model in models"
                :value="model.value"
                :key="model.value"
                @select="onSelectMenuItem(model)"
                class="text-foreground-2 cursor-pointer rounded-[3px] px-[10px] py-[5px] text-sm transition-[background-color] ease-in-out data-highlighted:bg-black/6 data-[state=checked]:bg-[rgba(63,140,255,0.1)]"
              >
                {{ model.label }}
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </ScrollAreaViewport>

          <ScrollAreaScrollbar orientation="vertical" />

          <div
            class="absolute inset-x-2.5 bottom-0 z-10 h-2.5 bg-gradient-to-b from-transparent to-white"
          />
          <DropdownMenuArrow class="fill-white" />
        </ScrollAreaRoot>
      </DropdownMenuContent>
    </DropdownMenuPortal>
  </DropdownMenuRoot>
</template>

<style lang="css" scoped>
  .DropdownMenuContent {
    animation-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
    animation-duration: 0.2s;
    transform-origin: var(--reka-dropdown-menu-content-transform-origin);

    &[data-state='open'] {
      animation-name: enter;
    }

    &[data-state='closed'] {
      animation-name: exit;
    }
  }
</style>
