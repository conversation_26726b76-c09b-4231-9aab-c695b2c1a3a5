<script lang="ts">
  import ImgDeepSeek from '@/assets/img/deepseek.svg'
  import ImgQwen from '@/assets/img/qwen.svg'

  function getModelImg(modelName: string) {
    modelName = modelName.toLowerCase()
    if (modelName.includes('deepseek')) {
      return ImgDeepSeek
    } else if (modelName.includes('qwen') || modelName.includes('qwq')) {
      return ImgQwen
    } else {
      return ''
    }
  }
</script>

<script setup lang="ts">
  const props = defineProps<{ modelName: string }>()

  const image = computed(() => getModelImg(props.modelName))
</script>

<template>
  <div class="border-separator rounded-[3px]">
    <img :src="image" />
  </div>
</template>
