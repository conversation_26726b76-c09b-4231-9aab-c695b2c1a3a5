<template>
    <div>
        <a-table :class="twMerge(
                'reset-ant-table-pagination',
                '[&_.ant-table-pagination-right]:items-center ',
                 'rounded-[5px] bg-white [&_.ant-pagination]:px-5! [&_.ant-table-content]:px-5!',
                '[&_.ant-table-thead_.ant-table-cell]:bg-white! [&_.ant-table-thead_.ant-table-cell]:before:hidden',
                '[&_.ant-table-row]:h-[58px]',
                'shadow-[0px_2px_19px_rgba(29,79,153,0.05)]'
            )" 
            :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }" :columns="columns"
            :data-source="data" 
            :pagination="{ size: 'small', showSizeChanger: false, total: total, pageSize: 10 }"
            @change="handleTableChange"
        >
            <template #bodyCell="{ column, record }">
                <div
                    v-if="column.key === 'action'"
                    class="flex items-center justify-center"
                    @click="onClickChange(record.key)"
                >
                    <IconTrash class="text-[#3F8CFF]" />
                </div>
            </template>
        </a-table>

    </div>
</template>
<script lang="ts" setup>
import { twMerge } from 'tailwind-merge'
import { computed, reactive } from 'vue';
import IconTrash from '~icons/lucide/trash-2'
// 在 script setup 中添加以下代码
const emits = defineEmits<{
    (e: 'selection-change', keys: Key[]): void;
    (e: 'pagination-change', pagination: any): void;
}>()
import type { ColumnType } from 'ant-design-vue/es/table'
type Key = string | number;

interface DataType {
    key: Key;
    name: string;
    username: number;
}

const columns:ColumnType<DataType>[] = [
    {
        title: '姓名',
        dataIndex: 'name',
        align: 'center',
        width: '300px',
    },
    {
        title: '工号',
        dataIndex: 'username',
        align: 'center',
    },
    {
        title: '操作',
        align: 'center',
        key:'action',
    },
];

const props = defineProps<{
    teamList: any[],
    total: number
}>()
const data = ref<DataType[]>([]);

watch(() => props.teamList, (newVal, oldVal) => { 
    console.log(newVal,'添加数据')
    data.value = []
    newVal.forEach((item: any) => { 
        data.value.push({
            key: item.id,
            name: item.teacher.first_name,
            username: item.teacher.username || '',
        });
    })
})

const handleTableChange = (pagination: any) => {
    emits('pagination-change', pagination);
}

const state = reactive<{
    selectedRowKeys: Key[];
    loading: boolean;
}>({
    selectedRowKeys: [],
    loading: false,
});

const onSelectChange = (selectedRowKeys: Key[]) => {
    state.selectedRowKeys = selectedRowKeys;
    emits('selection-change', selectedRowKeys);
};
const onClickChange = (data:any) => {
    console.log([data],'selectedRowKeys')
    emits('selection-change', data);
};
</script>

<style scoped></style>