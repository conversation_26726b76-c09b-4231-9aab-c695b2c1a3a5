<script lang="ts" setup>
import { courseAdd, courseListapi, courseDelete, courseEdit, semesterList,courseCopy, courseReopen } from '@/services/api/course'
import { twMerge } from 'tailwind-merge'
import uploadAnt from '@/components/ui/uploadant.vue'
import { getCourse } from '@/utils/auth'
import { Form } from 'ant-design-vue';
import { message } from 'ant-design-vue';
const useForm = Form.useForm
import { useUserStore } from '@/stores/user'
const userInfo = storeToRefs(useUserStore())
const course = ref({
    id: '',
    title: '',
    description: '',//描述
    semester: '',
    knowledge_uid: '',
    image: '',
    imageCourse:'',
    teacher: '',
    role: '',
});

interface optionsType {
    label: string;
    value: number;
}

const options = ref<optionsType[]>([]) //学期列表数据
async function getsemesterList() {
    const data = await semesterList()
    options.value = data.data.map((item: any) => {
        return {
            value: item.id,
            label: item.name
        }
    })
}
getsemesterList()

const { data: rawKbs } = useQuery({
    queryKey: kbQueryKey.kbList(),
    async queryFn() {
        const { data } = await kbFetcher<{ data: { datasets: API.Kb.Kb[] } }>('/datasets/', {
            query: {
                page_size: 9999,
                source: 'local',
            },
        })
        return data.datasets
    },
})

const kbsTransformed = computed(
    () =>
        rawKbs.value?.map((kb) => ({
            value: kb.id,
            label: kb.name, // 修复：将 lable 改为 label
        })) ?? [],
)
console.log(kbsTransformed,'知识库列results表')

const data = getCourse();
course.value = {
    ...course.value,
    ...data,
};
course.value.semester = data.semestername.value;
course.value.imageCourse = data.image;
course.value.knowledge_uid = data.knowledge_uid || '';
console.log(course.value,'课程表单数据')

//获取封面文件流
const getfile = (file: any) => {
    console.log(file,'获取封面文件流')
    course.value.image = file
}
const delFile = (file: any) => {
    course.value.image = ''
}

const courseRules = reactive({
    title: [{ required: true, message: '请输入课程名称', trigger: 'change' }],
    image: [{ required: true, message: '请上传课程封面', trigger: 'change' }],
    semester: [{ required: true, message: '请选择课程学期', trigger: 'change' }],
    // knowledge_uid: [{ required: true, message: '请选择知识库', trigger: 'change' }],
})
const { resetFields, validate, validateInfos } = useForm(course, courseRules);//验证课程表单提交

const loadingSubmit = ref(false);
//保存
function saveCourse() { 
    validate().then(() => {
        loadingSubmit.value = true
        console.log('submit', course);
        const formData = new FormData();
        formData.append('title', course.value.title);
        formData.append('semester', JSON.stringify(course.value.semester)); // 转为字符串
        // formData.append('knowledge_uid', course.value.knowledge_uid);//course.konwelage'4fea0a6662dd11f0b0db4ad5a33f453a'
        console.log(typeof course.value.image,'sssssss')
        if(typeof(course.value.image) != 'string'){
            formData.append('image', course.value.image);
        }
        // formData.append('teacher', JSON.stringify(userInfo.value.id));
        // formData.append('role', userInfo.value.role);

        formData.append('id', course.value.id);
        
        courseEdit(formData).then(res => {
            loadingSubmit.value = false
            open.value = false
            message.success('保存成功')
        }).catch(err => {
            loadingSubmit.value = false
            message.error('保存失败')
        })
        
    }).catch(err => {
        console.log('error', err);
    });
}
const modelTitle = ref()
const open = ref(false)
//重开
function reOpen() { 
    modelTitle.value = '重新开课'
    open.value = true
}

//复制
function copeCourse() { 
    modelTitle.value = '复制课程'
    open.value = true
}

const loading = ref(false)
//确定按钮
function submitForm() {
    loading.value = true
    if(modelTitle.value == '重新开课'){
        if(course.value.semester == getCourse().semestername.value){
            message.error('重开课程学期不能与原学期相同')
            loading.value = false
            return
        }
        const params = {
            course: course.value.id,
            semester: course.value.semester,
            user: userInfo.id.value
        }
        console.log(params,'重新开课')
        courseReopen(params).then((res:any) => { 
            loading.value = false
            // if(res.code == 200 ){
                open.value = false
                message.success(res.message)
            // }
        })
        return
    }
    const params = {
        new_title:course.value.title,
        new_semester_id:course.value.semester,
        course_id: course.value.id,
        semester_id: getSemester().value,
    }
    console.log(params,'复制课程')
    courseCopy(params).then((res:any) => { 
        loading.value = false
        open.value = false
        message.success(res.message)
    })
}


</script>
<template>
    <div class="bg-[#fff] rounded-[5px] pt-[46px] pb-[41px] mt-[20px] flex flex-col justify-center items-center 
        ">
        <div class="px-[20px]! w-full max-w-[640px] mx-auto">
            <div class="text-[#333333] text-[18px] font-[700] leading-[24px] mb-[50px]">课程管理</div>
            <a-form :model="course" name="basic" layout="vertical" autocomplete="off" :class="twMerge(
                '[&_.ant-form-item]:mb-[20px]!',
            )">
                <a-form-item label="课程名称：" v-bind="validateInfos.title">
                    <a-input v-model:value="course.title" placeholder="请输入课程名称" />
                </a-form-item>
                <a-form-item label="课程封面：" v-bind="validateInfos.image">
                    <uploadAnt :urlList="course.imageCourse" @beforeUploadgetfile="getfile" @deleteFile="delFile" />
                </a-form-item>
                <a-form-item label="学期：" v-bind="validateInfos.semester">
                    <a-select v-model:value="course.semester" placeholder="请选择课程学期"
                        :options="options"></a-select>
                </a-form-item>
                <a-form-item label="知识库：" v-bind="validateInfos.knowledge_uid">
                    <a-select v-model:value="course.knowledge_uid" placeholder="请选择绑定知识库"
                        :options="kbsTransformed" />
                </a-form-item>
                <div class="mt-[123px]">
                    <a-form-item>
                        <div class="flex justify-between flex-wrap gap-y-2 ">
                            <div>
                                <AButton :loading="loadingSubmit" type="primary" class="gradient-a-button w-[82px] h-[32px]" @click="saveCourse">
                                    保存
                                </AButton>
                            </div>
                            <div class="flex gap-[20px] flex-wrap">
                                <AButton type="primary" ghost class="outline-a-button flex! items-center w-[82px]" @click="reOpen">
                                    重新开课
                                </AButton>
                                <AButton type="primary" ghost class="outline-a-button flex! items-center w-[82px]" @click="copeCourse">
                                    复制课程
                                </AButton>
                            </div>
                        </div>
                    </a-form-item>
                </div>

            </a-form>
        </div>


        <a-modal v-model:open="open" :title="modelTitle" :footer="null" width="700px">
            <div style="margin: 50px 0 0 0;">
                <a-form :model="course" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }"
                    autocomplete="off">
                    <a-form-item label="课程名称" v-bind="validateInfos.title">
                        <a-input v-model:value="course.title" placeholder="请输入课程名称" :disabled="modelTitle == '重新开课'" />
                    </a-form-item>
                    <a-form-item label="课程学期" v-bind="validateInfos.semester">
                        <a-select v-model:value="course.semester" show-search placeholder="请选择课程学期"
                            :options="options"></a-select>
                    </a-form-item>
                    <!-- <a-form-item label="知识库">
                        <a-select v-model:value="course.knowledge_uid" show-search placeholder="请选择绑定知识库"
                            :options="kbsTransformed" />
                    </a-form-item> -->
                    <a-form-item label="课程封面" v-bind="validateInfos.image">
                        <uploadAnt :urlList="course.imageCourse" @beforeUploadgetfile="getfile" :disable="true" @deleteFile="delFile" />
                    </a-form-item>
                    <div style="display: flex;justify-content: flex-end">
                        <a-form-item>
                            <a-space>
                                <a-button @click=" open = false">取消</a-button>
                                <a-button type="primary" html-type="submit" @click="submitForm" :loading="loading">确定</a-button>
                            </a-space>
                        </a-form-item>
                    </div>
                </a-form>
            </div>
        </a-modal>


    </div>
</template>
