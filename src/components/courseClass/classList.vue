<template>
  <div class="list-sidebar">
    <div class="list-top">
        <span>班级列表</span>
        <AButton type="primary" size="small" @click="$emit('add-class')" class="new-btn">新建</AButton>
    </div>
    <div class="list-container hide-scrollbar">
      <AMenu class="custom-class-menu" :selected-keys="selectedClassKey" @select="onSelect">
        <AMenuItem
          v-for="cls in classes"
          :key="String(cls.id)"
          :value="String(cls.id)"
          class="flex items-center justify-between h-10 mb-1! px-[20px]! group"
           :style="hoveredId == cls.id ? { 
            backgroundColor: '#eaf4ff',
            borderRadius: '8px',
            color: '#222',
            fontWeight: 500
          } : {}"
           @mouseenter="onEnter(cls.id)" @mouseleave="onLeave"
         >
          <span class="class-name has-dot">{{ cls.name }}</span>
          <div class="ml-2 flex items-center justify-end">
            <ADropdown placement="bottomLeft" class="">
              <template #overlay>
                <AMenu @mouseenter="onEnter(cls.id)" @mouseleave="onLeave">
                  <AMenuItem @click="$emit('settings-class', cls)" class="hover:bg-[#ECF4FF]! p-[10px]! mx-[6px]! mt-[6px]!">
                    <img src="@/assets/icon/settings.svg" alt="自定义图标" class="w-4 h-4 inline-block align-middle" />
                    <span class="ml-1 align-middle text-[#666666] leading-[14px]!">班级设置</span>
                  </AMenuItem>
                  <AMenuItem @click="$emit('delete-class', cls.id)" class="hover:bg-[#ECF4FF]! p-[10px]! mx-[6px]! mb-[6px]!">
                    <img src="@/assets/icon/delete-circle.svg" alt="自定义图标" class="w-4 h-4 inline-block align-middle" />
                    <span class="ml-1 align-middle text-[#666666] leading-[14px]!">删除</span>
                  </AMenuItem>
                </AMenu>
              </template>
              <!-- opacity-0 group-hover:opacity-100 transition-opacity duration-200   -->
              <span class="flex items-center justify-center h-7 rounded-full  hover:bg-[#eaf4ff]">
                <img v-if="hoveredId == cls.id" :src="dotthreeIcon" alt="更多" class="w-[18px] h-[18px] cursor-pointer align-middle block" />
              </span>
            </ADropdown>
          </div>
        </AMenuItem>
      </AMenu>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, computed } from 'vue'
import { useMouseHover } from '@/hooks/useMouseHover'
const { hoveredId, onEnter, onLeave } = useMouseHover() // 
export interface ClassType{
  id: number
  name: string
  semester: string
}

const props = defineProps<{ classes: ClassType[], selectedClass: ClassType | null }>()
const emits = defineEmits(['add-class', 'rename-class', 'settings-class', 'delete-class', 'update:selectedClass'])

const selectedClassKey = computed(() => props.selectedClass ? [props.selectedClass.id.toString()] : [])

function onSelect({ key }: { key: string | number }) {
  const cls = props.classes.find(c => c.id.toString() === key.toString())
  emits('update:selectedClass', cls)
}

const dotthreeIcon = new URL('@/assets/icon/dotthreeIcon.png', import.meta.url).href
const open = ref(true)


</script>

<style scoped lang="scss">
/* 左侧列表区域 */
.list-sidebar {
  width: 300px;
  margin: 20px;
}

/* 列表顶部 */
.list-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

/* 列表容器 */
.list-container {
  overflow-y: auto;
  margin: 10px 0 10px 0;
  height: 700px;
}


/* 自定义菜单 */
.custom-class-menu {
  border-right: none;
  border-inline-end: none !important;
}

/* 班级名称 */
.has-dot::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #3f8cff;
  margin-right: 8px;
  vertical-align: middle;
}

.class-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;/* 防止长名称溢出 */
  white-space: nowrap;
}

.new-btn {
  opacity: 1;
  border-radius: 3px;
  background: rgba(63, 140, 255, 1);
  border: none;
  color: #fff;
  font-size: 14px;
  box-sizing: border-box;
}
:deep(.ant-menu-title-content) {
  display: flex !important;
  flex-wrap: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* antd 选中样式同步 */
:deep(.ant-menu-item) {
  margin-inline: 0 !important;
  width: 100% !important;
}

:deep(.ant-menu-item:hover) {
  background: #eaf4ff !important;
  border-radius: 8px !important;
  color: #222 !important;
  font-weight: 500  !important;
}

:deep(.custom-class-menu .ant-menu-item-selected) {
  background-color: #fff;
  color: #3f8cff !important;
}

</style>