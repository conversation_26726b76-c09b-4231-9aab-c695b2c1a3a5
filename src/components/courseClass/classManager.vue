<template>
  <div class="class-manager shadow-[0px_2px_19px_rgba(29,79,153,0.05)]" ref="classManagerRef">
    <!-- 班级列表 -->
    <ClassList :classes="classes" :selectedClass="selectedClass" @add-class="handleAddClass"
      @settings-class="handleSetClass" @delete-class="handleDeleteClass" @update:selectedClass="handleSelectClass" />

    <!-- 分割线 -->
    <ADivider type="vertical" style="height: 100%; color: #f0f0f0; background-color: #f0f0f0; border-color: #f0f0f0;" />

    <!-- 班级学生列表 -->
    <div class="main-content">
      <!-- 班级名称 -->
      <div class="header-title">
        <img src="@/assets/icon/label.svg" alt="自定义图标" style="width: 1em; height: 1em; fill: currentColor;" />

        <a-tooltip placement="top">
          <template #title>
            <span>{{ selectedClass?.name }}</span>
          </template>
          <div class="max-w-[40%] truncate" >{{ selectedClass?.name }}</div>
        </a-tooltip>
      </div>
      <!-- 班级操作 -->
      <div class="header gap-y-4">
        <!-- 搜索 -->
        <div class="flex items-center px-(--page-px)">
          <AInput class="w-[302px]! rounded-full!" v-model:value="searchValue" placeholder=""
            @change="handleFetchClassStudents(selectedClass)">
            <template #suffix>
              <IconSearch class="text-foreground-4" @click="handleFetchClassStudents(selectedClass)" />
            </template>
          </AInput>
          <div class="flex items-center text-foreground-3 ml-4 whitespace-nowrap">
            共有
            <span class="text-primary mx-1">{{ students.length }}</span>
            个筛选结果
          </div>
        </div>
        <!-- 班级操作按钮 -->
        <div class="button-row">
          <AButton type="primary" ghost class="outline-a-button flex! w-[82px] items-center gap-2"
            :disabled="!selectedRows.length" @click="removeStus">
            <img src="@/assets/icon/delete-box.svg" alt="自定义图标" style="width: 1em; height: 1em; fill: currentColor;"
              :style="{ filter: !selectedRows.length ? 'grayscale(1) brightness(1.5)' : 'none' }" />
            移除
          </AButton>
          <ADropdown placement="bottomLeft">
            <AButton type="primary" class="gradient-btn btn-32 ml-2.5 gradient-a-button w-[82px]">添加学生</AButton>
            <template #overlay>
              <AMenu>
                <AMenuItem @click="handleManualImport">
                  <span style="display: flex; align-items: center;">
                    <img src="@/assets/icon/manual-import.svg" alt="自定义图标"
                      style="width: 1em; height: 1em; fill: currentColor;" />
                    <span class="ml-1 text-[#666666]">手动导入</span>
                  </span>
                </AMenuItem>
              </AMenu>
            </template>
          </ADropdown>

          <!-- 手动导入学生弹框 -->
          <AModal v-model:open="dialogImportFormVisible" title="手动导入学生" width="500px" :footer="null" centered
            :getContainer="() => $refs.classManagerRef as HTMLElement">
            <AForm :model="formImport" :rules="importRules" ref="importFormRef" layout="horizontal">
              <AFormItem label="导入学号" name="studentId">
                <ATextarea :rows="10" v-model:value="formImport.studentId" autocomplete="off" />
                <span style="display: flex; align-items: center; margin-top: 8px;">
                  <img src="@/assets/icon/tips-black.svg" alt="自定义图标"
                    style="width: 1em; height: 1em; fill: currentColor;" />
                  <span style="margin-left: 5px;">每个学号一行，可多行输入</span>
                </span>
              </AFormItem>
            </AForm>
            <div class="flex justify-end gap-2 w-full">
              <AButton @click="dialogImportFormVisible = false">取 消</AButton>
              <AButton type="primary" :loading="addStudentLoading" @click="handleImportStudents">确 定</AButton>
            </div>
          </AModal>
        </div>
      </div>
      <!-- 班级学生列表 -->
      <a-spin :spinning="spinning">
        <StudentTable :students="students"  :total="total"
          @delete-student="handleDeleteStudent"
          @selection-change="handleSelectionChange" @pagination-change="handlePageChange" />
      </a-spin>
    </div>

    <!-- 新建班级弹框 -->
    <AModal v-model:open="dialogFormVisible" title="新建班级" width="500px" :footer="null" centered
      :getContainer="() => $refs.classManagerRef as HTMLElement">
      <AForm ref="ruleNewClassFormRef" :rules="formClassRules" :model="formClass" layout="horizontal">
        <AFormItem label="班级名称：" name="name">
          <AInput v-model:value="formClass.name" autocomplete="off" />
        </AFormItem>
        <AFormItem label="班级学期：" name="semester">
          <ASelect v-model:value="formClass.semester" placeholder="请选择学期" :disabled="true">
            <ASelectOption :value="semester.value">{{ semester.label }}</ASelectOption>
          </ASelect>
        </AFormItem>
      </AForm>
      <div class="flex justify-end gap-2 w-full">
        <AButton @click="cancelHandleNewClass()">取 消</AButton>
        <AButton type="primary" :loading="addclassLoading" @click="handleNewClass(ruleNewClassFormRef)">确 定</AButton>
      </div>
    </AModal>

    <!-- 班级设置弹框 -->
    <AModal v-model:open="dialogClsSetFormVisible" title="班级设置" width="500px" :footer="null" centered
      :getContainer="() => $refs.classManagerRef as HTMLElement" >
      <AForm ref="ruleSetClassFormRef" :rules="formClassRules" :model="formClassSettings" layout="horizontal" :label-col="labelCol">
        <AFormItem label="班级名称：" name="name">
          <AInput v-model:value="formClassSettings.name" autocomplete="off" :maxlength="50" />
        </AFormItem>
        <AFormItem label="班级学期：" name="settings-class">
          <ASelect v-model:value="formClassSettings.semester" :disabled="true">
            <ASelectOption :value="semester.label">{{ semester.label }}</ASelectOption>
          </ASelect>
        </AFormItem>
      </AForm>
      <div class="flex justify-end gap-2 w-full">
        <AButton @click="cancelSetClass()">取 消</AButton>
        <AButton type="primary" @click="handleSettingsClass(ruleSetClassFormRef)">确 定</AButton>
      </div>
    </AModal>

  </div>
</template>

<script lang="ts" setup>
import { getCourse, getSemester } from '@/utils/auth'
import { useUserStore } from '@/stores/user'
const userInfo = storeToRefs(useUserStore())

const course = getCourse()
const semester = getSemester()

import { reactive, ref, onMounted, watch, computed } from 'vue'
import IconSearch from '~icons/ant-design/search-outlined'
import type { FormInstance } from 'element-plus'
import ClassList from './classList.vue'
import StudentTable from './studentTable.vue'
import type { StudentType } from './studentTable.vue'
import type { ClassType } from './classList.vue'
import type { RuleObject } from 'ant-design-vue/es/form'
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import { message } from 'ant-design-vue'
import { courseClassList, addCourseClass, classSettings, classDelete, classStudentList, classStudentAdd, classStudentDelete } from '@/services/api/courseClass'

// 状态定义
const dialogFormVisible = ref(false) // 新建班级弹框
const dialogClsSetFormVisible = ref(false) // 班级设置弹框
const dialogImportFormVisible = ref(false) // 手动导入学生学号弹框
const searchValue = ref('') // 搜索
const selectedRows = ref<StudentType[]>([])
const currentPage = ref(1)
const total = ref(0)

// 表单数据
const formClass = reactive({
  name: '',
  semester: semester.value
})
const formClassSettings = reactive({
  id: '',
  name: '',
  semester: semester.value
})
const labelCol = { style: { width: '80px' } };
const formImport = reactive({
  studentId: ''
})

// 班级数据
const classes = ref<ClassType[]>([])
// 班级学生列表数据
const students = ref<StudentType[]>([])

const selectedClass = ref<ClassType | null>(null)


// 表单验证规则
const formClassRules: { [key: string]: RuleObject[] } = {
  name: [
    { required: true, message: '请输入班级名称' },
    // {
    //   validator: (_rule, value) => {
    //     if (!value) return Promise.reject('请输入班级名称');
    //     if (value.length < 2 || value.length > 20) return Promise.reject('长度在2-20之间');
    //     return Promise.resolve();
    //   }
    // }
  ],
  semester: [
    { required: true, message: '请选择班级学期' }
  ]
};
// 表单验证规则——导入学号
const importRules: { [key: string]: RuleObject[] } = {
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' },
    {
      validator: (_rule, value) => {
        // if (!value) return Promise.reject('请输入学号')
        if (value.length > 1000) return Promise.reject('输入内容不能超过1000个字符')
        if (/[^0-9,\n\r]/g.test(value)) return Promise.reject('包含非法字符')
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 表单引用
const ruleNewClassFormRef = ref<FormInstance>() // 新建班级表单
const ruleSetClassFormRef = ref<FormInstance>() // 班级设置表单
const importFormRef = ref<FormInstance>() // 导入学号表单

// 表格选择变更处理
const handleSelectionChange = (selection: StudentType[]) => {
  selectedRows.value = selection
}

//删除学生（多个）
async function removeStus() {
  const confirmed = await showDeleteConfirm('确定删除多条学生记录吗？删除后无法恢复！');
  if (confirmed) {
    const studentsId = selectedRows.value.map((item: StudentType) => item.id)
    const studentsIds = students.value.map((item: any) => {
      if (studentsId.includes(item.student.id)) {
        return item.id
      }
    }).filter((item) => item !== undefined)
    const params = {
      ids: studentsIds // 用数组包裹
    }
    // 调用删除学生接口
    classStudentDelete(params).then((res: any) => {
      if (res.code == 200) {
        message.success(res.message || '删除学生成功')
        handleFetchClassStudents(selectedClass.value)
      }
    }).catch((err: any) => {
      message.error(err?.message || '删除学生失败')
      console.log('删除学生失败', err)
    })
  }
}

// 打开新建班级弹框
function handleAddClass() {
  formClass.name = ''
  dialogFormVisible.value = true
}
// 打开班级设置弹框
const handleSetClass = (data:any) => {
  console.log("打开班级设置弹框", selectedClass.value,data)
  // selectedClass.value = data
  formClassSettings.id = data.id
  formClassSettings.name = data.name 
  formClassSettings.semester = semester.label
  dialogClsSetFormVisible.value = true
}

// 取消新建班级
function cancelHandleNewClass() {
  formClass.name = ''
  // formClass.semester = ''
  dialogFormVisible.value = false
}

const addclassLoading = ref(false)
const handleNewClass = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  console.log(formClass, 'formClassformClass')
  try {
    await formEl.validate()

    // 校验通过，执行后续逻辑
    const params = {
      user: userInfo.id.value,
      course_semester: course.course_semester_id,
      name: formClass.name,
      // semester: formClass.semester
    }
    addclassLoading.value = true
    addCourseClass(params).then((res: any) => {
      if (res.code == 200) {
        message.success('新建班级成功')
        handleFetchClasses()
        selectedClass.value = classes.value[0]
        dialogFormVisible.value = false
        addclassLoading.value = false
      }
    }).catch((err: any) => {
      message.error(err?.message || '新建班级失败')
      console.log('新建班级失败', err)
    })
  } catch (err) {
    // 校验失败，err 为错误信息
    message.error('完善表单内容')
    console.log('验证失败!', err)
  }
}

// 取消班级设置弹框
function cancelSetClass() {
  console.log("取消班级设置", formClassSettings.name, formClassSettings.semester)
  formClassSettings.name = ''
  formClassSettings.semester = ''
  dialogClsSetFormVisible.value = false
}

// 班级设置
const handleSettingsClass = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  try {
    await formEl.validate()
    // 校验通过，调用班级设置接口
    const params = {
      user: userInfo.id.value,
      course_semester: course.course_semester_id,
      name: formClassSettings.name,
      description: formClassSettings.semester,
      id: formClassSettings.id || selectedClass.value?.id
    }
    classSettings(params).then((res: any) => {
      if (res.code == 200) {
        message.success('班级设置成功')

        // selectedClass.value = classes.value[0]
        handleFetchClasses()
        dialogClsSetFormVisible.value = false
      }
    }).catch((err: any) => {
      message.error(err?.message || '更新班级设置失败')
      console.log('更新班级设置失败', err)
    })
  } catch (err) {
    // 校验失败，err 为错误信息
    console.log('验证失败!', err)
  }
}

// 单行删除学生逻辑
const handleDeleteStudent = async (studentId: number) => {
  const confirmed = await showDeleteConfirm('确定删除此条学生记录吗？');
  console.log(confirmed,'---');
  if(!confirmed) return
  // 调用API删除学生
  const params = {
    ids: [studentId]
  }
  classStudentDelete(params).then((res: any) => {
    if (res.code == 200) {
      message.success('删除学生成功')
      handleFetchClassStudents(selectedClass.value)
    }
  }).catch((err: any) => {
    message.error(err?.message || '删除学生失败')
    console.log('删除学生失败', err)
  })
}

// 分页逻辑
function handlePageChange(page: any) {
  console.log('当前页码：', page)
  currentPage.value = page.current
  handleFetchClassStudents(selectedClass.value)
}

// 手动添加学生逻辑
function handleManualImport() {
  dialogImportFormVisible.value = true
}
// 导入学生（批量）
const addStudentLoading = ref(false)
const handleImportStudents = async () => {
  if (!importFormRef.value) return
  try {
    await importFormRef.value.validate()
    // 处理导入逻辑
    const studentIds = formImport.studentId
      .split(/\n|\r|,+/)
      .map(id => id.trim())
      .filter(id => id)
    console.log('导入的学号:', studentIds)

    // 调用导入学生接口
    const params = {
      // course_id: course.id, // 课程ID
      course_semester_id: course.course_semester_id, // 课程学期ID
      class_obj: selectedClass.value?.id, // 班级ID
      students: studentIds // 学生用户ID
    }
    addStudentLoading.value = true
    classStudentAdd(params).then((res: any) => {
      if (res.code == 200) {
        message.success(res.message || '导入学生成功')
        // console.log('导入学生成功', res.message)
        handleFetchClassStudents(selectedClass.value)
        formImport.studentId = ''
        dialogImportFormVisible.value = false
        addStudentLoading.value = false
      }
    }).catch((err: any) => {
      message.error(err?.message || '导入学生失败')
      console.log('导入学生失败', err)
    })
  } catch (err) {
    // 错误处理
    console.log(err)
  }
}

//删除课程班级（单个）
async function handleDeleteClass() {
  const confirmed = await showDeleteConfirm('确定删除该班级记录吗？删除后无法恢复！');
  if (confirmed) {
    const params = {
      ids: [selectedClass.value?.id] // 接口用的数组，但是班级列表删除按钮只能单个删除
    }
    // 调用删除班级接口
    classDelete(params).then((res: any) => {
      if (res.code == 200) {
        message.success('删除班级成功')
        handleFetchClasses()
        // selectedClass.value = classes.value.length > 0 ? classes.value[0] : null
        // console.log(selectedClass.value,'--------------')
        selectedClass.value = null
      }
    }).catch((err: any) => {
      console.log('删除班级失败', err)
      message.error(err?.message || '删除班级失败')
      console.log('删除班级失败', err)
    })
  }
}

// 获取班级列表
async function handleFetchClasses() {
  const params = {
    course_semester: course.course_semester_id,
    page: '',
    page_size: '99999',
  }
  courseClassList(params).then((res: any) => {
    if (res.code == 200) {
      classes.value = res.data.results
      if (selectedClass.value == null) {
        selectedClass.value = classes.value.length > 0 ? classes.value[0] : null
      }
      console.log('获取班级列表成功', classes.value)
      if (classes.value.length > 0) {
        handleFetchClassStudents(selectedClass.value)
      }
      // handleFetchClassStudents(selectedClass.value)
    }
  }).catch((err: any) => {
    console.log('获取班级列表失败', err)
  })
}

// 获取班级学生列表部分
const spinning = ref(false)
function handleFetchClassStudents(cls: ClassType | null) {
  // 调用接口获取班级学生列表。
  students.value = []
  const params = {
    class_obj_id: cls?.id,
    student_name: searchValue.value,
    page: searchValue.value != ''? 1: (currentPage.value || 1),
    page_size: 10,
  }
  spinning.value = true
  classStudentList(params).then((res: any) => {
    if (res.code == 200) {
      students.value = res.data.results
      total.value = res.data.count
      // console.log('获取班级学生列表', students.value)
      spinning.value = false
    }
  }).catch((err: any) => {
    console.log('获取班级学生列表失败', err)
  })
}

// watch(selectedClass, (newValue, _oldValue) => {
//     handleFetchClassStudents(newValue)
// });

// 生命周期钩子
onMounted(() => {
  handleFetchClasses()
})

function handleSelectClass(cls: ClassType | null) {
  console.log("选择班级", cls)
  currentPage.value = 1
  selectedClass.value = cls
  handleFetchClassStudents(cls)
}

</script>

<style scoped>
/* 班级管理 */
.class-manager {
  display: flex;
  /* max-height: 90%; */
  position: relative;
  background-color: #ffffff;
  border-radius: 5px;
  overflow: auto;
}

/* 班级学生列表 */
.main-content {
  flex: 1;
  padding: 0 5px 0 5px;
  margin: 20px 20px 0;
  max-width: 100%;
  width: 75%;
  overflow: auto;
  position: relative;
  z-index: 1;
}

/* 班级名称 */
.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px 0 8px;
  font-weight: bold;
  min-height: 24px;
}

/* 班级操作 */
.header {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  margin: 17px 0;
}

/* 分割线 */
:deep(.ant-divider-vertical) {
  position: absolute !important;
  left: 340px !important;
  top: 0 !important;
  height: 100% !important;
  width: 2px !important;
  margin: 0px !important;
}

/* 班级列表删除弹框，提示内容 */
.deleteDialog-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.button-row {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}

/* 弹框样式 */
.class-manager :deep(.ant-modal .ant-modal-header) {
  margin-bottom: 22px !important;
}
</style>