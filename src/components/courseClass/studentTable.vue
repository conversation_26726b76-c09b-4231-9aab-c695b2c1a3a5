<template>
  <div class="table-container">
    <ATable 
      :class="twMerge(
        'reset-ant-table-pagination',
        '[&_.ant-table-pagination-right]:items-center ',
        'rounded-[5px] bg-white  [&_.ant-pagination]:px-5! [&_.ant-table-content]:px-5!',
        '[&_.ant-table-thead_.ant-table-cell]:bg-white! [&_.ant-table-thead_.ant-table-cell]:before:hidden',
        '[&_.ant-table-row]:h-[58px]! ',
        '[&_.ant-table-row]:border-[10px]! [&_.ant-table-row]:border-black! ',
      )" 
      :data-source="studentsChiled" 
      :columns="columns" 
      :row-selection="{ onChange: handleSelectionChange }"
      :pagination="{ size: 'small', showSizeChanger: false, total: total, pageSize: 10}" 
      :row-key="(record) => record.id" 
      :loading="loading"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <div v-if="column.key === 'action'" style="display: flex;justify-content: center;">
          <!-- <APopconfirm title="确定删除此条学生记录吗？" ok-text="确认" cancel-text="否" @confirm="() => confirmDelete(record.id)"> -->
            <ATooltip title="删除">
              <img src="@/assets/icon/delete-box.svg" alt="自定义图标" @click="confirmDelete(record.id)"
                style="width: 1em; height: 1em; fill: currentColor;" />
            </ATooltip>
          <!-- </APopconfirm> -->
        </div>
      </template>
    </ATable>
  </div>
</template>

<script lang="ts" setup>
import { twMerge } from 'tailwind-merge'
import { defineProps, defineEmits, h } from 'vue'
import type { ColumnType } from 'ant-design-vue/es/table'

export interface StudentType {
  id: number
  first_name: string
  username: string
  major: string
  college: string
}
const columns: ColumnType<any>[] = [
  {
    title: '姓名',
    dataIndex: 'first_name',
    key: 'first_name',
    width: '15%',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '学号',
    dataIndex: 'username',
    key: 'username',
    ellipsis: true,
  },
  {
    title: '院系',
    dataIndex: 'college',
    key: 'college',
    ellipsis: true,
  },
  {
    title: '专业',
    dataIndex: 'major',
    key: 'major',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'action',
    width: '15%',
    align: 'center',
  }
]

const props = defineProps<{ students: StudentType[], currentPage?: number, total: number, loading?: boolean }>()
const emits = defineEmits(['delete-student', 'page-change', 'selection-change','pagination-change'])

const studentsChiled = ref<any[]>([])
watch(() => props.students , (newVal) => {
  studentsChiled.value = newVal.map((item: any) => item.student)
})

const handleSelectionChange = (_selectedRowKeys: (string | number)[], selectedRows: StudentType[]) => {
  emits('selection-change', selectedRows)
}

const confirmDelete = (studentId: number) => {
  const id = props.students.map((item: any) => {
    if (item.student.id === studentId) {
      return item.id
    }
  }).filter((item) => item !== undefined)[0]
  emits('delete-student', id)
}

const renderEmpty = () =>
  h('div', { style: 'display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 32px 0;' }, [
    h('img', { src: require('@/assets/icon/noData.png'), alt: '暂无数据', style: 'width: 120px; object-fit: contain;' }),
    h('div', { style: 'color: #909399; margin-top: 12px;' }, '暂无学生数据')
  ])


const handleTableChange = (pagination: any) => {
    emits('pagination-change', pagination);
}
</script>

<style scoped>
.table-container {
  overflow: auto;
}

.custom-pagination {
  justify-content: flex-end;
  display: flex;
  background: #fff;
  padding: 8px 0;
}

.custom-pagination :deep(.el-pager li) {
  border-radius: 6px;
  min-width: 32px;
  height: 32px;
  line-height: 32px;
  margin: 0 2px;
  color: #606266;
  font-weight: 400;
  border: none;
  background: transparent;
  transition: all 0.2s;
}

.custom-pagination :deep(.el-pager li.is-active) {
  background: #409EFF;
  color: #fff;
  font-weight: 500;
  border: none;
}

.custom-pagination :deep(.el-pager li:hover) {
  background: #f4f8ff;
  color: #409EFF;
}

.custom-pagination :deep(button) {
  border-radius: 6px;
  min-width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: #606266;
  transition: all 0.2s;
}

.custom-pagination :deep(button:hover) {
  background: #f4f8ff;
  color: #409EFF;
}

/* antd 表格选中行 */
.custom-pagination :deep(.ant-pagination-item-active) {
  background: #409EFF;
  color: #fff;
  font-weight: 500;
  border: none;
}

:deep(.ant-table-thead > tr > th) {
  background: #fff !important;
  font-weight: bold !important;
  color: #222 !important;
  /* border-bottom: 1px solid #f0f0f0 !important; */
  border-right: none !important;
}

:deep(.ant-table-thead > tr > th:not(:last-child))::before {
  display: none !important;
}

:deep(.ant-table-wrapper .ant-table-cell-scrollbar:not([rowspan])) {
  box-shadow: 0 0 0 0 !important;
}



:deep(.ant-btn) {
  border: none !important;
  background: none !important;
  box-shadow: none !important;
}

:deep(.ant-btn[aria-label="删除"]) {
  color: #3f8cff !important;
}

:deep(.ant-pagination-item) {
  border-radius: 8px !important;
  border: none !important;
}

:deep(.ant-pagination-item-active) {
  background: #3f8cff !important;
  color: #fff !important;
  border: none !important;
}

:deep(.ant-pagination-item-active a) {
  color: #fff !important;
}
</style>