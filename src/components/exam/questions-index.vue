<template>
    <div class="question hide-scrollbar pt-[22px] px-[20px]">
        <div class="top" ref="topButton">
            <div class="flex justify-between" :class="type == 'stuView' ? 'mt-[12px]' : ''">
                <div class="font-bold text-[20px] ">{{ examInfo.exam_name }}</div>
                <a-button type="primary" class="gradient-a-button w-[82px]" @click="submitExamClick"
                    :loading="submitLoading" v-if="!isGrading">
                    提交
                </a-button>
                <div v-if="isGrading && type != 'stuView'">
                    <a-button type="primary" class="gradient-a-button w-[82px]" @click="submitExamClick"
                        :loading="submitLoading">发布</a-button>
                    <a-button type="primary" class="gradient-a-button w-[105px] ml-[15px]" @click="submitExamClickNext"
                        :loading="submitLoadingNext">发布+下一个</a-button>
                </div>
                <div v-if="type == 'stuView'" class="flex text-[#333333]">
                    <div class="">得分：</div>
                    <div class="text-[36px] mt-[-6px]">{{ gettatolScore }}</div>
                </div>
            </div>
        </div>
        <div
            class="flex justify-between items-center mt-[12px] text-[14px] pb-[23px] border-b-[1px] border-solid border-[#E8E8E8]">
            <div class="flex">
                <div class="text-[#666666]">学生:<span class="text-[#333333]">{{ userInfo.student_name }}</span></div>
                <div class="text-[#666666] ml-[34px]">学号:<span class="text-[#333333]"> {{ userInfo.student_number }}
                    </span></div>
                <div class="text-[#666666] ml-[34px]" v-if="isGrading">提交时间:<span class="text-[#333333]">
                        {{ formatDate(end_time) }} </span></div>
            </div>
            <div class="font-bold">
                题量{{ examList.length }}道，总分{{ tatolScore }}分
            </div>
        </div>
        <div class="overflow-y-auto h-[100%] hide-scrollbar pb-[90px]">
            <div v-for="(item, index) in examList" :key="index"
                :class="!isGrading ? 'border-b-[1px] border-solid border-[#E8E8E8]' : ''" class="py-[20px]">
                <div class="flex  justify-between">
                    <div class="flex mb-[10px] ">
                        <div class="flex shrink-0">
                            {{ index + 1 }}、
                            <div class="text-[#3F8CFF] font-medium mr-[10px]">
                                【{{ item.content.type }}】
                            </div>
                        </div>
                        <div v-if="!isGrading">
                            {{ item.content.stem }} （{{ item.score }}分）
                        </div>
                    </div>
                    <div class="text-[#FF0000] font-blod text-[14px] leading-[14px] shrink-0 flex" v-if="isGrading">
                        <div v-if="item.get_score < item.score">【错误】</div>
                        <div v-else class="text-[#33C4A5] font-[700]">【正确】</div>
                        <span class="text-[#333333]">分值：{{ item.score }}</span>
                    </div>
                </div>
                <Questions :item="item"></Questions>
                <div class="rounded-[5px] bg-[#FAFAFA] border border-solid border-[#E8E8E8] my-[20px] p-[20px]"
                    v-if="isGrading">
                    <div class="text-[#33C4A5] text-[14px] flex">
                        <div class="shrink-0 w-[70px] ">正确答案：</div>
                        <div v-if="item.content.type == '填空题'" class="flex">
                            <div v-for="(ans, indexans) in item.content.answer">
                                {{ ans.blank }}、
                            </div>
                        </div>
                        <div v-else-if="item.content.type == '多选题'">
                            {{ item.content.answer.join(',') }}
                        </div>

                        <div v-else>{{ item.content.answer }}</div>
                    </div>
                    <div class="flex mt-[10px]">
                        <div class="shrink-0 text-[#666666] text-[14px] w-[70px]">解析说明：</div>
                        <div class="text-[#999999] leading-[26px]">{{ item.content.explanation }}</div>
                    </div>
                    <div class="flex items-center mt-[20px] justify-between">
                        <div class="shrink-0 text-[#333333] text-right flex items-center flex-1">
                            <div class="shrink-0 ">评语：</div>
                            <a-input v-model:value="item.justification" placeholder="请输入评语..."
                                v-if="isGrading && type != 'stuView'" />
                            <div v-else class="shrink-0">{{ item.justification }}</div>
                        </div>

                        <div class="flex items-center  ml-[48px]">
                            <div class="shrink-0 text-[#333333] text-right items-center flex">
                                <div>得分：</div>
                                <a-input-number v-model:value="item.get_score" :min="0" :max="item.score"
                                    v-if="isGrading && type != 'stuView'" />
                                <div v-else class="text-[#999]">{{ item.get_score }}</div>
                            </div>

                            <div class="shrink-0 ml-[20px] mr-[-14px]" @click="item.get_score = item.score"
                                v-if="isGrading && type != 'stuView'">
                                <a-radio :checked="item.get_score == item.score ? true : false">
                                    直接满分
                                </a-radio>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="fixed bottom-[55px] z-10 botSut" v-show="showFloatButton" v-if="!isGrading">
            <a-button type="primary" class="gradient-a-button w-[82px]" @click="submitExamClick"
                :loading="submitLoading">提交</a-button>
        </div>
        <div v-else class="fixed bottom-[23px] botSutf z-10" v-show="showFloatButton && type != 'stuView'">
            <a-button type="primary" class="gradient-a-button w-[82px]" @click="submitExamClick"
                :loading="submitLoading">发布</a-button>
            <a-button type="primary" class="gradient-a-button w-[105px] ml-[15px]" @click="submitExamClickNext"
                :loading="submitLoadingNext">发布+下一个</a-button>
        </div>
    </div>


</template>

<script lang="ts" setup>
import { ref, onMounted, computed, onUnmounted } from 'vue';
import Questions from './questions.vue'
import { useQuestionStore } from '@/stores/question'
const questionStore = useQuestionStore() // 获取当前题号
import { formatDate } from '@/utils/util'
import { message } from 'ant-design-vue';

//题目列表
const props = defineProps(['examList', 'examInfo', 'userInfo', 'total_score', 'isGrading', 'end_time', 'submitStatus', 'type'])

const emit = defineEmits(['submitExam']);

const tatolScore = computed(() => {
    let score = 0
    props.examList.forEach((item: any) => {
        score += item.score
    })
    return score
})
//得分
const gettatolScore = computed(() => {
    let score = 0
    props.examList.forEach((item: any) => {
        score += item.get_score
    })
    return score
})
const submitLoading = ref(false)
const submitLoadingNext = ref(false)
function submitExamClick() {
    submitLoading.value = true
    emit('submitExam')
}
function submitExamClickNext() {
    submitLoadingNext.value = true
    // emit('submitExam')
    message.info('正在开发中')
    setTimeout(() => {
        submitLoadingNext.value = false
    }, 1000)
}


//判断提交按钮是否显示
const topButton = ref(null);
const showFloatButton = ref(false);
const initObserver = () => {
    const observer = new IntersectionObserver(
        (entries) => {
            entries.forEach(entry => {
                showFloatButton.value = !entry.isIntersecting;
            });
        },
        {
            root: null, // 视口为根
            threshold: 0.1, // 10% 可见时触发
        }
    );

    if (topButton.value) {
        observer.observe(topButton.value);
    }

    // 组件卸载时停止监听
    onUnmounted(() => {
        observer.disconnect();
    });
};

onMounted(() => {
    initObserver();
});
</script>
<style lang="scss">
.question {
    width: 1030px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
    // height: calc(100vh - 100px);
    overflow: hidden;
}

:deep(.ant-input-number .ant-input-number-handler-wrap) {
    opacity: 1;
}

.botSut {
    right: 10px !important;
}

@media (min-width: 1920px) {
    .botSut {
        right: 149px !important;
    }
}

.botSutf {
    right: 10px !important;
}

@media (min-width: 1920px) {
    .botSutf {
        right: 42px !important;
    }
}
</style>
