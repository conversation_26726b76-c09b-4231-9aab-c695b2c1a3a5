<template>
    <div class="">
        <!-- 单选题 -->
        <div v-if="item.content.type == '单选题'">
            <a-radio-group v-model:value="item.feedback">
                <a-radio :style="radioStyle" :value="it.key" v-for="(it, index2) in item.content.options" :key='index2' @change="answerhandle(item)">
                    <div class="flex items-center font-medium text-[#3E454E] text-[14px] leading-[20px]">
                        <div>{{ getEn(index2) }}:</div>
                        <div class="">{{ it.value }}</div>
                    </div>
                </a-radio>
            </a-radio-group>
        </div>
        <!-- 多选题 -->
		<div  v-else-if="item.content.type == '多选题'">
            <a-checkbox-group v-model:value="item.feedback"  class="flex flex-col gap-[14px]">
                <a-checkbox :value="it.key" v-for="(it,index2) in item.content.options" @change="answerhandle(item)">
                    <div class="flex items-center text-[#3E454E] text-[14px] leading-[20px] font-medium">
                        <div>{{ getEn(index2) }}:</div>
                        <div>{{ it.value }}</div>
                    </div>
                </a-checkbox>
            </a-checkbox-group>
        </div>

        <!-- 判断题 -->
        <div  v-else-if="item.content.type == '判断题'">
            <a-radio-group v-model:value="item.feedback">
                <a-radio :style="radioStyle" :value="it.key" v-for="(it, index2) in item.content.options" :key='index2' @change="answerhandle(item)">
                    <div class="flex text-[#3E454E] text-[14px] font-medium items-center">
                        <div>{{ it.value }}</div>
                    </div>
                </a-radio>
            </a-radio-group>
		</div>
        <!-- 问答题 -->
		<div v-if="item.content.type == '问答题'">
			<a-textarea
                v-model:value="item.feedback"
                style=""
                :rows="4"
                placeholder="请输入答案..."  @input="answerhandle(item)"
            />
		</div>

        <!-- 填空题 -->
		<div  v-else-if="item.content.type == '填空题'" v-for="(it, index) in item.content.answer" class="mb-[10px]">
            <a-input v-model:value="it.value" placeholder="请输入答案..."  @input="answerhandle(item)" />
		</div>
    </div>
</template>

<script lang="ts" setup>
import { emitter } from '@/utils/mitter';
import { ref } from "vue";

const Ens = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"];
// 根据索引获取字母
const getEn = (index: number) => {
    return Ens[index] || ""; // 如果索引超出范围，返回空字符串
};
const props = defineProps({
    item: {
        type: Object,
        default: () => ({})
    }
})
const emit = defineEmits(['answerChange']);

const radioStyle = reactive({
  display: 'flex',
//   height: '30px',
  lineHeight: '30px',
})

const answerhandle = (e: any) => {
    emitter.emit('answerhandleChange', e);
}


// // 判断选项是否被选中
// const changeAnswer = (userAnswer: string | null, optionId: string) => {
//     if (userAnswer && userAnswer.length > 0 && userAnswer.indexOf(optionId) != -1) {
//         return 'answer_options_active'
//     } else {
//         return ''
//     }
// };




const checkboxAnswerChange = (e: any) => {
    console.log(e,'多选')
    // emit('answerChange', props.item)
}

// 处理单选答案的变化
const radioAnswerChange = (e: any) => {
    console.log(e)
    // if (props.item.userAnswer.length > 0) {
    //     props.item.userAnswer = new Array()
    // }
    // props.item.userAnswer.push(optionId)
    // emit('answerChange', props.item)
};
// 多选题 or 不定项
function checkAnswerChange(optionId: string) {
	let index = props.item.userAnswer.findIndex((it: string) => it == optionId)
	if (index != -1) {
		props.item.userAnswer.splice(index, 1)
	} else {
		props.item.userAnswer.push(optionId)
	}
	emit('answerChange', props.item);
}
// 问答题 
function shortAnswer(e: any) {
	emit('answerChange', e);
}

// 填空题
function handleInput(event : any, keywords : any, i: number) {
	let length = toArray(keywords).length
	for (var k = 0; k < length; k++) {
		if (i == k) {
            props.item.userAnswer[k] = event;
		} else if (i != k && props.item.userAnswer[k] == undefined) {
            props.item.userAnswer[k] = '';
		}
	}
	emit('answerChange', props.item);
}

// 字符串转数组
function toArray(array: string | null) {
	if (array != null && array != '[]') {
		let temp = array.split(',')
		temp = temp.map(item => item.replace(/\[|]/g, '').replace(/\"|"/g, '').replace(/\s/g, ""))
		return temp
	} else {
		return new Array(0)
	}
}

</script>
<style lang="scss" scoped>
v-deep.el-input__wrapper{
    height: 40px;
}
</style>