<template>
    <div class="time">
        <!-- 总剩余时间
        <br> -->
        <span>{{ time }}</span>

        <a-modal v-model:open="open" title="考试结束" ok-text="确认" cancel-text="" @ok="hideModal" :maskClosable="false">
            <div>
                考试结束，已为你提交试卷！
            </div>
        </a-modal>
    </div>
</template>

<script lang="ts" setup>
import { Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
const [modal, contextHolder] = Modal.useModal();
import { ref, watch, onMounted, onUnmounted, h  } from "vue";

const props =defineProps(['totalMinutes'])
const emit = defineEmits(['timeOutExam'])
const open = ref(false);

// const time = ref('01:01:00')
const time = ref(formatMinutes(props.totalMinutes));
let timer: any = null; // 用于存储定时器的引用

// 将总分钟数转为 "HH:MM:SS" 格式
function formatMinutes(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${String(hours).padStart(2, '0')}:${String(remainingMinutes).padStart(2, '0')}:00`;
}



// 格式化时间函数
function formatTime(timeString: any) {
    const [hours, minutes, seconds] = timeString.split(':').map(Number);
    return { hours, minutes, seconds };
}
// 更新倒计时函数
// 修改 updateCountdown 函数中的解析方式
function updateCountdown() {
    const [hoursStr, minutesStr, secondsStr] = time.value.split(':');
    let hours = parseInt(hoursStr);
    let minutes = parseInt(minutesStr);
    let seconds = parseInt(secondsStr);

    if (seconds > 0) {
        seconds--;
    } else if (minutes > 0) {
        minutes--;
        seconds = 59;
    } else if (hours > 0) {
        hours--;
        minutes = 59;
        seconds = 59;
    } else {
        clearInterval(timer);
        // alert('倒计时结束');
        console.log('倒计时结束');
        open.value = true
        emit('timeOutExam')
        return;
    }

    time.value = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
}

async function hideModal() { 
    
    open.value = false
}

// 开始倒计时函数
function startCountdown() {
    timer = setInterval(updateCountdown, 1000); // 每秒更新一次时间
}

// function submitAnswer() {
//     emit('submitExam')
// }

// 组件挂载时自动开始倒计时（可选）
onMounted(() => {
    startCountdown(); // 或者根据需要注释掉这行，改为手动开始倒计时按钮触发
});

// 组件卸载时清除定时器以避免内存泄漏（可选）
onUnmounted(() => {
    clearInterval(timer); // 确保在组件销毁前清除定时器
});


</script>
<style lang="scss" scoped>
// .time {
//     width: 100%;
//     padding: 30px 0 0 0;
//     text-align: center;
//     background-color: #fff;
//     border-radius: 10px;
//     color: #2424248b;
//     font-size: 14px;
//     height: 36vh;
// }

// span {
//     font-size: 30px;
//     font-weight: bold;
//     color: rgb(54, 170, 253);
// }

.submit {
    margin-top: 16vh;
    line-height: 40px;
    border-radius: 30px;
    text-align: center;
    margin-left: 50%;
    transform: translateX(-50%);
}

v-deep.el-button,
.el-button.is-round {
    padding: 16px 33px;
}
</style>