<script setup lang="ts">
  import { message, type FormProps } from 'ant-design-vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  const props = defineProps<FormProps>()

  export interface FormValues {
    name: string
  }

  const formValues = ref<Partial<FormValues>>({})

  const invalidate = useKbInvalidation()
  const { mutate, status } = useMutation({
    mutationFn() {
      return kbFetcher('/datasets/', {
        method: 'post',
        body: formValues.value,
      })
    },
    onSuccess: invalidate.allKbs,
    onError(err) {
      message.error(err.message)
    },
  })

  const emit = defineEmits<{ 'update:status': [MutationStatus] }>()
  watchEffect(() => emit('update:status', status.value))
</script>

<template>
  <AForm
    v-bind="props"
    :model="formValues"
    @finish="mutate()"
  >
    <AFormItem
      label="名称"
      name="name"
      required
    >
      <AInput
        v-model:value="formValues.name"
        placeholder="请输入知识库名称"
        :maxlength="64"
        show-count
      />
    </AFormItem>
  </AForm>
</template>
