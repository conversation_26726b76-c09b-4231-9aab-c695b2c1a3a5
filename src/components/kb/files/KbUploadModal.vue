<script setup lang="ts">
  import Uppy from '@uppy/core'
  import { DashboardModal } from '@uppy/vue'
  import XHRUpload from '@uppy/xhr-upload'
  import zhCN from '@uppy/locales/lib/zh_CN'

  import { env } from '@/../env'
  import { injectKbFilesContext } from './KbFilesPage.vue'

  import '@uppy/core/dist/style.css'
  import '@uppy/dashboard/dist/style.css'

  const { kbId } = injectKbFilesContext()

  const uppy = new Uppy({
    id: 'kb-uppy',
    locale: zhCN,
    restrictions: { allowedFileTypes: ['text/plain', '.pdf', '.docx', '.doc', '.xls', '.xlsx'] },
  }).use(XHRUpload, {
    endpoint: `${env.VITE_API_BASE_URL}/v1/datasets/${kbId.value}/documents/`,
  })

  const isOpen = defineModel<boolean>('open', { default: false })

  watchEffect(() => {
    uppy.getPlugin('XHRUpload')?.setOptions({
      endpoint: `${env.VITE_API_BASE_URL}/v1/datasets/${kbId.value}/documents/`,
    })
  })

  const csrftoken = useCSRFToken()
  watchEffect(() => {
    uppy.getPlugin('XHRUpload')?.setOptions({
      headers: {
        'X-CSRFToken': csrftoken.value,
      },
    })
  })

  const invalidate = useKbInvalidation()
  uppy.on('dashboard:modal-closed', () => {
    uppy.clear()
    isOpen.value = false
  })
  uppy.on('upload-success', () => {
    invalidate.kbFiles(kbId.value)
  })
</script>

<template>
  <DashboardModal
    :uppy="uppy"
    :open="isOpen"
    class="reset-uppy-dashboard"
    :props="{
      proudlyDisplayPoweredByUppy: false,
      fileManagerSelectionType: 'both',
      closeAfterFinish: true,
      closeModalOnClickOutside: true,
    }"
  />
</template>
