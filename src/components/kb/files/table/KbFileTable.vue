<script lang="ts">
  import type { ColumnType } from 'ant-design-vue/es/table'

  export type TableItem = {
    id: string
    chunkNum: number
    createTime: Date
    chunkMethod: API.Kb.File['chunk_method']
    rawIndex: number
  }

  export const columns = [
    {
      title: '名称',
      key: 'name',
      width: 320,
      ellipsis: true,
    },
    {
      title: '分块数',
      dataIndex: 'chunkNum',
      key: 'chunkNum',
      width: 120,
    },
    {
      title: '创建时间',
      key: 'createTime',
      customRender: ({ record }) => record.createTime.toLocaleString('zh-CN'),
      width: 200,
    },
    {
      title: '分块方法',
      dataIndex: 'chunkMethod',
      key: 'chunkMethod',
      width: 120,
    },
    {
      title: '解析状态',
      key: 'chunkStatus',
      width: 200,
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
    },
  ] as const satisfies ColumnType<TableItem>[]
</script>

<script setup lang="ts">
  import { injectKbFilesContext } from '../KbFilesPage.vue'
  import KbFileName from './KbFileName.vue'

  const props = defineProps<{
    files: TableItem[]
  }>()

  const { selectedFiles, setSelectedFiles, rawFiles } = injectKbFilesContext()
</script>

<template>
  <ATable
    :data-source="props.files"
    :columns="columns"
    :class="[
      'reset-ant-table-pagination',
      'rounded-[5px] bg-white pb-2.5 [&_.ant-pagination]:px-5! [&_.ant-table-content]:px-5!',
      '[&_.ant-table-thead_.ant-table-cell]:bg-white! [&_.ant-table-thead_.ant-table-cell]:before:hidden',
      '[&_.ant-table-expanded-row-fixed]:max-w-full!',
    ]"
    :row-selection="{
      selectedRowKeys: selectedFiles,
      onChange(selectedRowKeys) {
        setSelectedFiles(selectedRowKeys as string[])
      },
      getCheckboxProps(record: TableItem) {
        return { disabled: rawFiles[record.rawIndex].run === 'RUNNING' }
      },
    }"
    :pagination="{ size: 'small', showSizeChanger: false }"
    :row-key="(record) => record.id"
    :scroll="{ x: 1200 }"
  >
    <template
      #bodyCell="//@ts-expect-error antdv poor typing
      { column, record }: { record: TableItem; column: (typeof columns)[number] }"
    >
      <KbFileName
        v-if="column.key === 'name'"
        :raw-index="record.rawIndex"
      />

      <KbFileStatus
        v-else-if="column.key === 'chunkStatus'"
        :raw-index="record.rawIndex"
      />

      <div
        v-else-if="column.key === 'action'"
        class="flex items-center space-x-4"
      >
        <KbFileActionDelete
          :raw-index="record.rawIndex"
          class="-ml-1!"
        />

        <KbFileActionEdit :raw-index="record.rawIndex" />

        <KbFileActionDownload :raw-index="record.rawIndex" />
      </div>
    </template>
  </ATable>
</template>
