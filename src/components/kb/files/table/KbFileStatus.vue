<script lang="ts">
  export type KbFileStatus = 'complete' | 'running' | 'failed' | 'not-started'

  const statusMap: Record<KbFileStatus, { text: string; color: string }> = {
    complete: { text: '已完成', color: 'success' },
    running: { text: '解析中', color: 'processing' },
    failed: { text: '解析失败', color: 'error' },
    'not-started': { text: '未开始', color: 'default' },
  }

  export function transformRawStatus(rawStatus: API.Kb.File['run']): KbFileStatus {
    switch (rawStatus) {
      case 'UNSTART':
        return 'not-started'
      case 'RUNNING':
        return 'running'
      case 'CANCEL':
        return 'not-started'
      case 'DONE':
        return 'complete'
      case 'FAIL':
        return 'failed'
      default:
        return 'not-started'
    }
  }
</script>

<script setup lang="ts">
  import { injectKbFilesContext } from '../KbFilesPage.vue'

  type Props = {
    rawIndex: number
  }

  const props = defineProps<Props>()

  const { rawFiles } = injectKbFilesContext()

  const status = computed(() => transformRawStatus(rawFiles.value[props.rawIndex].run))
</script>

<template>
  <div class="flex items-center space-x-5!">
    <ATag
      :color="statusMap[status].color"
      class="flex! w-16 items-center justify-center rounded-xs!"
    >
      {{ statusMap[status].text }}
    </ATag>

    <KbFileActionStartParse :raw-index="props.rawIndex" />
  </div>
</template>
