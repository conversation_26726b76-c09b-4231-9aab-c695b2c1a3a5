<script setup lang="ts">
  import { message } from 'ant-design-vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

  import { injectKbFilesContext } from '../../KbFilesPage.vue'

  import IconTrash from '~icons/lucide/trash-2'
  import IconLoading from '~icons/lucide/loader-circle'

  const props = defineProps<{ rawIndex: number }>()

  const { rawFiles, kbId } = injectKbFilesContext()

  const rawFile = computed(() => rawFiles.value[props.rawIndex])

  const invalidate = useKbInvalidation()
  const { mutate: deleteFile, status } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${kbId.value}/documents/`, {
        method: 'delete',
        body: {
          ids: [rawFile.value.id],
        },
      })
    },
    onSuccess() {
      return invalidate.kbFiles(kbId.value)
    },
    onError(error) {
      message.error(error.message)
    },
  })
</script>

<template>
  <APopconfirm
    title="确定删除该文件吗？"
    @confirm="deleteFile()"
    :ok-button-props="{ danger: true }"
  >
    <template #icon>
      <ExclamationCircleOutlined />
    </template>

    <AButton
      type="text"
      size="small"
      class="icon-a-button"
      title="删除"
      :disabled="rawFile.run === 'RUNNING'"
    >
      <IconLoading
        v-if="status === 'pending'"
        class="animate-spin"
      />
      <IconTrash v-else />
    </AButton>
  </APopconfirm>
</template>
