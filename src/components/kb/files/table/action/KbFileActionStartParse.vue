<script setup lang="ts">
  import { message } from 'ant-design-vue'

  import { injectKbFilesContext } from '../../KbFilesPage.vue'
  import { transformRawStatus } from '../KbFileStatus.vue'

  import IconPlay from '~icons/lucide/circle-play'
  import IconRedo from '~icons/ant-design/redo-outlined'
  import IconLoading from '~icons/lucide/loader-circle'

  const props = defineProps<{ rawIndex: number }>()

  const { rawFiles, kbId } = injectKbFilesContext()

  const rawFile = computed(() => rawFiles.value[props.rawIndex])
  const parseStatus = computed(() => transformRawStatus(rawFile.value.run))

  const invalidate = useKbInvalidation()
  const { mutate: startParse, status: mutationStatus } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${kbId.value}/parse-tasks/`, {
        method: 'post',
        body: {
          document_ids: [rawFile.value.id],
        },
      })
    },
    onSuccess() {
      return invalidate.kbFiles(kbId.value)
    },
    onError(error) {
      message.error(error.message)
    },
  })
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="icon-a-button"
    title="开始解析"
    @click="startParse()"
    v-if="parseStatus !== 'running'"
  >
    <IconLoading
      v-if="mutationStatus === 'pending'"
      class="animate-spin"
    />
    <IconPlay v-else-if="parseStatus === 'not-started'" />
    <IconRedo v-else />
  </AButton>
</template>
