<script lang="ts">
  import type { ShallowRef } from 'vue'

  type FileId = string

  const kbFilesContextInjectionKey = Symbol('kb-files') as InjectionKey<{
    selectedFiles: Readonly<ShallowRef<FileId[]>>
    setSelectedFiles(value: FileId[]): void

    rawFiles: Readonly<ShallowRef<API.Kb.File[]>>

    kbId: Readonly<ShallowRef<string>>
  }>

  export function injectKbFilesContext() {
    const context = inject(kbFilesContextInjectionKey)
    if (!context) {
      throw new Error('kbContext is not provided')
    }
    return context
  }
</script>

<script setup lang="ts">
  import { Modal, message } from 'ant-design-vue'
  import { useFuse } from '@vueuse/integrations/useFuse'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { h } from 'vue'

  import type { TableItem } from './table/KbFileTable.vue'

  import IconSearch from '~icons/ant-design/search-outlined'
  import IconPlay from '~icons/lucide/circle-play'
  import IconLoading from '~icons/lucide/loader-circle'
  import IconTrash from '~icons/lucide/trash-2'

  const props = defineProps<{ kbId: string }>()

  const { queryOptions } = useKbFiles({ kbId: props.kbId })
  const { data: rawFiles, suspense } = useQuery({
    ...queryOptions,
    refetchInterval: 15_000,
  })

  await useNavigationSuspense({ delay: 500, suspense })

  const filesTransformed = computed<TableItem[]>(
    () =>
      rawFiles.value?.map<TableItem>((file, index) => ({
        id: file.id,
        chunkMethod: file.chunk_method,
        chunkNum: file.chunk_count,
        createTime: new Date(file.create_time),
        rawIndex: index,
      })) ?? [],
  )

  const selectedFiles = ref<string[]>([])
  provide(kbFilesContextInjectionKey, {
    selectedFiles: shallowReadonly(selectedFiles),
    setSelectedFiles(value) {
      selectedFiles.value = value
    },

    rawFiles: computed(() => rawFiles.value ?? []),

    kbId: computed(() => props.kbId),
  })

  const searchString = ref('')
  const searchStringDebounced = useDebounce(searchString, 200)

  const { results } = useFuse(searchStringDebounced, filesTransformed, {
    fuseOptions: { keys: ['name'], threshold: 0.3 },
  })

  const files = computed(() => {
    if (!searchStringDebounced.value) {
      return filesTransformed.value
    }
    return results.value.map((result) => result.item)
  })

  const isUploadModalOpen = ref(false)

  const invalidate = useKbInvalidation()
  const { mutate: parseAll, status: parseAllStatus } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${props.kbId}/parse-tasks/`, {
        method: 'post',
        body: {
          document_ids: selectedFiles.value,
        },
      })
    },
    onSuccess() {
      selectedFiles.value = []
      return invalidate.kbFiles(props.kbId)
    },
    onError(err) {
      message.error(err.message)
    },
  })

  const { mutate: deleteAll, status: deleteAllStatus } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${props.kbId}/documents/`, {
        method: 'delete',
        body: {
          ids: selectedFiles.value,
        },
      })
    },
    onSuccess() {
      selectedFiles.value = []
      return invalidate.kbFiles(props.kbId)
    },
    onError(err) {
      message.error(err.message)
    },
  })

  function onDeleteAllClicked() {
    Modal.confirm({
      title: '提示',
      content: `确定删除 ${selectedFiles.value.length} 个文件吗？`,
      onOk() {
        deleteAll()
      },
      okButtonProps: {
        danger: true,
      },
      icon: h(ExclamationCircleOutlined),
      centered: true,
      wrapClassName: 'reset-ant-modal',
    })
  }
</script>

<template>
  <div>
    <div class="flex flex-wrap items-center gap-y-4 px-(--page-px) pt-6">
      <AInput
        class="max-w-[300px] rounded-full!"
        placeholder="搜索文件名称..."
        v-model:value="searchString"
      >
        <template #suffix>
          <IconSearch class="text-foreground-4" />
        </template>
      </AInput>

      <div class="text-foreground-3 ml-4">
        共有
        <span class="text-primary">{{ files.length }}</span>
        个筛选结果
      </div>

      <div class="ml-auto flex min-w-max items-center space-x-5">
        <AButton
          type="primary"
          class="gradient-a-button w-20!"
          @click="isUploadModalOpen = true"
        >
          新建
        </AButton>

        <AButton
          type="primary"
          ghost
          class="outline-a-button flex! items-center gap-2"
          :disabled="selectedFiles.length === 0"
          @click="parseAll()"
        >
          <IconLoading
            v-if="parseAllStatus === 'pending'"
            class="animate-spin"
          />
          <IconPlay v-else />
          解析
        </AButton>

        <AButton
          type="primary"
          ghost
          class="outline-a-button flex! items-center gap-2"
          :disabled="selectedFiles.length === 0"
          @click="onDeleteAllClicked"
        >
          <IconLoading
            v-if="deleteAllStatus === 'pending'"
            class="animate-spin"
          />
          <IconTrash v-else />
          删除
        </AButton>
      </div>
    </div>

    <div class="px-(--page-px) py-4">
      <KbFileTable :files="files" />
    </div>

    <KbUploadModal v-model:open="isUploadModalOpen" />
  </div>
</template>
