<script setup lang="ts">
  interface Props {
    content: string
    filename: string
  }

  const props = defineProps<Props>()

  // 解析 content 中的 <em> 标签，将其转换为带有特定样式的 span
  const parsedContent = computed(() => {
    return props.content.replace(
      /<em>(.*?)<\/em>/g,
      '<span class="text-[rgba(255,77,79,1)]">$1</span>',
    )
  })

  const ellipsisContainer = useTemplateRef('ellipsisContainer')
  const { style, isOverflowing } = useTextEllipsis(ellipsisContainer, { lines: 2 })

  const [DefineContent, ReuseContent] = createReusableTemplate()
</script>

<template>
  <DefineContent>
    <div
      v-html="parsedContent"
      ref="ellipsisContainer"
      :style="style"
    />
  </DefineContent>

  <div class="rounded-[5px] border-white bg-[rgba(255,255,255,0.5)] p-5">
    <!-- 内容展示区域 -->
    <APopover
      overlay-class-name="[&_.ant-popover-inner]:max-w-[1200px]"
      v-if="isOverflowing"
    >
      <ReuseContent />

      <template #content>
        <span v-html="parsedContent" />
      </template>
    </APopover>

    <ReuseContent v-else />

    <!-- 文件信息区域 -->
    <div class="mt-3 flex items-center space-x-2">
      <FileTypeImg :file-name="filename" />
      <span class="text-foreground-3">{{ filename }}</span>
    </div>
  </div>
</template>
