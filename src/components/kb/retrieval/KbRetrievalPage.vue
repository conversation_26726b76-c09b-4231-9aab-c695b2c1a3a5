<script setup lang="ts">
  import IconSearch from '~icons/ion/search'
  import IconLoading from '~icons/lucide/loader-circle'
  import ImgWelcome from '@/assets/img/kb-retrieval-welcome.webp'
  import ImgStars from '@/assets/img/stars.webp'

  const props = defineProps<{ kbId: string }>()

  const { query, page, queryOptions } = useKbChunks({ kbId: props.kbId })
  const { data, fetchStatus, status } = useQuery({
    ...queryOptions,
    enabled: () => query.value.length > 0,
    placeholderData(previousData) {
      return previousData
    },
  })

  const queryInput = ref('')
  debouncedWatch(
    queryInput,
    (value) => {
      query.value = value.trim()
      page.value = 1
    },
    { debounce: 200 },
  )

  const rawChunks = computed(() => data.value?.chunks ?? [])
  const totalNum = computed(() => data.value?.total ?? 0)

  const shouldShowWelcome = computed(() => status.value !== 'success')
</script>

<template>
  <KbRetrievalContainer class="px-(--page-px)">
    <template v-if="shouldShowWelcome">
      <!-- 标题 -->
      <div class="mt-[20svh] -mb-3 flex items-center justify-center">
        <img
          :src="ImgWelcome"
          class="pointer-events-none size-[154px]"
        />

        <div class="space-y-2">
          <div
            class="relative w-max bg-linear-135 from-[rgba(33,159,255,1)] to-[rgba(0,102,255,1)] bg-clip-text text-3xl font-bold text-transparent select-none"
          >
            智能检索

            <img
              :src="ImgStars"
              class="pointer-events-none absolute -right-[30px] bottom-1.5 w-[42px]"
            />
          </div>

          <div class="text-foreground-2 text-xl">让信息触手可及的智慧引擎</div>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="mb-8 flex justify-center">
        <AInput
          v-model:value="queryInput"
          placeholder="请输入检索的关键字..."
          class="[&_input]:placeholder:text-foreground-3! h-[50px] max-w-[600px] rounded-full! bg-[rgba(255,255,255,0.5)] not-[:is(*:hover,.ant-input-affix-wrapper-focused)]:border-white!"
          style="padding: 12px 14px 12px 32px; box-shadow: 0px 6px 58px rgba(21, 72, 153, 0.05)"
        >
          <template #suffix>
            <div>
              <IconLoading
                v-if="fetchStatus === 'fetching'"
                class="size-5 animate-spin"
                style="color: rgba(192, 196, 212, 1)"
              />
              <IconSearch
                v-else
                class="size-5"
                style="color: rgba(192, 196, 212, 1)"
              />
            </div>
          </template>
        </AInput>
      </div>
    </template>

    <template v-if="shouldShowWelcome === false">
      <!-- 搜索结果标题 -->
      <div class="flex items-center space-x-4 pt-5 pb-2">
        <div class="text-foreground-2 text-base font-bold">搜索结果</div>
        <div class="text-foreground-3">
          共有
          <span class="text-primary">{{ totalNum }} </span>
          个筛选结果
        </div>

        <AInput
          class="ml-auto! max-w-[300px] rounded-full!"
          placeholder="请输入检索的关键字..."
          v-model:value="queryInput"
        >
          <template #suffix>
            <IconSearch class="text-foreground-4" />
          </template>
        </AInput>
      </div>

      <!-- 检索结果展示 -->
      <Empty
        v-if="rawChunks.length === 0"
        class="mx-auto mt-20 w-[335px]"
      >
        <template #message> 暂无相关结果 </template>
      </Empty>

      <template v-else>
        <div class="space-y-2.5">
          <KbRetrievalCard
            v-for="chunk in rawChunks"
            :key="chunk.id"
            :content="chunk.highlight || chunk.content"
            :filename="chunk.document_keyword"
          />
        </div>

        <!-- 分页 -->
        <div
          v-if="rawChunks.length > 0"
          class="flex justify-end pt-3 pb-8"
        >
          <APagination
            class="reset-ant-pagination"
            v-model:current="page"
            :total="totalNum"
            :page-size="7"
            :show-size-changer="false"
            size="small"
          />
        </div>
      </template>
    </template>
  </KbRetrievalContainer>
</template>
