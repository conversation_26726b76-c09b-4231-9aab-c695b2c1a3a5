<script setup lang="ts">
  import { injectKbSettingsPageContext } from './KbSettingsPage.vue'

  const { formState } = injectKbSettingsPageContext()
</script>

<template>
  <div class="space-y-5!">
    <div class="text-foreground-2 text-lg font-bold">分块参数设置</div>

    <AFormItem
      required
      label="分块方法"
      name="chunkMethod"
    >
      <ASelect v-model:value="formState.chunkMethod">
        <ASelectOption value="naive">naive</ASelectOption>
        <ASelectOption value="book">book</ASelectOption>
        <ASelectOption value="laws">laws</ASelectOption>
        <ASelectOption value="one">one</ASelectOption>
      </ASelect>
    </AFormItem>

    <AFormItem
      label="分隔符"
      required
      name="delimiter"
    >
      <AInput v-model:value="formState.delimiter" />
    </AFormItem>

    <AFormItem
      label="关键词数量"
      required
      name="keywordNum"
    >
      <div class="flex gap-4">
        <ASlider
          class="reset-ant-slider grow"
          v-model:value="formState.keywordNum"
          :tooltip-open="false"
        />
        <AFormItemRest>
          <AInputNumber
            class="max-w-15"
            v-model:value="formState.keywordNum"
          />
        </AFormItemRest>
      </div>
    </AFormItem>

    <AFormItem
      label="问题数量"
      required
      name="questionNum"
    >
      <div class="flex gap-4">
        <ASlider
          class="reset-ant-slider grow"
          v-model:value="formState.questionNum"
          :tooltip-open="false"
        />
        <AFormItemRest>
          <AInputNumber
            class="max-w-15"
            v-model:value="formState.questionNum"
          />
        </AFormItemRest>
      </div>
    </AFormItem>
  </div>
</template>
