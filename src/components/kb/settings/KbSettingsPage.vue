<script lang="ts">
  export interface FormState {
    name: string
    description: string
    embeddingModel: string
    chunkMethod: string
    delimiter: string
    keywordNum: number
    questionNum: number
    courses: (string | number)[][]
  }

  const kbSettingsPageInjectionKey = Symbol('kb-settings-page') as InjectionKey<{
    formState: Ref<FormState>
  }>

  export function injectKbSettingsPageContext() {
    const context = inject(kbSettingsPageInjectionKey)
    if (!context) {
      throw new Error('kbSettingsPageContext is not provided')
    }
    return context
  }
</script>

<script setup lang="ts">
  import { message } from 'ant-design-vue'

  import { injectKbDetailContext } from '@/pages/(app)/aiSpace/kb/[id].vue'

  const { rawKb } = injectKbDetailContext()

  const formState = ref<FormState>({
    name: rawKb.value?.name ?? '',
    description: rawKb.value?.description ?? '',
    embeddingModel: rawKb.value?.embedding_model ?? '',
    chunkMethod: rawKb.value?.chunk_method ?? '',
    delimiter: rawKb.value?.parser_config?.delimiter ?? '',
    keywordNum: rawKb.value?.parser_config?.auto_keywords ?? 0,
    questionNum: rawKb.value?.parser_config?.auto_questions ?? 0,
    courses: [],
  })
  watchEffect(() => {
    formState.value = {
      name: rawKb.value?.name ?? '',
      description: rawKb.value?.description ?? '',
      embeddingModel: rawKb.value?.embedding_model ?? '',
      chunkMethod: rawKb.value?.chunk_method ?? '',
      delimiter: rawKb.value?.parser_config?.delimiter ?? '',
      keywordNum: rawKb.value?.parser_config?.auto_keywords ?? 0,
      questionNum: rawKb.value?.parser_config?.auto_questions ?? 0,
      courses: rawKb.value?.course_semesters.map((cs) => [cs.semester.id, cs.course_id]) ?? [],
    }
  })

  provide(kbSettingsPageInjectionKey, {
    formState,
  })

  const userStore = useUserStore()
  const queryClient = useQueryClient()
  const { queryOptions } = useKbDetail({ kbId: rawKb.value!.id })
  const { mutate: updateKb, status: updateKbStatus } = useMutation({
    mutationFn() {
      const updatePromise = kbFetcher(`/datasets/${rawKb.value!.id}/`, {
        method: 'put',
        body: {
          name: formState.value.name,
          description: formState.value.description,
          embedding_model:
            Number(rawKb.value?.chunk_count) === 0 ? formState.value.embeddingModel : undefined,
          chunk_method: formState.value.chunkMethod,
          parser_config: {
            delimiter: formState.value.delimiter,
            auto_keywords: formState.value.keywordNum,
            auto_questions: formState.value.questionNum,
          },
        } as Partial<API.Kb.Kb>,
      })

      const bindPromise = (function () {
        if (userStore.role !== 'teacher' && userStore.role !== 'admin') {
          return Promise.resolve()
        }
        return kbFetcher('/course-kb-link/', {
          method: 'post',
          body: {
            kb_id: rawKb.value!.id,
            course_ids: formState.value.courses.flatMap((arr) => arr.at(-1)),
          },
        })
      })()

      return Promise.all([updatePromise, bindPromise])
    },
    onError(err) {
      message.error(err.message)
    },
    onSuccess() {
      message.success('保存成功')
      return queryClient.invalidateQueries({ queryKey: queryOptions.queryKey })
    },
  })
</script>

<template>
  <div class="px-(--page-px) py-5">
    <AForm
      id="kb-settings-form"
      class="rounded-md bg-white"
      layout="vertical"
      :model="formState"
      @finish="updateKb()"
    >
      <div class="border-separator border-b border-dashed shadow-[0_2px_19px_#1D4F99C] lg:flex">
        <div class="border-separator flex-3 border-b border-dashed px-10 py-7.5 lg:border-b-0">
          <slot name="basic-settings" />
        </div>
        <!-- <div class="flex-2 px-10 py-7.5">
          <slot name="chunk-settings" />
        </div> -->
      </div>

      <div class="flex px-10 py-6">
        <AButton
          class="gradient-a-button ml-auto"
          type="primary"
          html-type="submit"
          form="kb-settings-form"
          :loading="updateKbStatus === 'pending'"
        >
          保存设置
        </AButton>
      </div>
    </AForm>
  </div>
</template>
