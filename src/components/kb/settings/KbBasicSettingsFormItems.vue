<script setup lang="ts">
  import { message, Cascader as ACascader, type CascaderProps } from 'ant-design-vue'

  import { injectKbSettingsPageContext } from './KbSettingsPage.vue'
  import { injectKbDetailContext } from '@/pages/(app)/aiSpace/kb/[id].vue'

  const { formState } = injectKbSettingsPageContext()
  const { rawKb } = injectKbDetailContext()

  const { queryOptions: modelsQueryOptions } = useOpenaiModelList({ categories: ['embedding'] })
  const {
    data: embeddingModels,
    status: embeddingModelsStatus,
    error,
  } = useQuery(modelsQueryOptions)

  watch(error, (err) => {
    if (err) {
      message.error(err.message)
    }
  })

  const { queryOptions: semestersCoursesQueryOptions } = useSemestersCourses()
  const { data: semestersCourses, status: semestersCoursesStatus } = useQuery(
    semestersCoursesQueryOptions,
  )

  const courseOptions = computed(() => {
    return semestersCourses.value?.map((sc) => ({
      label: sc.semesterName,
      value: sc.semesterId,
      children: sc.courses.map((course) => ({
        label: course.courseName,
        value: course.courseId,
      })),
    }))
  })

  const cascaderDisplayRender: CascaderProps['displayRender'] = ({ labels, selectedOptions }) => {
    if (selectedOptions?.length === 1) {
      return `${labels[0]} - 全部课程`
    }
    return labels.join(' - ')
  }
</script>

<template>
  <div class="space-y-5!">
    <div class="text-foreground-2 text-lg font-bold">基本设置</div>

    <AFormItem
      required
      label="知识库名称"
    >
      <AInput
        v-model:value="formState.name"
        :maxlength="64"
        show-count
      />
    </AFormItem>

    <AFormItem
      label="知识库描述"
      name="description"
    >
      <ATextarea
        v-model:value="formState.description"
        :rows="4"
        :maxlength="8192"
        show-count
      />
    </AFormItem>

    <AFormItem
      label="嵌入模型"
      required
      name="embeddingModel"
    >
      <ASelect
        v-model:value="formState.embeddingModel"
        :loading="embeddingModelsStatus === 'pending'"
        :disabled="rawKb && rawKb.chunk_count > 0"
      >
        <ASelectOption
          v-for="model in embeddingModels"
          :key="model.id"
          :value="model.id"
        >
          {{ model.id }}
        </ASelectOption>
      </ASelect>
    </AFormItem>

    <AppPermissionGuard :roles="['teacher', 'admin']">
      <AFormItem
        label="关联课程"
        name="courses"
      >
        <ACascader
          v-model:value="formState.courses"
          @update:value="console.log"
          multiple
          :options="courseOptions"
          :loading="semestersCoursesStatus === 'pending'"
          :display-render="cascaderDisplayRender"
          :show-checked-strategy="ACascader.SHOW_CHILD"
        />
      </AFormItem>
    </AppPermissionGuard>
  </div>
</template>
