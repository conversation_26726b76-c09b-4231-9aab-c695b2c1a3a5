<script setup lang="ts">
  import { message, type FormProps } from 'ant-design-vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  export type FormValues = {
    question: string
    answer: string
    fileId: string
  }

  const props = withDefaults(defineProps<FormProps & { kbId: string }>(), {
    layout: 'vertical',
  })
  const emit = defineEmits<{ 'update:status': [MutationStatus] }>()

  const formState = ref<Partial<FormValues>>({})

  const { queryOptions } = useKbFiles({ kbId: props.kbId })
  const { data: rawFiles, status } = useQuery(queryOptions)

  const invalidate = useKbInvalidation()
  const { mutate: createQa, status: createQaStatus } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${props.kbId}/documents/${formState.value.fileId}/chunks/`, {
        method: 'post',
        body: {
          content: formState.value.answer,
          questions: [formState.value.question],
        },
      })
    },
    onSuccess: () => invalidate.kbQas(props.kbId),
    onError(err) {
      message.error(err.message || '添加问答对失败')
    },
  })

  watchEffect(() => emit('update:status', createQaStatus.value))
</script>

<template>
  <AForm
    v-bind="props"
    :model="formState"
    @finish="createQa()"
  >
    <AFormItem
      label="问题"
      required
    >
      <ATextarea
        v-model:value="formState.question"
        placeholder="问题"
        :rows="4"
      />
    </AFormItem>

    <AFormItem
      label="回答"
      required
    >
      <ATextarea
        v-model:value="formState.answer"
        placeholder="回答"
        :rows="4"
      />
    </AFormItem>

    <AFormItem
      label="所属文档"
      required
    >
      <ASelect
        v-model:value="formState.fileId"
        :filter-option="
          (input, option) => option?.label.toLowerCase().includes(input.toLowerCase())
        "
        show-search
        :loading="status === 'pending'"
        :options="
          rawFiles?.map((file) => ({
            value: file.id,
            label: file.name,
          }))
        "
      />
    </AFormItem>
  </AForm>
</template>
