<script setup lang="ts">
  import { message } from 'ant-design-vue'
  import { twMerge } from 'tailwind-merge'

  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import IconLoading from '~icons/lucide/loader-circle'
  import IconTrash from '~icons/lucide/trash-2'

  const props = defineProps<{
    id: string
    question: string
    answer: string
    fileId: string
    filename: string
    kbId: string
  }>()

  const checked = defineModel<boolean>('checked', { default: false })

  const { mutateAsync: deleteQa, status } = useMutation({
    async mutationFn() {
      return kbFetcher(`/datasets/${props.kbId}/documents/${props.fileId}/chunks/`, {
        method: 'delete',
        body: {
          chunk_ids: [props.id],
        },
      })
    },
  })

  const invalidate = useKbInvalidation()
  function onDeleteConfirm() {
    deleteQa(undefined, {
      onSuccess: () => {
        return invalidate.kbQas(props.kbId)
      },
      onError(err) {
        message.error(err.message || '删除问答对失败')
      },
    })
  }

  defineExpose({
    deleteQa,
  })
</script>

<template>
  <div
    :class="
      twMerge(
        'flex cursor-pointer space-x-2.5 rounded-[5px] border-white bg-[rgba(255,255,255,0.5)] p-5 pt-4.5 shadow-[0px_2px_19px_rgba(29,79,153,0.05)] transition-all hover:-translate-y-0.5',
        checked && 'bg-primary/6',
      )
    "
    @click="checked = !checked"
  >
    <div>
      <ACheckbox
        v-model:checked="checked"
        class="mt-0.5!"
      />
    </div>

    <div class="grow">
      <TextEllipsis
        :lines="1"
        :text="props.question"
        class="text-foreground-2 mb-2.5 text-base font-bold"
        :tooltip="{
          title: props.question,
          placement: 'top',
          overlayInnerStyle: { maxHeight: '300px', overflowY: 'auto' },
          overlayStyle: { maxWidth: '400px' },
        }"
      />

      <TextEllipsis
        :lines="1"
        class="text-foreground-2 text-sm"
        :text="props.answer"
        :tooltip="{
          title: props.answer,
          placement: 'bottom',
          overlayInnerStyle: { maxHeight: '300px', overflowY: 'auto' },
          overlayStyle: { maxWidth: '400px' },
        }"
      />

      <div class="mt-6 flex">
        <div class="flex items-center space-x-2">
          <FileTypeImg :file-name="props.filename" />
          <div class="text-foreground-3">{{ props.filename }}</div>
        </div>

        <APopconfirm
          title="确定删除该问答对吗？"
          @confirm="onDeleteConfirm()"
          :ok-button-props="{ danger: true }"
        >
          <template #icon>
            <ExclamationCircleOutlined />
          </template>

          <AButton
            type="text"
            size="small"
            class="icon-a-button ml-auto!"
            @click.stop
          >
            <IconLoading
              v-if="status === 'pending'"
              class="animate-spin"
            />
            <IconTrash v-else />
          </AButton>
        </APopconfirm>
      </div>
    </div>
  </div>
</template>
