<script setup lang="ts">
  import { h } from 'vue'
  import type { MutationStatus } from '@tanstack/vue-query'
  import { message, Modal } from 'ant-design-vue'

  import IconSearch from '~icons/ant-design/search-outlined'
  import IconTrash from '~icons/lucide/trash-2'
  import IconLoading from '~icons/lucide/loader-circle'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

  import KbQaCard from './KbQaCard.vue'

  const props = defineProps<{
    kbId: string
  }>()

  const { search, queryOptions } = useKbQas({ kbId: props.kbId })
  const { data: rawQas, error } = useQuery({
    ...queryOptions,
    placeholderData: (oldData) => oldData,
  })

  watch(error, (err) => {
    if (err) {
      message.error(err.message)
    }
  })

  const searchInput = ref('')
  debouncedWatch(
    searchInput,
    (value) => {
      search.value = value.trim()
    },
    { debounce: 200 },
  )

  const isCreateModalOpen = ref(false)
  const createQaStatus = ref<MutationStatus>('idle')
  watchEffect(() => {
    if (createQaStatus.value === 'success') {
      isCreateModalOpen.value = false
    }
  })

  const selectedQaIds = reactive(new Set<string>())
  const QaRefs = new Map<string, InstanceType<typeof KbQaCard>>()

  const invalidate = useKbInvalidation()
  const batchDeleting = ref(false)
  function batchDeleteQas() {
    const promises = Array.from(selectedQaIds).map((id) => {
      const qaRef = QaRefs.get(id)
      if (qaRef) {
        return qaRef.deleteQa()
      }
      return Promise.resolve()
    })

    batchDeleting.value = true
    Promise.all(promises)
      .then(() => {
        selectedQaIds.clear()
        invalidate.kbQas(props.kbId)
      })
      .catch((error: Error) => {
        message.error(error.message)
      })
      .finally(() => {
        batchDeleting.value = false
      })
  }

  function onBatchDeleteClick() {
    Modal.confirm({
      title: '提示',
      content: `确定删除 ${selectedQaIds.size} 个文件吗？`,
      onOk: batchDeleteQas,
      okButtonProps: {
        danger: true,
      },
      icon: h(ExclamationCircleOutlined),
      centered: true,
      wrapClassName: 'reset-ant-modal',
    })
  }
</script>

<template>
  <div>
    <div class="flex flex-wrap items-center gap-y-4 px-(--page-px) pt-6 pb-3">
      <AInput
        class="max-w-[300px] rounded-full!"
        placeholder="搜索问题..."
        v-model:value="searchInput"
      >
        <template #suffix>
          <IconSearch class="text-foreground-4" />
        </template>
      </AInput>

      <div class="text-foreground-3 ml-4">
        共有
        <span class="text-primary">{{ rawQas?.length ?? 0 }}</span>
        个筛选结果
      </div>

      <div class="ml-auto flex min-w-max items-center space-x-5">
        <AButton
          class="outline-a-button flex! items-center space-x-2"
          type="primary"
          ghost
          :disabled="batchDeleting || selectedQaIds.size === 0"
          @click="onBatchDeleteClick"
        >
          <IconLoading
            v-if="batchDeleting"
            class="animate-spin"
          />
          <IconTrash v-else />
          删除
        </AButton>

        <AButton
          class="gradient-a-button"
          type="primary"
          @click="isCreateModalOpen = true"
        >
          添加问答对
        </AButton>
      </div>
    </div>

    <Empty
      v-if="Boolean(rawQas?.length) === false"
      class="mx-auto mt-20 w-[335px]"
    >
      <template #message> 暂无数据 </template>
    </Empty>

    <div
      v-else
      class="grid grid-cols-1 gap-2.5 px-(--page-px) pt-2 pb-5"
    >
      <KbQaCard
        v-for="qa in rawQas"
        :key="qa.id"
        :id="qa.id"
        :question="qa.questions[0]"
        :answer="qa.content"
        :filename="qa.document_name"
        :file-id="qa.document_id"
        :kb-id="props.kbId"
        :ref="
          // @ts-ignore
          (r) => QaRefs.set(qa.id, r)
        "
        @update:checked="
          (selected) => {
            if (selected) {
              selectedQaIds.add(qa.id)
            } else {
              selectedQaIds.delete(qa.id)
            }
          }
        "
      />
    </div>

    <AModal
      v-model:open="isCreateModalOpen"
      title="添加问答对"
      wrap-class-name="reset-ant-modal [&_.ant-modal-body]:max-h-[640px] [&_.ant-modal-body]:overflow-y-auto"
      :width="700"
      destroy-on-close
      centered
      :ok-button-props="{
        // @ts-expect-error antdv poor typing
        form: 'create-qa-form',
        htmlType: 'submit',
        loading: createQaStatus === 'pending',
      }"
    >
      <KbQaCreateForm
        id="create-qa-form"
        :kb-id="props.kbId"
        @update:status="createQaStatus = $event"
      />
    </AModal>
  </div>
</template>
