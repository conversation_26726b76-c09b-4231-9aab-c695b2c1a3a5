<template>
    <div>
        <a-table :class="twMerge(
            'reset-ant-table-pagination',
            '[&_.ant-table-pagination-right]:items-center ',
            'rounded-[5px] bg-white [&_.ant-pagination]:px-5! [&_.ant-table-content]:px-5!',
            '[&_.ant-table-thead_.ant-table-cell]:bg-white! [&_.ant-table-thead_.ant-table-cell]:before:hidden',
            '[&_.ant-table-row]:h-[58px]',
            
        )" :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }" :columns="columns"
            :data-source="data" :pagination="{ size: 'small', showSizeChanger: false, total: total, pageSize: 10 }"
            @change="handleTableChange">
            <template #bodyCell="{ column, record }">
                <div @mouseenter="onRowHover(record.id)" @mouseleave="onRowLeave">
                    <div v-if="column.key === 'title'" class="flex items-center">
                        <div v-if="editingIndex !== record.id" class="cursor-pointer" @click="changeTitle(record)">
                            {{ record.title }}
                        </div>
                        <a-input v-else v-model:value="record.title" @blur="[editingIndex = null,editBlur(record)]" ref="inputRef" class="topTitle-input">
                            <!-- <template #suffix>
                                <CheckCircleOutlined @mousedown.prevent style="color:#C4C4C4;margin-left:10px;" />
                                <CloseCircleOutlined @mousedown.prevent style="color:#C4C4C4;margin-left:5px;" />
                            </template> -->
                        </a-input>
                        <a-tooltip placement="bottom">
                            <template #title>
                                <span>编辑</span>
                            </template>
                            <img class="cursor-pointer w-[14px] h-[14px] ml-[15px]" v-if="hoveredRowId == record.id && editingIndex != record.id"
                                src="@/assets/image/img/edittable.png" @click="onEdit(record)" />

                        </a-tooltip>
                        
                    </div>
                    <div v-if="column.key === 'author'">
                         {{ record.author }}
                    </div>
                    <div v-if="column.key === 'updated_at'" class="flex items-center justify-center">
                        {{ formatDate(record.updated_at) }}
                    </div>
                    <div v-if="column.key === 'action'" class="flex items-center justify-center">
                        <a-tooltip placement="bottom">
                            <template #title>
                                <span>下载</span>
                            </template>
                            <DownloadOutlined style="color: #3F8CFF;font-size: 14px;" @click="downloadTeachPlan(record)" />
                        </a-tooltip>
                        <a-tooltip placement="bottom">
                            <template #title>
                                <span>删除</span>
                            </template>
                            <IconTrash class="text-[#3F8CFF] ml-[20px] " @click="onClickChange(record.key)" />
                        </a-tooltip>
                    </div>
                </div>
            </template>
        </a-table>

    </div>
</template>
<script lang="ts" setup>
import { DownloadOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons-vue';
import { twMerge } from 'tailwind-merge'
import { computed, reactive } from 'vue';
import { formatDate } from '@/utils/util'
import IconTrash from '~icons/lucide/trash-2'
import { env } from '@/../env'

const emit = defineEmits(['selection-change', 'pagination-change', 'selection-check','edit-change','change-title'])
import type { ColumnType } from 'ant-design-vue/es/table'
type Key = string | number;

interface DataType {
    key: Key;
    title: string;
    author: number;
    updated_at: string;
}

const columns: ColumnType<DataType>[] = [
    {
        title: '文件名',
        dataIndex: 'title',
        // align: 'left',
        width: '40%',
        key: 'title',
    },
    {
        title: '创建者',
        dataIndex: 'author',
        align: 'center',
        key: 'author',
    },
    {
        title: '更新时间',
        dataIndex: 'updated_at',
        align: 'center',
        key: 'updated_at',
    },
    {
        title: '操作',
        align: 'center',
        key: 'action',
    },
];

const pptTitle = ref('')
const editingIndex = ref<number | null>(null)
const hoveredRowId = ref<number | null>(null)
function onRowHover(id: number) {
    hoveredRowId.value = id
}
function onRowLeave() {
    hoveredRowId.value = null
}

const props = defineProps<{
    pptTemTableList: any[],
    total?: number
}>()
const data = ref<DataType[]>([]);

watch(() => props.pptTemTableList, (newVal, oldVal) => {
    console.log(newVal, '添加数据')
    data.value = []
    newVal.forEach((item: any) => {
        data.value.push({
            key: item.id,
            ...item
        });
    })
})

const handleTableChange = (pagination: any) => {
    emit('pagination-change', pagination);
}


const state = reactive<{
    selectedRowKeys: Key[];
    loading: boolean;
}>({
    selectedRowKeys: [],
    loading: false,
});

const onSelectChange = (selectedRowKeys: Key[]) => {
    // console.log('selectedRowKeys changed: ', selectedRowKeys);
    state.selectedRowKeys = selectedRowKeys;
    emit('selection-check', selectedRowKeys);
};
const onClickChange = (data: any) => {
    console.log([data], 'selectedRowKeys')
    emit('selection-change', data);
};

const downloadTeachPlan = (item: any) => {
    if (item.file == null) {
        const url = `${env.VITE_PPT_FRONTEND_URL}?type=pptSquare/pptManage&id=${item.id}&courseId=${getCourse().id}`
        window.open(url, '_blank');
    } else {
        window.open(item.file, '_blank');
    }
}

const onEdit = (item: any) => {
    console.log(item, '编辑')
    editingIndex.value = item.key
    pptTitle.value = item.title
}
const editBlur = (item: any) => {
    console.log(item, '编辑完成')
    if(pptTitle.value != item.title){
        emit('edit-change',item)
    }
    // editingIndex.value = null
}


const changeTitle = (e: any) => {
    emit('change-title',e)
}
</script>

<style scoped>
.topTitle-input {
    height: 32px;
    padding-left: 20px;

    color: #333;
    white-space: nowrap;
    overflow-x: auto;

    border-radius: 5px;
    background: rgba(255, 255, 255, 1);

    border: 1px solid rgba(63, 140, 255, 1);

    box-shadow: 0px 1px 4px  rgba(63, 140, 255, 0.5);
}
</style>