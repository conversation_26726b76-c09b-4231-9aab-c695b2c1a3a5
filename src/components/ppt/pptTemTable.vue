<template>
    <div>
        <a-table :class="twMerge(
                'reset-ant-table-pagination',
                '[&_.ant-table-pagination-right]:items-center ',
                 'rounded-[5px] bg-white pb-2.5 [&_.ant-pagination]:px-5! [&_.ant-table-content]:px-5!',
                '[&_.ant-table-thead_.ant-table-cell]:bg-white! [&_.ant-table-thead_.ant-table-cell]:before:hidden',
                '[&_.ant-table-row]:h-[58px]'
            )" 
            :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }" :columns="columns"
            :data-source="data" 
            :pagination="{ size: 'small', showSizeChanger: false, total: total, pageSize: 10 }"
             @change="handleTableChange"
        >
            <template #bodyCell="{ column, record }">
                <div
                    v-if="column.key === 'updated_at'"
                    class="flex items-center justify-center"
                >
                    {{ formatDate(record.updated_at) }}
                </div>
                <div
                    v-if="column.key === 'action'"
                    class="flex items-center justify-center"
                >
                    <a-tooltip placement="bottom">
                        <template #title>
                            <span>下载</span>
                        </template>
                        <DownloadOutlined style="color: #3F8CFF;font-size: 14px;" @click="downloadTeachPlan(record)" />
                    </a-tooltip>
                    <a-tooltip placement="bottom">
                        <template #title>
                            <span>删除</span>
                        </template>
                        <IconTrash class="text-[#3F8CFF] ml-[20px]" @click="onClickChange(record.key)" />
                    </a-tooltip>
                </div>
            </template>
        </a-table>

    </div>
</template>
<script lang="ts" setup>
import { twMerge } from 'tailwind-merge'
import { computed, reactive } from 'vue';
import { formatDate } from '@/utils/util'
import { DownloadOutlined } from '@ant-design/icons-vue'
import IconTrash from '~icons/lucide/trash-2'
import { env } from '@/../env'
// 在 script setup 中添加以下代码
// const emits = defineEmits<{
//     (e: 'selection-change', keys: Key[]): void;

// }>()
const emit = defineEmits(['selection-change','pagination-change','selection-check'])
import type { ColumnType } from 'ant-design-vue/es/table'
type Key = string | number;

interface DataType {
    key: Key;
    title: string;
    author: number;
    updated_at: string;
}

const columns:ColumnType<DataType>[] = [
    {
        title: '文件名',
        dataIndex: 'title',
        // align: 'left',
        width: '40%',
    },
    {
        title: '创建者',
        dataIndex: 'author',
        align: 'center',
    },
    {
        title: '更新时间',
        dataIndex: 'updated_at',
        align: 'center',
        key:'updated_at',
    },
    {
        title: '操作',
        align: 'center',
        key:'action',
    },
];

const props = defineProps<{
    pptTemTableList: any[],
    total?: number
}>()
const data = ref<DataType[]>([]);

watch(() => props.pptTemTableList, (newVal, oldVal) => { 
    console.log(newVal,'添加数据')
    data.value = []
    newVal.forEach((item: any) => { 
        data.value.push({
            key: item.id,
            ...item
            // title: item.title,
            // author: item.author || '',
            // updated_at: item.updated_at || '',
        });
    })
})

const handleTableChange = (pagination: any) => {
    emit('pagination-change', pagination);
}


const state = reactive<{
    selectedRowKeys: Key[];
    loading: boolean;
}>({
    selectedRowKeys: [],
    loading: false,
});

const onSelectChange = (selectedRowKeys: Key[]) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys);
    state.selectedRowKeys = selectedRowKeys;
    emit('selection-check', selectedRowKeys);
};
const onClickChange = (data:any) => {
    // console.log([data],'selectedRowKeys')
    emit('selection-change', data);
};

const downloadTeachPlan = (item: any) => {
    console.log(item, '下载')
    window.open(item.tmp_json_file, '_blank');
}
</script>

<style scoped></style>