<template>

    <div class="space">
        <div class="space-left" :class="{ 'collapsed': isCollapsed }">
            <div class="left1">
                <img src="@/assets/image/img/aiIconc.png" style="width: 24px;height: 24px;" />
                <img src="@/assets/image/img/AIspaceText.png" style="width: 61px;height: 20px;margin-left: 8px;" />
            </div>

            <div class="left_item" v-for="(item, index) in leftItems" :class="{ active: isActive(item.path) }"
                @click="goTo(item.path)">
                <div class="flex" style="font-size: 12px;">
                    <img :src="isActive(item.path) ? item.iconActive : item.icon " class="menu-icon" />
                    <div style="margin-left: 8px;width: 48px;">{{ item.label }}</div>
                </div>
                <img :src="rightIcon"  v-if="isActive(item.path)">
            </div>
        </div>
        <!-- 悬浮按钮 -->
        <div class="toggle-btn z-index-999" @click="toggleCollapse">
            <RightOutlined v-if="isCollapsed" style="font-size:8px"/>
            <LeftOutlined v-else style="font-size:8px"/>
        </div>
    </div>

</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router'
import { RightOutlined,LeftOutlined } from '@ant-design/icons-vue';

import materialIcon from '@/assets/icon/aiSpace/material.svg';
import kbIcon from '@/assets/icon/aiSpace/kb.svg';
import graphIcon from '@/assets/icon/aiSpace/graph.svg';
import abstractIcon from '@/assets/icon/aiSpace/abstract.svg';
import materialActiveIcon from '@/assets/icon/aiSpace/materialActive.svg';
import kbActiveIcon from '@/assets/icon/aiSpace/kbActive.svg';
import graphActiveIcon from '@/assets/icon/aiSpace/graphActive.svg';
import abstractActiveIcon from '@/assets/icon/aiSpace/abstractActive.svg';
import rightIcon from '@/assets/icon/aiSpace/right.svg';
import toolboxIcon from '@/assets/icon/aiSpace/toolbox.svg';
import toolboxActiveIcon from '@/assets/icon/aiSpace/toolboxActive.svg';

const isCollapsed = ref(false);
const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value;
};

const router = useRouter();
const route = useRoute();

const leftItems = reactive([
    { icon: materialIcon,  iconActive:materialActiveIcon, label: '教学材料', path: '/aiSpace' },
    { icon: kbIcon, iconActive:kbActiveIcon, label: '知识库', path: '/aiSpace/kb'},
    { icon: toolboxIcon, iconActive:toolboxActiveIcon, label: '工具箱', path: '/aiSpace/toolbox'},
    { icon: graphIcon, iconActive:graphActiveIcon, label: '知识图谱', path: '/aiSpace/graph'},
    { icon: abstractIcon, iconActive:abstractActiveIcon, label: '摘要提取', path: '/aiSpace/abstract'}
]);

const isActive = (path:any) => {
    if( path === '/aiSpace' ){
        return route.path === path;
    }else{
        return route.path.startsWith(path)
    }
};

const goTo = (path:any) => {
  router.push(path);
};

</script>
<style scoped lang="less">
.space {

    min-height: 100vh;
    display: flex;
    overflow-x: auto;

    .space-left {
        position: relative;
        transition: width 0.3s ease; // 宽度变化动画
        overflow: hidden;
        width: 169px;
        // min-width: 169px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
        border-left: 1px solid rgba(229, 229, 229, 1);
        flex-shrink: 0;

        &.collapsed {
            width: 0px; // 收起后的宽度
        }

        .left1 {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 28px 0 35px 0;
        }


        .left_item {
            width: 129px;
            height: 30px;
            opacity: 1;
            border-radius: 71px;
            margin: 0 auto 9px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 13px;
            cursor: pointer;

            &.active {
                background: rgba(63, 140, 255, 0.1);
                color: rgba(63, 140, 255, 1);
            }
        }

        .flex {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .toggle-btn {
        margin: auto;
        cursor: pointer;
        background-image: url('@/assets/image/img/ce.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        min-width: 24px;
        height: 67px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: black;
        font-weight: bold;
    }

    .space-right {
        display: flex;
        align-items: center;
        flex-direction: column;
        flex: 1;

        .title {
            font-weight: 600;
            margin-top: 270px;
            font-size: 30px;
            letter-spacing: 0px;
            line-height: 20px;
        }

        .ftitle {
            font-size: 16px;
            font-weight: 400;
            letter-spacing: 0px;
            line-height: 20px;
            margin-top: 19px
        }

        .item-container {
            width: 100%;
            display: flex;
            justify-content: center;
        }
    }

}
</style>
