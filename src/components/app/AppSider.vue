<script lang="ts">
  export const appSiderInjectionKey = Symbol('app-sider') as InjectionKey<{
    isSiderCollapsed: Readonly<Ref<boolean>>
    setSiderCollapsed(value: boolean): void
  }>
  export function injectAppSiderContext() {
    const context = inject(appSiderInjectionKey)
    if (!context) {
      throw new Error('appSiderContext is not provided')
    }
    return context
  }
</script>

<script setup lang="ts">
  import IconMenuFold from '~icons/ant-design/menu-fold-outlined'
  import IconMenuUnfold from '~icons/ant-design/menu-unfold-outlined'
  import IconModels from '~icons/local/models'
  import IconAi from '~icons/local/ai'
  import IconCourses from '~icons/local/courses'
  import IconResources from '~icons/local/resources'
  import IconSystemConfig from '~icons/local/system-config'

  const isCollapsed = defineModel<boolean>('collapsed', { required: true })
</script>

<template>
  <ALayoutSider
    :width="218"
    :collapsed-width="80"
    collapsible
    class="group bg-white! shadow-[0_2px_10px_#438ffe19]! duration-350! ease-in-out! **:duration-300! [&_.ant-layout-sider-trigger]:static! [&_.ant-layout-sider-trigger]:bg-white! [&_.ant-layout-sider-trigger]:duration-350! [&_.ant-layout-sider-trigger]:ease-in-out!"
    v-model:collapsed="isCollapsed"
  >
    <RouterLink to="/">
      <div class="pt-7 transition-all ease-in-out group-[.ant-layout-sider-collapsed]:pt-5">
        <img
          src="/logo.svg"
          class="m-auto h-[70px] object-contain transition-all ease-in-out group-[.ant-layout-sider-collapsed]:h-9"
        />
        <div
          class="text-foreground-2 max-h-6 overflow-hidden text-center font-[alimamashuheiti] text-nowrap transition-all group-[.ant-layout-sider-collapsed]:max-h-0 group-[.ant-layout-sider-collapsed]:scale-10 group-[.ant-layout-sider-collapsed]:opacity-0"
        >
          数智科教平台
        </div>
      </div>
    </RouterLink>

    <!-- 导航菜单 -->
    <div
      class="space-y-5 px-10 pt-14 transition-all ease-in-out group-[.ant-layout-sider-collapsed]:px-6 group-[.ant-layout-sider-collapsed]:pt-9"
    >
      <!-- 视觉上保持对齐 -->
      <AppSiderNavLink
        class="pl-5.5 group-[.ant-layout-sider-collapsed]:pl-1.5"
        to="/course"
        title="我的课程"
        :icon="IconCourses"
      />
      <AppSiderNavLink
        to="/aiSpace"
        title="AI空间"
        :icon="IconAi"
        @click="isCollapsed = true"
      />
      <AppSiderNavLink
        to="/resources"
        title="资源检索"
        :icon="IconResources"
      />

      <AppPermissionGuard :roles="['admin']">
        <AppSiderNavLink
          to="/models"
          :icon="IconModels"
          title="模型管理"
        />
      </AppPermissionGuard>

      <AppPermissionGuard :roles="['admin']">
        <AppSiderNavLink
          to="/system-config"
          :icon="IconSystemConfig"
          title="系统配置"
        />
      </AppPermissionGuard>
    </div>

    <template #trigger>
      <div class="flex h-full justify-end px-6 transition-all">
        <AButton
          type="text"
          class="ml-auto! p-1.5!"
        >
          <IconMenuUnfold v-if="isCollapsed" />
          <IconMenuFold v-else />
        </AButton>
      </div>
    </template>
  </ALayoutSider>
</template>
