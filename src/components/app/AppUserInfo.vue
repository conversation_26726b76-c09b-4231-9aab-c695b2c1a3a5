<script setup lang="ts">
  import { env } from '@/../env'

  import IconLogout from '~icons/lucide/power'
  import IconUser from '~icons/ant-design/user-outlined'

  defineOptions({ inheritAttrs: false })

  const userStore = useUserStore()
  const route = useRoute()

  function doLogout() {
    location.assign(`${env.VITE_API_BASE_URL}/v1/cas/logout/`)
  }
</script>

<template>
  <ADropdown v-if="Boolean(route.meta.hideUserInfo) === false && userStore.name">
    <AButton
      v-bind="$attrs"
      type="text"
      class="flex! items-center gap-1.5"
    >
      <IconUser />
      {{ userStore.name }}
    </AButton>

    <template #overlay>
      <AMenu>
        <AMenuItem @click="doLogout()">
          <template #icon>
            <IconLogout />
          </template>
          退出登录
        </AMenuItem>
      </AMenu>
    </template>
  </ADropdown>
</template>
