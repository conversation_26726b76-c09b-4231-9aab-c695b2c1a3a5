<script setup lang="ts">
  import type { RouterLinkProps } from 'vue-router'
  import { twMerge } from 'tailwind-merge'
  import type { HTMLAttributes } from 'vue'

  import { injectAppSiderContext } from './AppSider.vue'

  defineOptions({ inheritAttrs: false })

  const { class: className, ...props } = defineProps<
    RouterLinkProps & {
      title: string
      icon: Component
      class?: HTMLAttributes['class']
    }
  >()

  const { isSiderCollapsed: isCollapsed } = injectAppSiderContext()

  const [DefineButton, ReuseButton] = createReusableTemplate()
</script>

<template>
  <RouterLink
    class="block"
    v-bind="props"
    v-slot="{ isActive }"
  >
    <DefineButton>
      <button
        :class="
          twMerge(
            'text-foreground-2 flex max-h-10 w-[138px] cursor-pointer items-center gap-2.5 overflow-hidden rounded-[30px] bg-[rgba(63,140,255,0.1)] py-2 pl-5 font-medium text-nowrap [&_svg]:shrink-0',
            isActive && 'bg-primary font-bold text-white',
            !isActive && 'hover:bg-[rgba(63,140,255,0.3)]',
            isCollapsed && 'size-8 max-h-8 rounded-[5px] pl-1',
            className,
          )
        "
        style="
          transition:
            border-radius 0.3s ease-out,
            width 0.3s ease-in-out,
            padding 0.3s ease-in-out,
            background-color 0.2s ease-out !important;
        "
        v-bind="$attrs"
      >
        <props.icon class="transition-none! **:transition-none!" />
        {{ props.title }}
      </button>
    </DefineButton>

    <ATooltip
      v-if="isCollapsed"
      :title="props.title"
      placement="right"
    >
      <ReuseButton />
    </ATooltip>

    <ReuseButton v-else />
  </RouterLink>
</template>
