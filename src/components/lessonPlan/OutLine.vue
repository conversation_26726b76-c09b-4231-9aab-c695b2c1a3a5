<template>
  <div class="outline-timeline">
    <div 
      v-for="(item, index) in items" 
      :key="index" 
      class="timeline-item"
    >
      <div class="timeline-label">{{ item.label }}</div>
      <div class="timeline-marker">
        <div class="timeline-dot"></div>
        <div class="timeline-horizontal-line"></div>

      </div>
      <div class="timeline-content">
        <div class="content-box">{{ item.content }}</div>
      </div>

      <!-- 垂直连接线只在点与点之间显示 -->
      <div 
        v-if="index < items.length - 1" 
        class="timeline-vertical-line"
      ></div>

    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps,onMounted, } from 'vue';

const props = defineProps({
  content: String,
})
const items =ref<any>([])

type TitleItem = {
  label: string;
  content: string;
};

onMounted (()=>{
  if (props.content){
    items.value = createItems(props.content)
    console.log(items.value);
    
  }
})

function createItems(markdown: string): TitleItem[] {
  const lines = markdown.split('\n');
  const items: TitleItem[] = [];

  for (const line of lines) {
    const trimmed = line.trim();

    // 一级标题（# 主标题）
    if (/^#\s+/.test(trimmed)) {
      items.push({
        label: '主标题',
        content: trimmed.replace(/^#\s+/, '').trim(),
      });
    }

    // 二级标题（## 一、引言）
    else if (/^##\s+/.test(trimmed)) {
      const raw = trimmed.replace(/^##\s+/, '').trim();
      const match = raw.match(/^([一二三四五六七八九十]+)、(.*)$/);
      if (match) {
        items.push({
          label: `第${match[1]}章`,
          content: match[2].trim(),
        });
      } else {
        items.push({
          label: '章节',
          content: raw,
        });
      }
    }

    // 小节标题（- 1.1、导入话题）
    else if (/^-\s*\d+\.\d+、/.test(trimmed)) {
      const raw = trimmed.replace(/^-+\s*/, '').trim();
      const match = raw.match(/^(\d+\.\d+)、(.*)$/);
      if (match) {
        items.push({
          label: match[1],
          content: match[2].trim(),
        });
      }
    }
  }

  return items;
}


</script>

<style scoped>
.outline-timeline {
  position: relative;
  padding: 0.5vw;
}


.timeline-item {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 1vw;
}

.timeline-label {
  width: 5vw;
  text-align: right;
  padding-right:2vw;
  font-size: 14px;
  font-weight: 700;
  color: rgba(51, 51, 51, 1);
}

.timeline-marker {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 2;
}

.timeline-dot {
  width: 0.8vw;
  height: 0.8vw;
  border-radius: 50%;
  background-color: #409eff;
  z-index: 3;
}

.timeline-horizontal-line {
  height: 0.1vh;
  width: 1vw;
  background-color: #e0e0e0;
}

.timeline-content {
  flex: 1;
}

.content-box {
  padding: 10px 15px;
  border-radius: 10px;
  border: 1px solid rgba(229, 229, 229, 1);
  font-size: 14px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}

/* 垂直连接线 - 现在作为每个时间线项目的一部分 */
.timeline-vertical-line {
  position: absolute;
  top: 50%;
  bottom: 0;
  left: 5.4vw; /* 调整左边距以对齐蓝点 */
  width: 0.1vh;
  height: 140%;
  background-color: #e0e0e0;
  z-index: 1;
}

</style>
