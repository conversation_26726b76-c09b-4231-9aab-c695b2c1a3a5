<template>
<div class="content">
    <div id="outline" v-if="isOutlineUpdate" class="left"></div>
    <div v-else class="left">
        <div class="card-list">
            <div
                v-for="(item, index) in leftOutline"
                :key="index"
                class="card"
                @click="jump(item)"
            >
            <div class="card-index">
                {{ (index + 1).toString().padStart(2, '0') }}
            </div>
            <div class="card-title">
                {{ item.text }}
            </div>
            </div>
        </div>
    </div>

    <div id="aiEditor" class="center" style="padding: 0; margin: 0">

            <div class="aie-container" style="background-color: #f3f4f6">
                <div class="aie-header-panel">
                    <div class="aie-container-header" style="background: #fff;"></div>
                </div>
                    <div class="aie-main">
                        <!-- <div class="aie-directory-content">
                            <div class="aie-directory" :style="isDirectory ? 'left:-250px' : ''">
                                <Vue3Lottie :animationData="TestLottieJSONData" :height="50" :width="50" class="arrow"
                                    @click="isDirectory = !isDirectory"
                                    :style="isDirectory ? 'transform: rotate(90deg)' : 'transform: rotate(-90deg)'" />
                            </div>
                        </div>
                        <div class="aie-container-panel">
                            <div class="aie-container-main"></div>
                        </div> -->

                        <!-- <div class="aie-content">
                            <Sidebar @updateLessonPlan="receiveLessonPlan" @updateLoading="updateLoading"/>
                        </div> -->
                        <div class="aie-container-panel">
                            <a-spin :spinning="spinning">
                                <div class="aie-container-main"></div>
                                <loading v-if="loadings" class="loading-center" />
                            </a-spin>
                        </div>
                    </div>

                <div class="aie-container-footer"></div>
            </div>

    </div>
    <div v-if="showInput" class="input-popup"
    :style="{ top: inputPopupPosition.top + 'px', left: inputPopupPosition.left + 'px' }">
        <input v-model="inputValue" placeholder="输入内容..." />
        <button @click="insertContent">确认</button>
    </div>
    <div v-if="showInput" class="overlay" @click="closeInputPopup"></div>
    <div
      v-if="showDropdown"
      class="dropdown"
      :style="{ top: dropdownPosition.top + 'px', left: dropdownPosition.left + 'px' }"
    >
      <div class="dropdown-item" @click="handleSelect('选项1')">选项 1</div>
      <div class="dropdown-item" @click="handleSelect('选项2')">选项 2</div>
      <div class="dropdown-item" @click="handleSelect('选项3')">选项 3</div>
    </div>

    <div class="right">

        <div class="right-header">
        </div>
        <div class="main-content">

            <div class="chat-window">
                <div class="message-user" v-show="finishInput">
                    <div class="bubble">
                        <span> {{ keyword }} </span>
                    </div>
                </div> 
                <div class="message-ai">  
                    <div class="avatar">
                        <img src="@/assets/image/avatar.svg" alt="机器人图标" class="robot-icon">
                    </div>
                    <img v-if="aiLoading" src="@/assets/image/3dots.svg" class="robot-icon">
                    <div class="bubble" v-else>
                        <span> {{ timelineItems }} </span>
                    </div>
                </div>

            </div>

            <!-- 保存按钮
            <button class="save-btn" @click="saveTemp()">保存</button> -->

            <!-- 底部输入区域 -->
            <div class="input-wrapper">

                <a-input 
                    class="input-field"
                    placeholder="请输入"
                    v-model:value="keyword"
                    @pressEnter="aiChat"
                    :bordered="false"
                
                />


                <div class="input-actions">
                    <span class="send" @click="aiChat()">
                        <img style="width: 18px; height: 16px;" src="@/assets/image/plane.svg"/>
                    </span>
                </div>
            </div>

        </div>

    </div>
</div>
</template>

<script setup lang="ts">
import { AiEditor } from "aieditor";
import { parseMarkdown } from "@/utils/markdownParser";
import "aieditor/dist/style.css";
import { onMounted, ref, watch, nextTick } from "vue";
import type { TabsPaneContext } from "element-plus";
import { Document, Packer, Paragraph, TextRun } from "docx";
import type { ISectionOptions } from "docx";
import { saveAs } from 'file-saver';
// @ts-ignore
import html2pdf from 'html2pdf.js';
import loading from "@/components/ui/loading.vue";
import { getTeachPlan, aiConversation} from '@/services/api/LessonPlan'
import { defineEmits } from 'vue'
import { CaretDownOutlined,SearchOutlined } from '@ant-design/icons-vue';

const spinning = ref(false)
const streamLoading =ref(false)
const finishInput = ref(false)

// 定义 emit 事件类型
const emit = defineEmits(["updateTeachPlan"]);

const props = defineProps({
  mode: String,
  teachPlanData: Object,
  listData: Object,
  title: String
})

const streamValue = ref('');

onMounted(() => {
    initEditor();
    if (!sessionStorage.getItem("clear")) {
        aiEditorInstance?.clear();
        sessionStorage.setItem("clear", "true");
    }
    // 初始化后，内容存在时，手动更新一次目录
    // setTimeout(() => {
    //     if (aiEditorInstance && !aiEditorInstance.isEmpty()) {
    //         updateOutLine(aiEditorInstance);
    //         createOutLine(aiEditorInstance!);
    //     }
    // }, 300);

    
    if (props.mode === "create"){
        const fileValue = ref<File | null>(null);
        const propsFile = props.teachPlanData!.file;
        if (propsFile) {
            // 从 base64 字符串恢复成 Blob
            const byteCharacters = atob(propsFile.byte.split(',')[1]);  // 移除 'data:image/png;base64,' 部分
            const byteArrays = [];

            for (let offset = 0; offset < byteCharacters.length; offset++) {
            byteArrays.push(byteCharacters.charCodeAt(offset));
            }

            // 生成 Blob 并将其转换为 File
            const byteArray = new Uint8Array(byteArrays);
            const newFile = new File([byteArray], propsFile.name, { type: propsFile.type });
            fileValue.value = newFile;
            props.teachPlanData!.file = fileValue.value
        }

        teachPlan(props.teachPlanData)
    }
    else{
        // console.log('listData',props.listData?.content);
        if (props.listData?.content && aiEditorInstance) {    
            aiEditorInstance.clear();
            if (aiEditorInstance.isEmpty()) {
                aiEditorInstance.setMarkdownContent(props.listData?.content);
            } else {
                aiEditorInstance.focus().insertMarkdown(props.listData?.content);
            }
        }
        //去除内容里面的首行标题
        aiEditorInstance!.setMarkdownContent(aiEditorInstance!.getMarkdown().replace(/^# (.+)$/m, ''));
        //生成左侧目录样式
        createOutLine(aiEditorInstance!);
    }
    
})


const teachPlan = async (params:any) => {
  spinning.value = true
  streamLoading.value = true
  const res = await getTeachPlan(params)
  if (!res.body) {
    console.error('响应体为空');
    return;
  }
  const reader: ReadableStreamDefaultReader = res.body.getReader()
  const decoder = new TextDecoder('utf-8')

  const readStream = () => {
    spinning.value = false
    reader.read().then(({ done, value }) => {
      if (done) {
        streamLoading.value = false
        //生成左侧目录样式
        createOutLine(aiEditorInstance!);

        // 内容页面滚动到顶部
        const chatContent = document.querySelector('.aie-container-panel');
        if (chatContent) {
            chatContent.scrollTop = 0;
        }

        // 导航页面滚动到顶部
        const left = document.querySelector('#outline');
        if (left) {
            left.scrollTop = 0;
        }

        console.log("donedonedonedonedone",aiEditorInstance!.getMarkdown())
        //向父组件传递标题
        // emit('emitTitle', aiEditorInstance!.getMarkdown().match(/^# (.+)$/m)[1])
        
        //然后去除内容里面的首行标题
        aiEditorInstance!.setMarkdownContent(aiEditorInstance!.getMarkdown().replace(/^# (.+)$/m, ''));
        return
      }
      const chunk = decoder.decode(value, { stream: true })
      streamValue.value += chunk

      if (streamValue.value && aiEditorInstance) {            
            aiEditorInstance.clear();
            if (aiEditorInstance.isEmpty()) {
                aiEditorInstance.setMarkdownContent(streamValue.value);
            } else {
                aiEditorInstance.focus().insertMarkdown(streamValue.value);
            }
      }

      updateOutLine(aiEditorInstance!);
      readStream()
    })
  }
  readStream()
}



const isDirectory = ref(false);
//const props = defineProps(["lesson_plan"]);
// const emit = defineEmits(["update:lesson_plan"]);

// 用于存储接收到的 lesson_plan
const receivedLessonPlan = ref('');

const loadings = ref(false);

// 更新加载状态
const updateLoading = (loading: boolean) => {
  console.log('更新加载状态:', loading);
  loadings.value = loading;
}

// 定义接收数据的函数
const receiveLessonPlan = (plan:any) => {
  receivedLessonPlan.value = plan;
  //console.log('接收到的 lesson_plan:', receivedLessonPlan.value);
  if (plan && aiEditorInstance) {
        aiEditorInstance.clear();
        if (aiEditorInstance.isEmpty()) {
            aiEditorInstance.setMarkdownContent(plan);
        } else {
            aiEditorInstance.focus().insertMarkdown(plan);
        }
    }
    updateOutLine(aiEditorInstance!);
};

// 编辑器实例
let aiEditorInstance: AiEditor | null = null;

const showInput = ref(false);
const inputValue = ref("");
const selectedText = ref(""); // 记录选中的文字
const showDropdown = ref(false)
const dropdownPosition = ref({ top: 0, left: 0 })
const inputPopupPosition = ref({ top: 0, left: 0 });

//浮动菜单输入框确认按钮
const insertContent = () => {
    if (inputValue.value.trim() && aiEditorInstance) {
        const state = aiEditorInstance.innerEditor?.state;
        const view = aiEditorInstance.innerEditor?.view;

        if (!state || !view) return;

        const pos = state.selection.to; // 获取选区结束位置
        aiEditorInstance.focusPos(pos);
        aiEditorInstance.insert(inputValue.value.trim());
        // return
        // const newContent = `${inputValue.value.trim()}`;
        // aiEditorInstance.focus().insert(newContent);
        updateOutLine(aiEditorInstance);
    }

    // 清空输入框并关闭
    inputValue.value = "";
    selectedText.value = "";
    showInput.value = false;
};

const closeInputPopup = () => {
    showInput.value = false;
    selectedText.value = "";
};

// 初始化编辑器
const initEditor = () => {
    aiEditorInstance = new AiEditor({
        element: "#aiEditor",
        placeholder: "点击输入内容...",
        contentRetention: true,
        content: "",
        toolbarKeys: ["undo", "redo", "brush", "eraser",
        "|", "heading", "font-family", "font-size",
        "|", "bold", "italic", "underline", "strike", "link", "code", "subscript", "superscript", "hr", "todo", "emoji",
        "|", "highlight", "font-color",
        "|", "align", "line-height",
        "|", "bullet-list", "ordered-list", "indent-decrease", "indent-increase", "break",
        "|", "image", "video", "attachment", "quote", "code-block", "table",
        "|", "source-code", "printer", "fullscreen",
        {
            id: "export-word",
            tip: "导出Word",
            icon: `
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16 8V16H14L12 14L10 16H8V8H10V13L12 11L14 13V8H15V4H5V20H19V8H16ZM3 2.9918C3 2.44405 3.44749 2 3.9985 2H16L20.9997 7L21 20.9925C21 21.5489 20.5551 22 20.0066 22H3.9934C3.44476 22 3 21.5447 3 21.0082V2.9918Z"></path></svg>
                    `,
            onClick: () => {
                if (aiEditorInstance) {
                    const content = aiEditorInstance.getHtml();
                    console.log(content,'content内容');
                    /* htmlToDocx(content, 'lesson_plan.docx', {
                        pageSize: "A4" as any,
                        orientation: "portrait",
                        margins: { top: 720, bottom: 720, left: 720, right: 720 } // 单位是 1/20 pt，720 = 0.5 inch
                    } as any).then((result: Blob | ArrayBuffer) => {
                        if(result instanceof Blob){
                            saveAs(result, 'lesson_plan.docx');
                        }else{
                            saveAs(new Blob([result]), 'lesson_plan.docx');
                        }
                    }); */
                    // const textContent =  parseMarkdown(content)
                    const sections = htmlToDocxSections(content);
                    console.log(sections,'textContent内容');
                    // return
                    const doc = new Document({
                        sections: [
                            {
                                children: sections
                                // [
                                //     new Paragraph(textContent)
                                // ]
                            } as ISectionOptions
                        ]
                    });
                    Packer.toBlob(doc).then((blob) => {
                        saveAs(blob, props.title);
                    });
                }
            }
        },
        {
            id: 'export_pdf',
            tip: '导出PDF',
            icon: ` <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M5 4H15V8H19V20H5V4ZM3.9985 2C3.44749 2 3 2.44405 3 2.9918V21.0082C3 21.5447 3.44476 22 3.9934 22H20.0066C20.5551 22 21 21.5489 21 20.9925L20.9997 7L16 2H3.9985ZM10.4999 7.5C10.4999 9.07749 10.0442 10.9373 9.27493 12.6534C8.50287 14.3757 7.46143 15.8502 6.37524 16.7191L7.55464 18.3321C10.4821 16.3804 13.7233 15.0421 16.8585 15.49L17.3162 13.5513C14.6435 12.6604 12.4999 9.98994 12.4999 7.5H10.4999ZM11.0999 13.4716C11.3673 12.8752 11.6042 12.2563 11.8037 11.6285C12.2753 12.3531 12.8553 13.0182 13.5101 13.5953C12.5283 13.7711 11.5665 14.0596 10.6352 14.4276C10.7999 14.1143 10.9551 13.7948 11.0999 13.4716Z"></path></svg>`,
            onClick: () => {
                if (aiEditorInstance) {
                    const content = aiEditorInstance.getHtml();
                    const element = document.createElement('div');
                    element.innerHTML = content;
                    const opt = {
                        margin:       10,
                        filename:     'lesson_plan.pdf',
                        image:        { type: 'jpeg', quality: 0.98 },
                        html2canvas:  { scale: 2 },
                        jsPDF:        { unit: 'mm', format: 'a4', orientation: 'portrait' }
                    };
                    html2pdf().from(element).set(opt).save();
                }
            }
        }
    ],
        textSelectionBubbleMenu: {
            enable: true,
            items: [
                // "ai",
                "Bold",
                "Italic",
                "Underline",
                // {
                //     id: "customInput",
                //     title: "输入内容",
                //     icon: `
                //         <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                //             <path d="M5 4h14a1 1 0 011 1v14a1 1 0 01-1 1H5a1 1 0 01-1-1V5a1 1 0 011-1zm0-2a3 3 0 00-3 3v14a3 3 0 003 3h14a3 3 0 003-3V5a3 3 0 00-3-3H5z"></path>
                //             <path d="M6 11h12v2H6z"></path>
                //         </svg>
                //     `,
                //     onClick: (editor) => {
                //         //showInput.value = true;
                //         const selected = aiEditorInstance?.getSelectedText();
                //         const selection = window.getSelection()
                //         if (selected && selected.trim() &&  selection?.rangeCount) {
                //             const rect = selection.getRangeAt(0).getBoundingClientRect()
                //             inputPopupPosition.value = {
                //                 top: rect.top + window.scrollY + 50, 
                //                 left: rect.left + window.scrollX
                //             };
                //             selectedText.value = selected.trim();
                //             console.log("选中的文字：", selectedText.value);
                //             inputValue.value = "";
                //             showInput.value = true;
                //         } else {
                //             alert("请先选中一段文字！");
                //         }
                //     },
                // },
                // {
                //     id: 'dropdown',
                //     title: '选择操作',
                //     icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                //                      <path d="M5 4h14a1 1 0 011 1v14a1 1 0 01-1 1H5a1 1 0 01-1-1V5a1 1 0 011-1zm0-2a3 3 0 00-3 3v14a3 3 0 003 3h14a3 3 0 003-3V5a3 3 0 00-3-3H5z"></path>
                //                         <path d="M6 11h12v2H6z"></path>
                //                     </svg>`,
                //     onClick: (editor) => {
                //         const selection = window.getSelection()
                //         if (selection?.rangeCount) {
                //         const rect = selection.getRangeAt(0).getBoundingClientRect()
                //         showDropdown.value = true
                //         dropdownPosition.value = {
                //             top: rect.bottom + window.scrollY + 54,
                //             left: rect.left + window.scrollX + 10,
                //             }
                //         }
                //     },
                // },
            ],
        },

        // ai: {
        //     models: {
        //         spark: {},
        //     },
        // },
        
        onChange: (editor) => {

            if(streamLoading.value){
                // 内容页面滚动到底部
                const chatContent = document.querySelector('.aie-container-panel');
                if (chatContent) {
                    chatContent.scrollTop = chatContent.scrollHeight;
                }

                // 导航页面滚动到底部
                const left = document.querySelector('#outline');
                if (left) {
                    left.scrollTop = left.scrollHeight;
                }
            }
            
            if(isOutlineUpdate.value){
                updateOutLine(editor);
            }else{
                createOutLine(editor);
            }

            emit('updateTeachPlan', editor.getMarkdown())
        },
    });
};
// 悬浮框下拉框选项
function handleSelect(option: string) {
  if (aiEditorInstance) {
    aiEditorInstance.focus().insert(`【${option}】`)
  }
  showDropdown.value = false
}
// 将 HTML 转换为 docx 段落和文本运行
const htmlToDocxSections = (html: string): any[] => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const sections: any[] = [];

    const traverse = (node: Node) => {
        if (node.nodeType === Node.TEXT_NODE) {
            const text = node.textContent?.trim();
            if (text) {
                sections.push(
                    new Paragraph({
                        children: [new TextRun(text)]
                    })
                );
            }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement;

            if (element.tagName === 'BR') {
                sections.push(new Paragraph({}));
            } else if (element.tagName === 'P') {
                const text = element.textContent?.trim();
                if (text) {
                    sections.push(new Paragraph({ children: [new TextRun(text)] }));
                }
            } else if (element.tagName === 'STRONG') {
                const text = element.textContent?.trim();
                if (text) {
                    sections.push(
                        new Paragraph({
                            children: [new TextRun({ text, bold: true })]
                        })
                    );
                }
            } else if (element.tagName === 'H3' || element.tagName === 'H4') {
                const text = element.textContent?.trim();
                if (text) {
                    sections.push(
                        new Paragraph({
                            children: [
                                new TextRun({
                                    text: text,
                                    bold: true, // 可选：标题常用加粗
                                    size: element.tagName === 'H3' ? 32 : 20, // 单位1/2pt, 16pt = 32, 14pt = 28
                                    color: "000000" // 黑色
                                })
                            ]
                        })
                    );
                }
            } else if (element.tagName === 'UL') {
                const listItems = Array.from(element.querySelectorAll('li'));
                listItems.forEach(item => {
                    const text = item.textContent?.trim();
                    if (text) {
                        sections.push(
                            new Paragraph({
                                text,
                                bullet: { level: 0 }
                            })
                        );
                    }
                });
            } else {
                const children = element.childNodes;
                for (let i = 0; i < children.length; i++) {
                    traverse(children[i]);
                }
            }
        }
    };

    traverse(doc.body);
    return sections;
};

// 生成文档目录
function updateOutLine(editor: AiEditor) {
    const outlineContainer = document.querySelector("#outline");
    while (outlineContainer?.firstChild) {
        outlineContainer.removeChild(outlineContainer.firstChild);
    }
    const outlines = editor.getOutline();
    
    for (let outline of outlines) {
        const child = document.createElement("div");
        child.classList.add(`aie-title${outline.level}`);
        child.style.marginLeft = `${10 * (outline.level - 1)}px`;
        child.innerHTML = `<a href="#${outline.id}">${outline.text}</a>`;
        child.addEventListener("click", (e) => {
            e.preventDefault();
            const el = editor.innerEditor.view.dom.querySelector(`#${outline.id}`) as HTMLElement;
            el.scrollIntoView({ behavior: "smooth", block: "center", inline: "nearest" });
            setTimeout(() => {
                editor.focusPos(outline.pos + outline.size - 1);
            }, 1000);
        });
        outlineContainer?.appendChild(child);
    }
}

const isOutlineUpdate = ref(true)
const teachPlanTitle = ref('')
const leftOutline = ref<any[]>([])

// 生成文档目录大纲（只有二级标题、匹配样式）
function createOutLine(editor: AiEditor) {
    const outlineContainer = document.querySelector("#outline");
    while (outlineContainer?.firstChild) {
        outlineContainer.removeChild(outlineContainer.firstChild);
    }
    const outlines = editor.getOutline();
    console.log("outlines",outlines);

    leftOutline.value = []
    for (let outline of outlines) {
        if (outline.level === 1){
            teachPlanTitle.value = outline
        }
        else if(outline.level === 2){
            leftOutline.value.push(outline)
        }
        else{
            continue
        }
    }

    isOutlineUpdate.value = false
}

const jump = (outline:any) =>{
    const editor = aiEditorInstance!
    const el = editor.innerEditor.view.dom.querySelector(`#${outline.id}`) as HTMLElement;
    el.scrollIntoView({ behavior: "smooth", block: "center", inline: "nearest" });
    setTimeout(() => {
        editor.focusPos(outline.pos + outline.size - 1);
    }, 1000);
}

// ai聊天
const keyword = ref('')
const aiLoading = ref(false)
const timelineItems = ref('你好，有什么可以帮助你的吗？');

const aiChat= async () => {
  finishInput.value = true
  aiLoading.value = true;

  timelineItems.value = ''

  const res = await aiConversation(keyword.value)
  if (!res.body) {
    console.error('响应体为空');
    return;
  }
  const reader: ReadableStreamDefaultReader = res.body.getReader()
  const decoder = new TextDecoder('utf-8')

  const readStream = () => {
    reader.read().then(({ done, value }) => {
      if (done) {    
        aiLoading.value = false;    
        return
      }

      const chunk = decoder.decode(value, { stream: true })
      timelineItems.value += chunk

      const chatContent = document.querySelector('.aie-container-panel');
      console.log('chatContent',chatContent);
      
      if (chatContent) {
        chatContent.scrollTop = chatContent.scrollHeight;
      }

      readStream()
    })
  }
  readStream()    
}

</script>


<style scoped lang="scss">
.content{
  width: 100%;
  height: 100%;
  display: flex;
  justify-content:space-between;
  gap:20px;
  padding-top: 20px;
}

.left{
  width: 15%;
  height: 100%;
  background-color: white;
  overflow: auto;
  padding: 20px;
}

.card-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.card {
  min-height:40px;
  display: flex;
  border: 1px solid #4993ff;
  border-radius: 0.2vw;
  overflow: hidden;
  font-size: 1vw;
  line-height: 2vw;
  cursor: pointer;
}

.card-index {
  background-color: #4993ff;
  color: white;
  width: 20%;
  text-align: center;
  font-weight: bold;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center;     /* 垂直居中 */
}

.card-title {
  flex: 1;
  background-color: white;
  color: #333;
  padding-left: 12px;
  border-left: 1px solid #4993ff;
  display: flex;
  align-items: center;     /* 垂直居中 */
}


.center{
  width: 65%;
  height: 100%;
  background-color: white;
  overflow: auto;
}

.right{
  width: 20%;
  height: 100%;
  background-color: white;
  overflow: auto;
}

.right-header{
  width:100%;
  border: 1px;
}


//目录
.aie-directory {
    width: 200px;
    background-color: white;
    padding: 0 20px;
    margin-left: 10px;
    position: fixed;
    top: 50px;
    height: calc(100vh - 60px);
    border-radius: 5px;
    box-shadow: 1px 1px 5px #ededed;
    transition: .2s linear;
    left: 0;


    .aie-panel{
        background-color: #fff;
    }


    .arrow {
        position: absolute;
        top: 45%;
        right: -50px;
        transform: rotate(-90deg);
        transition: .5s linear;
        transform-origin: center;
    }

    :deep(#outline) {
        a {
            color: #000;
            text-decoration: none;
            margin: 10px;
            // display: -webkit-box;
            /* 使用WebKit的弹性盒模型 */
            // -webkit-line-clamp: 1;
            /* 限制显示的文本行数 */
            // -webkit-box-orient: vertical;
            /* 设置盒模型的方向为垂直 */
            overflow: hidden;
            /* 隐藏超出容器的部分 */
        }

        .aie-title1 {
            font-size: 18px;
            font-weight: 900;
        }

        .aie-title2 {
            font-size: 17px;
            font-weight: 800;
        }

        .aie-title3 {
            font-size: 16px;
            font-weight: 700;
        }

        .aie-title4 {
            font-size: 15px;
            font-weight: 600;
        }

        .aie-title5 {
            font-size: 14px;
            font-weight: 500;
        }

        .aie-title6 {
            font-size: 13px;
            font-weight: 400;
        }
    }

}

.page-header {
    background-color: #fff;
    height: 50px;
    line-height: 50px;
    padding: 0 2rem;
    display: flex;
    position: sticky;
    border-bottom: 1px solid #efefef;
    top: 0;
    z-index: 1;
}

.aie-header-panel {
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    padding: 10px 12px;
    border-bottom: 1px solid #ededf2;
    top: 0;
    z-index: 2;
}

.aie-header-panel .aie-header>div {
    align-items: center;
    justify-content: center;
    padding: 20px 0;
}

:deep(.aie-container aie-header>div) {
    border-bottom: none !important;
}

.aie-container {
    border: none !important;
}

.aie-content{
    //background-color: aquamarine;
    //width: 500px;
    //width: 24vw;
    // min-height: 100%;
    min-width: 360px;
    position: sticky;
    top: calc(59px); 
    align-self: flex-start; 
    background-image: url('@/assets/image/backlessonplan.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.aie-main {
    height: 100%;
    display: flex;
    overflow: auto;
}

.aie-container-panel {
    //width: calc(100% - 2rem - 2px);
    /* max-width: 800px;
    margin: 1rem auto;
    border: 1px solid rgb(229 231 235);
    
    height: 100%;
    min-height: 100vh;
    position: sticky;
    padding: 1rem;
    z-index: 1; */

    width: 100%;
    flex:1;
    background-color: #fff;
    padding: 20px 60px;
    overflow: auto;
}


.aie-directory h5 {
    color: #000000c4;
    font-size: 16px;
    text-indent: 4px;
    line-height: 32px;
}

.aie-directory a {
    height: 30px;
    font-size: 14px;
    color: #000000a3;
    text-indent: 4px;
    line-height: 30px;
    text-decoration: none;
    width: 100%;
    display: inline-block;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

.aie-directory a:hover {
    cursor: pointer;
    background-color: #334d660f;
    border-radius: 4px;
}

.aie-title1 {
    font-size: 14px;
    font-weight: 500;
}

.aie-title2,
.aie-title3,
.aie-title4,
.aie-title5,
.aie-title6 {
    font-size: 12px;
}

.aie-directory-content {
    position: sticky;
    top: 108px;
}

.aie-container-footer {
    display: none;
}

@media screen and (max-width: 1280px) {
    .aie-directory {
        display: none;
    }
}

@media screen and (max-width: 1400px) {
    .aie-directory {
        width: 200px;
    }
}

:deep(.el-tabs__item) {
    font-size: 20px;
    padding: 30px;
}


.input-popup {
    width: 400px;
    height: 50px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 5px;
    padding: 10px;
    background: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    z-index: 9999;
    animation: fadeIn 0.3s ease-in-out;
}

.input-popup input {
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    flex-grow: 1;
}

.input-popup button {
    background: #4caf50;
    color: #fff;
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

button:hover {
    opacity: 0.9;
}

/* 点击空白区域的遮罩层 */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    //background: rgba(0.1, 0, 0, 0);
    z-index: 9998;
    animation: fadeIn 0.3s ease-in-out;
}

/* 弹出动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.loading-center {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999; /* 加载组件在其他元素之上 */
}

.dropdown {
  position: absolute;
  background: white;
  border: 1px solid #ddd;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  z-index: 9999;
  width: 120px;
}

.dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: #f0f0f0;
}

.main-content {
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.4vw;
}

.message-user {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-direction: row-reverse;
  align-self: flex-end;
}

.message-ai {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  align-self: flex-start;
}

.avatar {
  flex-shrink: 0;
  width: 2vw;
  height: 2vw;
  text-align: center;
  line-height: 2vw;
  font-size: 1rem;
  user-select: none;
}

.bubble {
  flex: 1;
  min-width:80%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 1vw;
  border-radius: 0.8rem;
  background-color: rgba(237, 244, 255, 1);
  color: #000;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.4;
  font-size: 1rem;
}

.icon-header {
  text-align: center;
  margin-bottom: 30px;
}

.robot-icon {
  width: 30px;
  margin-bottom: 10px;
}



.text {
  width: 80px;
  color: #333;
}

input {
  flex: 1;
  border: none;
  border-bottom: 1px solid #eee;
  padding: 5px;
  outline: none;
}


.input-wrapper {
  width: 100%;
  height: 60px;
  display: inline-flex;
  align-items: center;
  background-color: rgba(240, 249, 255, 1);
  padding: 20px;
  border-radius: 81px;
  border: 1px solid rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.05);
}

.input-field {
  border: none;
  outline: none;
  padding: 0 10px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0px;
  line-height: 14px;
  background: transparent;

}

.input-actions {
  display: flex;
  align-items: center;
}

.send {
  width: 50px;
  height: 30px;
  background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
  color: white;
  padding: 7px 15px;
  border-radius: 148px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}


</style>