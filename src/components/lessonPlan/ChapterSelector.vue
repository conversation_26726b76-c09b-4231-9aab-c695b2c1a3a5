<template>
  <div>
    <!-- 搜索框 -->
    <a-input v-model:value="searchQuery" placeholder="请输入搜索内容" @input="handleSearch" style="margin-bottom: 10px;">
      <template #suffix>
        <SearchOutlined style="color:#C4C4C4"/>
      </template>
    </a-input>

    <!-- 全选复选框（单独处理） -->
    <div style="display: flex;margin-left: 4px; margin-bottom: 4px;">
      <a-checkbox
        v-model:checked="selectAll"
        @change="handleSelectAll"
      >              
      </a-checkbox>
      <div style="flex:1; display: flex; justify-content: space-between;margin-left: 8px;">
        <span>全选 </span>
      </div>
    </div>
    
    <div class="checkbox-group-wrapper">
      <!-- 树形结构 -->
      <a-tree
        :treeData="filteredTreeData"
        :checkedKeys="checkedKeys"
        checkable
        @check="onCheck"
        :checkStrictly="false"
      />
    </div>

  </div>
</template>

<script lang="ts" setup>
import { ref, watch, defineProps, defineEmits } from 'vue';
import { SearchOutlined } from '@ant-design/icons-vue';

type Key = string | number;

interface TreeNode {
  title: string;
  key: string;
  children?: TreeNode[];
}

const props = defineProps<{
  treeData: TreeNode[];
}>();

const emits = defineEmits<{
  (e: 'update:checkedKeys', checkedKeys: Key[]): void;
}>();

const searchQuery = ref<string>('');
const filteredTreeData = ref<TreeNode[]>(props.treeData);
const checkedKeys = ref<Key[]>([]);
const selectAll = ref<boolean>(false);

/**
 * 过滤树形结构
 */
const filterTree = (nodes: TreeNode[], query: string): TreeNode[] => {
  const filtered: TreeNode[] = [];
  for (const node of nodes) {
    const hasMatch = node.title.includes(query);
    let children: TreeNode[] = [];

    if (node.children) {
      children = filterTree(node.children, query);
    }

    if (hasMatch) {
      filtered.push({
        ...node,
        children: node.children,
      });
    } else if (children.length) {
      filtered.push({
        ...node,
        children,
      });
    }
  }
  return filtered;
};

const handleSearch = () => {
  if (searchQuery.value) {
    filteredTreeData.value = filterTree(props.treeData, searchQuery.value);
  } else {
    filteredTreeData.value = props.treeData;
  }
};

/**
 * 递归获取所有 key
 */
const getAllKeys = (nodes: TreeNode[]): Key[] => {
  const keys: Key[] = [];
  for (const node of nodes) {
    keys.push(node.key);
    if (node.children) {
      keys.push(...getAllKeys(node.children));
    }
  }
  return keys;
};

const handleSelectAll = () => {
  if (selectAll.value) {
    checkedKeys.value = getAllKeys(filteredTreeData.value);
  } else {
    checkedKeys.value = [];
  }
};

watch(checkedKeys, () => {
  const totalKeys = getAllKeys(filteredTreeData.value);
  selectAll.value = checkedKeys.value.length === totalKeys.length;

  // emit 选中 keys
  emits('update:checkedKeys', checkedKeys.value);
});

const onCheck = (checkedKeysValue: Key[] | { checked: Key[]; halfChecked: Key[] }) => {
  if (Array.isArray(checkedKeysValue)) {
    checkedKeys.value = checkedKeysValue;
  } else {
    checkedKeys.value = checkedKeysValue.checked;
  }
};
</script>

<style scoped lang="scss">

:deep(.ant-tree-switcher) {
  display: flex;
  align-items: center;
  justify-content: center;
}
:deep(.ant-tree-checkbox){
  margin: 0;
}

.checkbox-group-wrapper{
  width: 100%;
  max-height: 160px;
  overflow: auto;
}

.checkbox-group-wrapper::-webkit-scrollbar {
  width: 4px; /* 滚动条宽度 */
}

.checkbox-group-wrapper::-webkit-scrollbar-thumb {
  background-color: #C7DDFC; /* 滑块颜色 */
  border-radius: 136px; /* 圆角 */
}

.checkbox-group-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #C7DDFC; /* 滑块 hover 时颜色 */
}

</style>