<script setup lang="ts">
  const props = defineProps<{
    imgPath: string
    name: string
    description: string
    code: {
      language: string
      content: string
    }
  }>()

  const isDetailModalOpen = ref(false)

  const highlighter = await getShi<PERSON><PERSON>ighlighter()

  const highlightedCode = computed(() =>
    highlighter.codeToHtml(props.code.content, {
      lang: props.code.language,
      themes: {
        light: 'catppuccin-latte',
        dark: 'catppuccin-macchiato',
      },
    }),
  )
</script>

<template>
  <div
    class="cursor-pointer rounded-[5px] bg-white p-5 shadow-[0px_2px_19px_rgba(29,79,153,0.02)] transition-transform hover:-translate-y-[2px]"
    @click="isDetailModalOpen = true"
  >
    <div class="mb-2 flex items-center space-x-2.5">
      <img
        :src="props.imgPath"
        class="size-10"
      />

      <div class="text-xl font-medium text-black">
        {{ props.name }}
      </div>
    </div>

    <TextEllipsis
      :text="props.description"
      :lines="3"
      class="text-foreground-4"
    />

    <AModal
      :width="1000"
      wrap-class-name="reset-ant-modal [&_.ant-modal-body]:overflow-y-auto [&_.ant-modal-body]:max-h-[540px] scrollbar-gray [&_.ant-modal-body]:pb-2.5 [&_.ant-modal-header]:pb-0"
      title="工具详情"
      v-model:visible="isDetailModalOpen"
      :footer="null"
    >
      <div class="flex">
        <div class="flex-1 shrink-0 space-y-4 border-r border-dashed pt-10 pr-5">
          <div class="flex items-center space-x-2.5">
            <img
              :src="props.imgPath"
              class="size-10"
            />
            <div class="text-xl font-medium text-black">{{ props.name }}</div>
          </div>

          <div class="text-foreground-4 whitespace-pre-wrap">
            {{ props.description }}
          </div>
        </div>

        <div class="flex-2 pl-2.5 contain-inline-size">
          <div class="bg-[rgba(247,247,247,0.8)]">
            <div class="flex items-center bg-[rgba(204,204,204,0.1)] p-2.5">
              <div class="font-medium text-black">代码示例</div>
              <CopyButton
                :source="props.code.content"
                class="text-foreground-3! ml-auto"
              />
            </div>

            <div
              class="scrollbar-gray h-[400px] max-w-full overflow-auto [&_.line]:px-4 [&_code]:block [&_code]:py-4 [&_pre]:bg-transparent!"
              v-html="highlightedCode"
            />
          </div>
        </div>
      </div>
    </AModal>
  </div>
</template>
