<script setup lang="ts">
  import { message, type FormProps } from 'ant-design-vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  import IconExclamationMark from '~icons/ant-design/exclamation-circle-outlined'

  export interface FormValues {
    text: string
  }

  const props = defineProps<FormProps>()
  const emit = defineEmits<{ 'update:status': [MutationStatus] }>()

  const formValues = ref<Partial<FormValues>>({})

  const queryClient = useQueryClient()
  const { mutate, status } = useMutation({
    mutationFn() {
      const text = formValues.value.text?.trim() ?? ''
      return baseFetcher('/keyword_rule/', {
        method: 'post',
        body: {
          keyword: text,
        },
      })
    },
    onSuccess() {
      return queryClient.invalidateQueries({ queryKey: ['system', 'filter-words'] })
    },
    onError(error) {
      message.error(error.message || '添加词汇失败')
    },
  })

  watchEffect(() => emit('update:status', status.value))
</script>

<template>
  <AForm
    v-bind="props"
    :model="formValues"
    @finish="mutate()"
  >
    <AFormItem
      required
      label="违禁词汇"
      name="text"
    >
      <ATextarea
        v-model:value="formValues.text"
        placeholder="请输入筛选词汇"
        :maxlength="4096"
        show-count
        :rows="6"
        class="text-black!"
      />

      <div class="mt-3 flex space-x-1.5 text-xs text-black">
        <IconExclamationMark />
        <div>每个词汇一行，可多行输入</div>
      </div>
    </AFormItem>
  </AForm>
</template>
