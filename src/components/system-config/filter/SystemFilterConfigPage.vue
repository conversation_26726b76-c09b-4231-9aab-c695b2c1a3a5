<script lang="ts">
  import type { ShallowRef } from 'vue'

  type FilterWordId = string

  const systemFilterContextInjectionKey = Symbol('system-filter') as InjectionKey<{
    selectedWords: Readonly<ShallowRef<FilterWordId[]>>
    setSelectedWords(value: FilterWordId[]): void

    rawWords: Readonly<ShallowRef<API.SystemConfig.FilterWord[]>>
  }>

  export function injectSystemFilterContext() {
    const context = inject(systemFilterContextInjectionKey)
    if (!context) {
      throw new Error('systemFilterContext is not provided')
    }
    return context
  }
</script>

<script setup lang="ts">
  import { Modal, message } from 'ant-design-vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { h } from 'vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  import IconSearch from '~icons/ant-design/search-outlined'
  import IconTrash from '~icons/lucide/trash-2'
  import IconLoading from '~icons/lucide/loader-circle'

  const searchString = ref('')
  const searchStringDebounced = refDebounced(searchString, 200)

  const { data, fetchStatus } = useQuery({
    queryKey: systemConfigQueryKey.filterWordList({ search: searchStringDebounced }),
    async queryFn() {
      const { data } = await baseFetcher<{
        data: { total: number; data: API.SystemConfig.FilterWord[] }
      }>('/keyword_rule/', {
        query: {
          keyword__icontains: searchStringDebounced.value,
        },
      })

      return data
    },
    placeholderData(oldData) {
      return oldData ?? { total: 0, data: [] }
    },
  })

  const rawWords = computed(() => data.value?.data ?? [])

  const selectedWords = ref<string[]>([])
  provide(systemFilterContextInjectionKey, {
    selectedWords: shallowReadonly(selectedWords),
    setSelectedWords(value) {
      selectedWords.value = value
    },

    rawWords: computed(() => rawWords.value ?? []),
  })

  // 全选逻辑
  const isAllSelected = computed(() => {
    return rawWords.value.length > 0 && selectedWords.value.length === rawWords.value.length
  })

  const isIndeterminate = computed(() => {
    return selectedWords.value.length > 0 && selectedWords.value.length < rawWords.value.length
  })

  function handleSelectAll(e: { target: { checked: boolean } }) {
    const checked = e.target.checked
    if (checked) {
      selectedWords.value = rawWords.value.map((word) => word.id)
      console.log(selectedWords.value)
    } else {
      selectedWords.value = []
    }
  }

  const queryClient = useQueryClient()
  const { mutate: batchDeleteWords, status: batchDeleteStatus } = useMutation({
    mutationFn() {
      return baseFetcher('/keyword_rule/multiple_delete/', {
        method: 'delete',
        body: {
          pks: selectedWords.value,
        },
      })
    },
    onSuccess() {
      selectedWords.value = []
      return queryClient.invalidateQueries({ queryKey: ['system', 'filter-words'] })
    },
    onError(error) {
      message.error(error.message || '批量删除失败')
    },
  })

  function confirmBatchDelete() {
    Modal.confirm({
      icon: h(ExclamationCircleOutlined),
      content: `确定要删除选中的 ${selectedWords.value.length} 个词汇吗？`,
      okButtonProps: {
        danger: true,
      },
      centered: true,
      cancelText: '取消',
      onOk() {
        batchDeleteWords()
      },
      wrapClassName: 'reset-ant-modal',
    })
  }

  const isCreateModalOpen = ref(false)
  const mutationStatus = ref<MutationStatus>('idle')
  watchEffect(() => {
    if (mutationStatus.value === 'success') {
      isCreateModalOpen.value = false
    }
  })

  const isEmpty = computed(() => rawWords.value.length === 0 && fetchStatus.value !== 'fetching')
</script>

<template>
  <div>
    <!-- 搜索和操作栏 -->
    <div class="flex flex-wrap items-center gap-y-4 px-(--page-px) pt-5">
      <!-- 搜索框 -->
      <AInput
        v-model:value="searchString"
        placeholder="输入词汇搜索"
        class="max-w-[300px] rounded-full!"
        allow-clear
      >
        <template #suffix>
          <IconSearch class="text-foreground-4" />
        </template>
      </AInput>

      <div class="text-foreground-3 ml-4">
        共有
        <span class="text-primary">{{ rawWords.length }}</span>
        个筛选结果
      </div>
    </div>

    <!-- 主面板 -->
    <div
      class="mx-(--page-px) mt-5 min-h-[50svh] rounded-lg bg-white px-5 py-7"
      style="box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05)"
    >
      <!-- 面板操作栏 -->
      <div class="mb-6 flex items-center justify-between">
        <!-- 全选 -->
        <ACheckbox
          :checked="isAllSelected"
          :indeterminate="isIndeterminate"
          @change="handleSelectAll"
          :disabled="rawWords.length === 0"
        >
          全选
        </ACheckbox>

        <!-- 操作按钮 -->
        <div class="flex items-center space-x-4">
          <AButton
            :disabled="selectedWords.length === 0"
            class="outline-a-button flex! items-center space-x-2"
            type="primary"
            ghost
            @click="confirmBatchDelete"
          >
            <IconLoading
              v-if="batchDeleteStatus === 'pending'"
              class="animate-spin"
            />
            <IconTrash v-else />
            删除
          </AButton>

          <AButton
            type="primary"
            class="gradient-a-button flex! items-center space-x-2"
            @click="isCreateModalOpen = true"
          >
            添加词汇
          </AButton>
        </div>
      </div>

      <!-- 空状态 -->
      <Empty
        v-if="isEmpty"
        class="mx-auto my-20 w-[335px]"
      >
        <template #message> 暂无数据</template>
      </Empty>

      <template v-else>
        <!-- 词汇列表 -->
        <ACheckboxGroup
          v-model:value="selectedWords"
          class="flex w-full flex-wrap gap-5 contain-inline-size"
        >
          <ACheckbox
            v-for="word in rawWords"
            :key="word.id"
            :value="word.id"
            class="whitespace-nowrap"
          >
            <TextEllipsis
              :lines="1"
              :text="word.keyword"
              :tooltip="{ title: word.keyword }"
            />
          </ACheckbox>
        </ACheckboxGroup>
      </template>
    </div>

    <!-- 添加词汇模态框 -->
    <AModal
      v-model:open="isCreateModalOpen"
      title="添加词汇"
      centered
      :ok-button-props="{
        // @ts-expect-error antdv poor typing
        form: 'create-filter-form',
        htmlType: 'submit',
        loading: mutationStatus === 'pending',
      }"
      destroy-on-close
      class="reset-ant-modal"
      width="600px"
    >
      <SystemFilterCreateForm
        id="create-filter-form"
        @update:status="mutationStatus = $event"
      />
    </AModal>
  </div>
</template>

<style lang="css" scoped>
  :deep(.ant-checkbox-wrapper) {
    color: var(--color-foreground-3);
  }
</style>
