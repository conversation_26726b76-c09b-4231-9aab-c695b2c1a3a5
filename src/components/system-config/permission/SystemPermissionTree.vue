<script setup lang="ts">
  import type { TreeProps } from 'ant-design-vue'

  const checkedKeys = defineModel<string[]>('checkedKeys', { default: [] })

  const props = withDefaults(defineProps<TreeProps>(), {
    defaultExpandAll: true,
    checkable: true,
  })
</script>

<template>
  <ATree
    v-bind="props"
    v-model:checked-keys="checkedKeys"
    @check="console.log"
    :class="[
      '[&_.ant-tree-title]:text-foreground-3! [&_.ant-tree-checkbox]:mt-0! [&_.ant-tree-checkbox]:mr-1!',
      '[&_.ant-tree-switcher]:flex! [&_.ant-tree-switcher]:items-center! [&_.ant-tree-switcher]:justify-center!',
    ]"
  />
</template>
