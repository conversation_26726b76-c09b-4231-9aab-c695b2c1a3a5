<script setup lang="ts">
  import { message, type TreeProps } from 'ant-design-vue'
  import { systemConfigQueryKey } from '@/queries/system-config'

  const { data: rawPermissions, suspense } = useQuery({
    queryKey: systemConfigQueryKey.permissionList(),
    async queryFn() {
      const permissionGroups = await baseFetcher<API.SystemConfig.PermissionGroup[]>('/group/')
      return permissionGroups
    },
  })

  await useNavigationSuspense({ delay: 500, suspense })

  const teacherPermissions = computed(() =>
    rawPermissions.value?.find((p) => p.group_name === '教师'),
  )
  const studentPermissions = computed(() =>
    rawPermissions.value?.find((p) => p.group_name === '学生'),
  )

  const teacherSavedPermissions = computed(() =>
    teacherPermissions.value?.models
      .flatMap((m) => m.permissions)
      .filter((p) => p.has_perm)
      .map((p) => String(p.id)),
  )
  const studentSavedPermissions = computed(() =>
    studentPermissions.value?.models
      .flatMap((m) => m.permissions)
      .filter((p) => p.has_perm)
      .map((p) => String(p.id)),
  )

  const teacherCheckedKeys = ref<string[]>([])
  const studentCheckedKeys = ref<string[]>([])

  // 初始化权限数据
  watchEffect(() => {
    teacherCheckedKeys.value = teacherSavedPermissions.value ?? []
  })

  watchEffect(() => {
    studentCheckedKeys.value = studentSavedPermissions.value ?? []
  })

  const teacherTreeData = computed<Required<TreeProps>['treeData']>(
    () =>
      teacherPermissions.value?.models.map((model) => ({
        title: model.model_name,
        key: model.model_name,
        children: model.permissions.map((permission) => ({
          title: permission.name,
          key: String(permission.id),
          selectable: false,
        })),
      })) ?? [],
  )

  const teacherTreeDataWithCheckAll = computed(() => {
    return [
      {
        title: '全选',
        key: 'check-all',
        selectable: false,
        children: teacherTreeData.value,
      },
    ]
  })

  const studentTreeData = computed<Required<TreeProps>['treeData']>(
    () =>
      studentPermissions.value?.models.map((model) => ({
        title: model.model_name,
        key: model.model_name,
        children: model.permissions.map((permission) => ({
          title: permission.name,
          key: String(permission.id),
        })),
      })) ?? [],
  )

  const studentTreeDataWithCheckAll = computed(() => {
    return [
      {
        title: '全选',
        key: 'check-all',
        selectable: false,
        children: studentTreeData.value,
      },
    ]
  })

  const queryClient = useQueryClient()
  const { mutate: savePermissions, status: saveStatus } = useMutation({
    async mutationFn() {
      return baseFetcher('/group/batch_update/', {
        method: 'post',
        body: [
          {
            group: teacherPermissions.value?.id,
            permissions: teacherCheckedKeys.value.map(Number).filter(Boolean),
          },
          {
            group: studentPermissions.value?.id,
            permissions: studentCheckedKeys.value.map(Number).filter(Boolean),
          },
        ],
      })
    },
    onSuccess() {
      message.success('保存设置成功')
    },
    onError(error) {
      message.error(error.message || '保存设置失败')
      return queryClient.invalidateQueries({ queryKey: systemConfigQueryKey.permissionList() })
    },
  })

  const roleConfigs = [
    {
      title: '老师',
      checkedKeys: teacherCheckedKeys,
      treeData: teacherTreeDataWithCheckAll,
    },
    {
      title: '学生',
      checkedKeys: studentCheckedKeys,
      treeData: studentTreeDataWithCheckAll,
    },
  ]
</script>

<template>
  <div class="h-full px-(--page-px) py-5 contain-size">
    <!-- 主面板 -->
    <div
      class="flex max-h-full flex-col rounded-lg bg-white"
      style="box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05)"
    >
      <!-- 上半部分：权限配置区域 -->
      <div class="grid grow grid-cols-1 overflow-y-auto lg:grid-cols-2">
        <div
          v-for="(config, index) in roleConfigs"
          :key="config.title"
          class="px-10 py-7.5"
          :class="[
            'border-dashed',
            index < roleConfigs.length - 1 && 'border-b lg:border-r lg:border-b-0',
          ]"
        >
          <!-- 角色标题 -->
          <h3 class="text-foreground-1 mb-9 text-lg font-medium">
            {{ config.title }}
          </h3>

          <!-- 权限树 -->
          <SystemPermissionTree
            v-if="config.treeData.value"
            :tree-data="config.treeData.value"
            v-model:checked-keys="config.checkedKeys.value"
          />
        </div>
      </div>

      <!-- 下半部分：保存操作区域 -->
      <div class="flex shrink-0 justify-end border-t border-dashed px-10 py-6">
        <AButton
          type="primary"
          class="gradient-a-button"
          :loading="saveStatus === 'pending'"
          @click="savePermissions()"
        >
          保存设置
        </AButton>
      </div>
    </div>
  </div>
</template>
