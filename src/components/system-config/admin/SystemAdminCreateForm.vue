<script setup lang="ts">
  import { message, type FormProps } from 'ant-design-vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  import IconExclamationMark from '~icons/ant-design/exclamation-circle-outlined'

  export interface FormValues {
    text: string
  }

  const props = defineProps<FormProps>()
  const emit = defineEmits<{ 'update:status': [MutationStatus] }>()

  const formValues = ref<Partial<FormValues>>({})

  const queryClient = useQueryClient()
  const { mutate, status } = useMutation({
    mutationFn() {
      const text = formValues.value.text?.trim() ?? ''
      const usernames = text.split(/[\s,]/).filter(Boolean)
      return baseFetcher('/admin-setting/', {
        method: 'patch',
        body: {
          usernames,
          is_admin: true,
        },
      })
    },
    onSuccess() {
      return queryClient.invalidateQueries({ queryKey: ['system', 'admins'] })
    },
    onError(error) {
      message.error(error.message || '导入失败')
    },
  })

  watchEffect(() => emit('update:status', status.value))
</script>

<template>
  <AForm
    v-bind="props"
    :model="formValues"
    @finish="mutate()"
  >
    <AFormItem
      required
      label="导入工号"
      name="text"
    >
      <ATextarea
        v-model:value="formValues.text"
        placeholder="请输入工号"
        :maxlength="4096"
        show-count
        :rows="6"
        class="text-black!"
      />

      <div class="mt-3 flex space-x-1.5 text-xs text-black">
        <IconExclamationMark />
        <div>每个工号一行，可多行输入</div>
      </div>
    </AFormItem>
  </AForm>
</template>
