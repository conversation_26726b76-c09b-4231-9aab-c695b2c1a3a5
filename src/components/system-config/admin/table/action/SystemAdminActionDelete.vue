<script setup lang="ts">
  import { message } from 'ant-design-vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

  import { injectSystemAdminContext } from '@/components/system-config/admin/SystemAdminConfigPage.vue'

  import IconTrash from '~icons/lucide/trash-2'
  import IconLoading from '~icons/lucide/loader-circle'

  const props = defineProps<{ rawIndex: number }>()

  const { rawStaff } = injectSystemAdminContext()

  const rawData = computed(() => rawStaff.value[props.rawIndex])

  const queryClient = useQueryClient()
  const { mutate: deleteAdmin, status } = useMutation({
    mutationFn() {
      return baseFetcher('/admin-setting/', {
        method: 'patch',
        body: {
          is_admin: false,
          usernames: [rawData.value.username],
        },
      })
    },
    onSuccess() {
      return queryClient.invalidateQueries({ queryKey: ['system', 'admins'] })
    },
    onError(error) {
      message.error(error.message || '删除失败')
    },
  })
</script>

<template>
  <APopconfirm
    title="确定删除该管理员吗？"
    @confirm="deleteAdmin()"
    :ok-button-props="{ danger: true }"
  >
    <template #icon>
      <ExclamationCircleOutlined />
    </template>

    <AButton
      type="text"
      size="small"
      class="icon-a-button"
    >
      <IconLoading
        v-if="status === 'pending'"
        class="animate-spin"
      />
      <IconTrash v-else />
    </AButton>
  </APopconfirm>
</template>
