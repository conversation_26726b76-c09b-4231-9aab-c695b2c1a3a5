<script lang="ts">
  import type { ColumnType } from 'ant-design-vue/es/table'

  export type TableItem = {
    id: string
    name: string
    employeeId: string
    rawIndex: number
  }

  export const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '工号',
      dataIndex: 'employeeId',
      key: 'employeeId',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      align: 'center',
    },
  ] as const satisfies ColumnType<TableItem>[]
</script>

<script setup lang="ts">
  import { injectSystemAdminContext } from '../SystemAdminConfigPage.vue'

  const page = defineModel<number>('page', { default: 1 })

  const props = defineProps<{
    admins: TableItem[]
    total: number
    loading?: boolean
  }>()

  const { selectedStaff, setSelectedStaff } = injectSystemAdminContext()
</script>

<template>
  <ATable
    :data-source="props.admins"
    :columns="columns"
    :class="[
      'reset-ant-table-pagination',
      'rounded-[5px] bg-white pb-2.5 [&_.ant-pagination]:px-5! [&_.ant-table-content]:px-5!',
      '[&_.ant-table-thead_.ant-table-cell]:bg-white! [&_.ant-table-thead_.ant-table-cell]:before:hidden',
      '[&_.ant-table-expanded-row-fixed]:max-w-full!',
    ]"
    :row-selection="{
      selectedRowKeys: selectedStaff,
      onChange(selectedRowKeys) {
        setSelectedStaff(selectedRowKeys as string[])
      },
    }"
    :pagination="{
      size: 'small',
      showSizeChanger: false,
      current: page,
      total: props.total,
      onChange(pageVal) {
        page = pageVal
      },
    }"
    :row-key="(record) => record.employeeId"
    :loading="props.loading"
  >
    <template
      #bodyCell="//@ts-expect-error antdv poor typing
      { column, record }: { record: TableItem; column: (typeof columns)[number] }"
    >
      <div v-if="column.key === 'action'">
        <SystemAdminActionDelete
          :raw-index="record.rawIndex"
          class="-ml-1!"
        />
      </div>
    </template>
  </ATable>
</template>
