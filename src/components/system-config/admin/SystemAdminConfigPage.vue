<script lang="ts">
  import type { ShallowRef } from 'vue'

  type AdminId = string

  const systemAdminContextInjectionKey = Symbol('system-admin') as InjectionKey<{
    selectedStaff: Readonly<ShallowRef<AdminId[]>>
    setSelectedStaff(value: AdminId[]): void

    rawStaff: Readonly<ShallowRef<API.SystemConfig.Staff[]>>
  }>

  export function injectSystemAdminContext() {
    const context = inject(systemAdminContextInjectionKey)
    if (!context) {
      throw new Error('systemAdminContext is not provided')
    }
    return context
  }
</script>

<script setup lang="ts">
  import { Modal, message } from 'ant-design-vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { h } from 'vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  import { systemConfigQueryKey } from '@/queries/system-config'
  import type { TableItem } from './table/SystemAdminConfigTable.vue'

  import IconSearch from '~icons/ant-design/search-outlined'
  import IconTrash from '~icons/lucide/trash-2'
  import IconLoading from '~icons/lucide/loader-circle'

  const page = ref(1)
  const pageSize = 10
  const searchString = ref('')
  const searchStringDebounced = refDebounced(searchString, 200)
  watch(
    searchStringDebounced,
    () => {
      page.value = 1
    },
    { flush: 'sync' },
  )

  const { data, fetchStatus } = useQuery({
    queryKey: systemConfigQueryKey.adminList({ page, pageSize, search: searchStringDebounced }),
    async queryFn() {
      const { data } = await baseFetcher<{
        data: {
          total: number
          data: API.SystemConfig.Staff[]
        }
      }>('/admin-setting/', {
        query: {
          page: page.value,
          page_size: pageSize,
          search: searchStringDebounced.value,
          is_admin: true,
        },
      })

      return data
    },
    placeholderData(oldData) {
      return oldData ?? { total: 0, data: [] }
    },
  })

  const total = computed(() => data.value?.total ?? 0)
  const rawStaff = computed(() => data.value?.data ?? [])

  const adminsTransformed = computed<TableItem[]>(
    () =>
      rawStaff.value?.map<TableItem>((admin, index) => ({
        id: admin.id,
        name: admin.first_name,
        employeeId: admin.username,
        rawIndex: index,
      })) ?? [],
  )

  const selectedStaff = ref<string[]>([])
  provide(systemAdminContextInjectionKey, {
    selectedStaff: shallowReadonly(selectedStaff),
    setSelectedStaff(value) {
      selectedStaff.value = value
    },

    rawStaff: computed(() => rawStaff.value ?? []),
  })

  const queryClient = useQueryClient()
  const { mutate: batchDeleteAdmins, status: batchDeleteStatus } = useMutation({
    mutationFn() {
      return baseFetcher('/admin-setting/', {
        method: 'patch',
        body: {
          is_admin: false,
          usernames: selectedStaff.value,
        },
      })
    },
    onSuccess() {
      message.success('批量删除成功')
      selectedStaff.value = []
      return queryClient.invalidateQueries({ queryKey: ['system', 'admins'] })
    },
    onError(error) {
      message.error(error.message || '批量删除失败')
    },
  })

  function confirmBatchDelete() {
    Modal.confirm({
      icon: h(ExclamationCircleOutlined),
      content: `确定要删除选中的 ${selectedStaff.value.length} 个管理员吗？`,
      okButtonProps: {
        danger: true,
      },
      centered: true,
      cancelText: '取消',
      onOk() {
        batchDeleteAdmins()
      },
      wrapClassName: 'reset-ant-modal',
    })
  }

  const isCreateModalOpen = ref(false)
  const mutationStatus = ref<MutationStatus>('idle')
  watchEffect(() => {
    if (mutationStatus.value === 'success') {
      isCreateModalOpen.value = false
    }
  })
</script>

<template>
  <div>
    <!-- 搜索和操作栏 -->
    <div class="flex flex-wrap items-center gap-y-4 px-(--page-px) pt-5">
      <!-- 搜索框 -->
      <AInput
        v-model:value="searchString"
        placeholder="输入教师姓名或工号搜索"
        class="max-w-[300px] rounded-full!"
        allow-clear
      >
        <template #suffix>
          <IconSearch class="text-foreground-4" />
        </template>
      </AInput>

      <div class="text-foreground-3 ml-4">
        共有
        <span class="text-primary">{{ total }}</span>
        个筛选结果
      </div>

      <!-- 批量操作 -->
      <AButton
        :disabled="selectedStaff.length === 0"
        class="outline-a-button mr-4 ml-auto flex! items-center space-x-2"
        type="primary"
        ghost
        @click="confirmBatchDelete"
      >
        <IconLoading
          v-if="batchDeleteStatus === 'pending'"
          class="animate-spin"
        />
        <IconTrash v-else />
        删除
      </AButton>

      <AButton
        type="primary"
        class="gradient-a-button w-20!"
        @click="isCreateModalOpen = true"
      >
        添加用户
      </AButton>
    </div>

    <!-- 表格 -->
    <div class="px-(--page-px) py-5">
      <SystemAdminConfigTable
        :admins="adminsTransformed"
        v-model:page="page"
        :total="total"
        :loading="fetchStatus === 'fetching'"
      />
    </div>

    <AModal
      v-model:open="isCreateModalOpen"
      title="手动导入用户"
      width="600px"
      centered
      :ok-button-props="{
        htmlType: 'submit',
        // @ts-expect-error antdv poor typing
        form: 'create-admin-form',
        loading: mutationStatus === 'pending',
      }"
      wrap-class-name="reset-ant-modal"
      destroy-on-close
    >
      <SystemAdminCreateForm
        @update:status="mutationStatus = $event"
        id="create-admin-form"
      />
    </AModal>
  </div>
</template>
