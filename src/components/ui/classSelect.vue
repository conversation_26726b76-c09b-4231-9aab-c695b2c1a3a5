<script lang="ts" setup>
import { courseClassList } from '@/services/api/courseClass'
import { getCourse } from '@/utils/auth'
const classOption = ref([{}])
const valueCalss = ref()
const emit = defineEmits(['update:value']);
async function getClassList() {
    const params = {
        course_semester: getCourse().course_semester_id,
        page: '',
        page_size: '99999'
    }
    try {
        const res = await courseClassList(params)
        classOption.value = [{
            value:'',
            label:'全部'
        }]
        res.data.results.forEach((item: any) => {
            classOption.value.push({
                value: item.id,
                label: item.name
            })
        })
        console.log(classOption.value,'ss')
    } catch (error) {
        console.error('获取作业学情分析列表失败:', error)
    }
}
onMounted(() => {
    getClassList()
})

const handleChange = (e: any) => {
    emit('update:value', e)
}

</script>
<template>
    <a-select v-model:value="valueCalss" :options="classOption" class="w-[100px]!"
        placeholder="班级选择" allowClear @change="handleChange" style=""></a-select>
</template>
<style scoped lang="scss">
:deep(.ant-select-selector){
    border-radius: 150px !important;
}
</style>