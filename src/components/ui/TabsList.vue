<script setup lang="ts">
  import { TabsList as Reka<PERSON><PERSON><PERSON><PERSON><PERSON>, Tabs<PERSON>rigger, injectTabsRootContext } from 'reka-ui'

  export type TabItem = {
    label: string
    value: string
  }

  export type Props = {
    items: TabItem[]
    /**
     * 每个 tab 的宽度，单位 px
     */
    itemWidth: number
  }

  const props = defineProps<Props>()
  const tabCSSWidth = computed(() => props.itemWidth + 'px')

  const { modelValue: activeTab } = injectTabsRootContext()

  const containerWidth = computed(() => props.items.length * props.itemWidth)

  const activeIndex = computed(() =>
    props.items.findIndex((item) => item.value === activeTab.value),
  )

  const clipLeft = computed(() => activeIndex.value * props.itemWidth)
  const clipRight = computed(() => (activeIndex.value + 1) * props.itemWidth)

  const clipPath = computed(() => {
    return `inset(0 ${Math.trunc(containerWidth.value - clipRight.value)}px 0 ${Math.trunc(clipLeft.value)}px round 50px)`
  })
</script>

<template>
  <RekaTabsList class="relative flex rounded-[50px] bg-[#3F8CFF26]">
    <TabsTrigger
      v-for="item in props.items"
      :key="item.value"
      :value="item.value"
      as-child
      class="tabs-button"
    >
      <button class="tabs-button">
        {{ item.label }}
      </button>
    </TabsTrigger>

    <div
      class="absolute inset-0 z-10 flex transition-[clip-path] duration-400 ease-out"
      :style="{
        'clip-path': clipPath,
      }"
    >
      <TabsTrigger
        v-for="item in props.items"
        :key="item.value"
        :value="item.value"
        class="tabs-button overlay-button"
      >
        {{ item.label }}
      </TabsTrigger>
    </div>
  </RekaTabsList>
</template>

<style scoped>
  .tabs-button {
    padding-block: 4px;
    cursor: pointer;
    color: var(--color-primary);
    text-align: center;
    width: v-bind(tabCSSWidth);

    &.overlay-button {
      color: white;
      font-weight: bold;

      &:nth-child(odd) {
        background: linear-gradient(135deg, #219fffff 0%, #0066ffff 100%);
      }
      &:nth-child(even) {
        background: linear-gradient(45deg, #0066ffff 0%, #219fffff 100%);
      }
    }
  }
</style>
