<template>
  <div class="dropdown-panel">
    <a-input-search v-model:value="searchValue" size="small" style="width: 163px;" @search="onSearch" />
    <div>
      <a-checkbox v-model:checked="checkedAll" style="margin-top: 12px;">全选</a-checkbox>
    </div>
    <a-tree style="overflow: hidden;" v-model:checkedKeys="checkedKeys" checkable :tree-data="treeData">
    </a-tree>
    <div v-if="treeData.length == 0" class="flex justify-center items-center">
      <img src="@/assets/image/zanwu/zanwuIcon.png" class="w-[250px]"/>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
interface DataNode {
  title: string
  key: string
  children?: DataNode[]
}
const props = defineProps({
  treeData: { type: Array as () => any[], required: true },
})
const emit = defineEmits(['selectedChildIds','selectedChange'])

const searchValue = ref('')
const checkedAll = ref(false)
const onSearch = (searchValue: any) => {
  emit('selectedChange', searchValue)
}


const checkedKeys = ref<string[]>([]);

watch(checkedKeys, () => {
  const selectedChildIds = getSelectedChildIds(props.treeData, checkedKeys.value);
  console.log('选中的子节点', selectedChildIds,checkedKeys.value);
  const data = {
    selectedChildIds:selectedChildIds,
    checkedKeys:checkedKeys.value
  }
  emit('selectedChildIds',data );
});

// 提取所有 children 节点的 key
function extractChildKeys(data: DataNode[]): string[] {
  let childKeys: string[] = [];

  data.forEach((node) => {
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        childKeys.push(child.key);
        // 可以递归继续查找更深层子节点
      });
    }
  });

  return childKeys;
}

// 获取选中的子节点 id
function getSelectedChildIds(treeData: DataNode[], checkedKeys: string[]): string[] {
  const childKeys = extractChildKeys(treeData);
  return checkedKeys.filter(key => childKeys.includes(key));
}

watch(checkedAll, (newValue) => {
  console.log('newValue', newValue, props.treeData);
  if (newValue) {
    checkedKeys.value = extractKeys(props.treeData);
  } else {
    checkedKeys.value = [];
  }

});


//获取全部的key
function extractKeys(data: any, result: any[] = []) {
  data.forEach((item: any) => {
    result.push(item.key); // 添加当前节点的key
    if (item.children) {
      extractKeys(item.children, result); // 递归处理子节点
    }
  });
  return result;
}




</script>

<style scoped lang="scss">
:deep(.ant-input) {
  height: 27px !important;
}

:deep(.ant-btn-sm) {
  height: 27px !important;
}
:deep(.ant-tree-title){
      overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
}

.dropdown-panel {
  padding: 10px;
  background: white;
  border-radius: 5px;
  height: 294px;
  margin-bottom: 14px;
  border: 1px solid rgba(229, 229, 229, 1);
  margin-top: 15px;
  overflow-y: scroll;
  overflow-x: hidden;
}

/* 整个滚动条 */
.dropdown-panel::-webkit-scrollbar {
  width: 4px;
  /* 滚动条宽度 */
  height: 27px;
}

/* 滚动条轨道 */
.dropdown-panel::-webkit-scrollbar-track {
  background: #fff;
  /* 轨道颜色 */
  border-radius: 4px;
}

/* 滚动条滑块 */
.dropdown-panel::-webkit-scrollbar-thumb {
  background: #C7DDFC;
  /* 滑块颜色 */
  border-radius: 4px;
}


.tree-container {
  clip-path: inset(0px round 10px);
  /* 这里的值应与父容器的 border-radius 一致 */
}
</style>
