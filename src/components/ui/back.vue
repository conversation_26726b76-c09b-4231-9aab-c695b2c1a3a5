<template>
    <div class="back-btn" @click="toback">
        <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
        <span style="font-size: 14px;line-height: 14px;">返回</span>
    </div>
</template>
<script setup lang="ts">
import { defineProps } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const props = defineProps({
    url: {
        type: String,
        default: ''
    }
})
function toback() {
    if(props.url == '-1'){
        router.back()
        return
    }
    router.push(props.url)
}

</script>
<style scoped lang="scss">
.back-btn {

    cursor: pointer;
    width: 60px;
    padding-top: 50px;
    color: #333;
    display: flex;
    align-items: center;
}
</style>