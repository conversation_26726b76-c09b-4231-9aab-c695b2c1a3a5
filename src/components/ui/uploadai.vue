<script lang="ts" setup>
import { fileSizeCheck } from '@/utils/util'
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { ref } from 'vue';
import type { UploadProps } from 'ant-design-vue';

function getBase64(file: File) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}

const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

const fileList = ref<UploadProps['fileList']>([]);
const handleRemove: UploadProps['onRemove'] = file => {
    fileList.value = [];
    emits('deleteFile', null)
};

const beforeUpload: UploadProps['beforeUpload'] = file => {
    if (fileSizeCheck(file, 10 * 1024 * 1024)) {
        message.error('文件大小不能超过10M');
        return
    }
    fileList.value = [file];
    emits('beforeUploadgetfile', file)
    return false;
};
const emits = defineEmits(['beforeUploadgetfile', 'deleteFile'])
const props = defineProps({
    accept: {
        type: String,
        default: '.ppt,.pptx'
    }
})


const handleCancel = () => {
    previewVisible.value = false;
    previewTitle.value = '';
};
const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
        file.preview = (await getBase64(file.originFileObj)) as string;
    }
    previewImage.value = file.url || file.preview;
    previewVisible.value = true;
    previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
};
</script>
<style scoped lang="scss">

.custom-upload {
    :deep(.ant-upload) {
        width: 100%;
        display: block;
    }
}
.ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;

}
.upload-demo {
    border-radius: 5px;
    border: 1.5px solid #e6efff;
    padding: 20px 31px;
    &:hover {
        border: 2px dashed #3F8CFF;
    }
}
</style>
<template>
    <div class="upload-demo hover:cursor-pointer">
        <a-upload :file-list="fileList" :before-upload="beforeUpload" :max-count="1"
            @remove="handleRemove" :multiple="false" :accept="accept" class="custom-upload">
            <div class="flex items-center justify-center">
                <img
                    src="@/assets/image/quetion/ziliaoupload.png"
                    class=" w-[56px] h-[52px] " 
                />
            </div>
        </a-upload>
    </div>
</template>
