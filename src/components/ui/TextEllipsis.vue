<script setup lang="ts">
  import type { TooltipProps } from 'ant-design-vue'

  type Props = {
    text?: string
    lines: number

    tooltip?: TooltipProps
  }

  defineOptions({ inheritAttrs: false })

  const props = defineProps<Props>()

  const textRef = ref<HTMLElement>()
  const { style, isOverflowing } = useTextEllipsis(textRef, { lines: props.lines })

  const [DefineContent, ReuseContent] = createReusableTemplate()
</script>

<template>
  <DefineContent>
    <div
      ref="textRef"
      v-bind="$attrs"
      :style="style"
    >
      <slot>{{ text }}</slot>
    </div>
  </DefineContent>

  <ATooltip
    v-if="props.tooltip && isOverflowing"
    v-bind="props.tooltip"
  >
    <ReuseContent />
  </ATooltip>

  <ReuseContent v-else />
</template>
