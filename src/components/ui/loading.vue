<template>
    <div class="loader-56-1"></div>
</template>
<script setup lang="ts">
</script>
<style scoped>
@keyframes animloader56-1 {
  17% {
    border-bottom-right-radius: 3px;
  }
  25% {
    transform: translateY(9px) rotate(22.5deg);
  }
  50% {
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
    border-bottom-right-radius: 40px;
  }
  75% {
    transform: translateY(9px) rotate(67.5deg);
  }
  100% {
    transform: translateY(0) rotate(90deg);
  }
}

@keyframes animloader56-1s {
  0%,
  100% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.2, 1);
  }
}

.loader-56-1 {
  position: relative;
  width: 48px;
  height: 48px;

  /**
   * 阴影层
   */
  &::before {
    content: "";
    position: absolute;
    top: calc(100% + 15px);
    left: 0;
    width: 100%;
    height: 5px;
    border-radius: 50%;
    background: #5c97fc;
    opacity: 0.1;
    animation: animloader56-1s .5s linear infinite;
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 3px;
    background: #3580FD;
    animation: animloader56-1 .5s linear infinite;
  }
}
</style>