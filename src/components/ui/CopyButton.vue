<script setup lang="ts">
  import type { ButtonProps } from 'ant-design-vue'
  import type { HTMLAttributes } from 'vue'
  import { useClipboard } from '@vueuse/core'
  import { twMerge } from 'tailwind-merge'

  import IconCopy from '~icons/icon-park-outline/copy'
  import IconCheck from '~icons/lucide/check'

  const {
    type = 'text',
    size = 'small',
    class: className,
    ...props
  } = defineProps<ButtonProps & { source: string; class?: HTMLAttributes['class'] }>()

  const { copy } = useClipboard()

  const copiedState = ref(false)
  async function handleCopyClicked() {
    await copy(props.source)
    copiedState.value = true
    await new Promise((resolve) => setTimeout(resolve, 1500))
    copiedState.value = false
  }
</script>

<template>
  <AButton
    v-bind="props"
    :type="type"
    :size="size"
    :class="twMerge('group size-auto! overflow-y-hidden p-1!', className)"
    :data-copied="copiedState ? '' : undefined"
    @click="handleCopyClicked()"
  >
    <div class="relative size-4">
      <IconCopy
        class="transition-transform duration-200 ease-in-out group-data-copied:translate-y-[120%]"
      />
      <IconCheck
        class="absolute inset-0 translate-y-[-120%] transition-transform duration-200 ease-in-out group-data-copied:translate-y-0"
      />
    </div>
  </AButton>
</template>
