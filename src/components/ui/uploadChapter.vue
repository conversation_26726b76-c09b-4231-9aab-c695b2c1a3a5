<script lang="ts" setup>
import { message } from 'ant-design-vue';
import type { TreeProps } from 'ant-design-vue';
import IconSearch from '~icons/ant-design/search-outlined'
import uploadPPT from '@/components/ui/uploadai.vue'
const emit = defineEmits(['update:open', 'getpptFile']) // 传值
import { defineProps } from 'vue'
type ChapterNode = {
    key: string | number;
    title: string;
    children?: ChapterNode[];
};
const props = defineProps({
    open: {
        type: Boolean,
        default: false
    },
    modelTitle: {
        type: String,
        default: '上传PPT文件'
    },
    accept: {
        type: String,
        default: '.ppt,.pptx'
    },
    treeData: {
        type: Array as PropType<ChapterNode[]>,
        default: []
    },
    uploadloading: {
        type: Boolean,
        default: false
    },
})

const fileUrl = ref('')
const checkedAll = ref(false)
const selectedKeys = ref<string[]>([]);

const getfile = (file: any) => {
    fileUrl.value = file
}
const delFile = (file: any) => {
}

// 新增处理关闭事件
const handleClose = () => {
    emit('update:open', false)
}
const check = (e: any) => {
    console.log(e, 'eeeeeeee')
    selectedKeys.value = e
}

const ok = () => {
    if (!fileUrl.value) {
        message.error('请上传文件')
    }
    emit('getpptFile', {
        file: fileUrl.value,
        selectedKeys: selectedKeys.value
    })
}


const treeDataData = ref<TreeProps['treeData']>([]);
const searchValue = ref<string>('');
const defaultExpandAll = ref<boolean>(false);

const getParentKey = (
    key: string | number,
    tree: TreeProps['treeData'],
): string | number | undefined => {
    let parentKey;
    for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
            if (node.children.some(item => item.key === key)) {
                parentKey = node.key;
            } else if (getParentKey(key, node.children)) {
                parentKey = getParentKey(key, node.children);
            }
        }
    }
    console.log('parentKey', parentKey);
    return parentKey;
};
watch(searchValue, value => {
    const expanded = props.treeData.map((item: TreeProps['treeData'][number]) => {
    console.log(item.title.indexOf(value),'item.title.indexOf(value)')
        if (item.title.indexOf(value) > -1) {
            // return getParentKey(item.key, props.treeData);
            return item
        }
        return null;
    }).filter((item, i, self) => item && self.indexOf(item) === i);
    treeDataData.value = expanded;
    defaultExpandAll.value = true;
    searchValue.value = value;
});

watch(props.treeData, (value, oldvalue) => {
    treeDataData.value = JSON.parse(JSON.stringify(value));
},{deep: true,immediate: true})

watch(() => props.open, (value) =>{
    if(!value){
        selectedKeys.value = []
        fileUrl.value = ''
    }
},{deep: true,immediate: true})

</script>
<template>
    <a-modal :open="open" :title="modelTitle" maskClosable :footer="null" width="700px" @cancel="handleClose">
        <uploadPPT :accept="accept" @beforeUploadgetfile="getfile" @deleteFile="delFile" class="mt-[12px]" />
        <AInput class="mt-[20px]" placeholder="请输入一级章节查询..." v-model:value="searchValue" @change="">
            <template #suffix>
                <IconSearch class="text-foreground-4" @click="" />
            </template>
        </AInput>
        <div class="mt-[10px]">
            <a-checkbox v-model:checked="checkedAll">全选</a-checkbox>
        </div>
        <div class="max-h-[210px] overflow-y-auto">
            <a-tree checkable 
                :tree-data="treeDataData" 
                :defaultExpandAll="defaultExpandAll"
                v-model:selectedKeys="selectedKeys"
                @check="check"
            >
                <template #title="{ title }">
                    <span v-if="title.indexOf(searchValue) > -1">
                        {{ title.substring(0, title.indexOf(searchValue)) }}
                        <span style="color: #f50">{{ searchValue }}</span>
                        {{ title.substring(title.indexOf(searchValue) + searchValue.length) }}
                    </span>
                    <span v-else>{{ title }}</span>
                </template>
            </a-tree>
        </div>
        <div class="flex justify-end mt-[20px]">
            <a-space>
                <a-button @click="handleClose">取消</a-button>
                <a-button type="primary" :loading="uploadloading" @click="ok">确定</a-button>
            </a-space>
        </div>
    </a-modal>
</template>