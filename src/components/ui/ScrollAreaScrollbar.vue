<script setup lang="ts">
  import {
    ScrollAreaScrollbar as RekaScrollAreaScrollbar,
    ScrollAreaThumb,
    type ScrollAreaScrollbarProps,
  } from 'reka-ui'
  import type { HTMLAttributes } from 'vue'
  import { twMerge } from 'tailwind-merge'

  const { class: className, ...props } = defineProps<
    ScrollAreaScrollbarProps & { class?: HTMLAttributes['class'] }
  >()
</script>

<template>
  <RekaScrollAreaScrollbar
    v-bind="props"
    :class="
      twMerge(
        'z-20 flex touch-none rounded-lg select-none',
        props.orientation === 'vertical' && 'w-2.5 py-1.5 pr-0.5 pl-1',
        props.orientation === 'horizontal' && 'h-2.5 flex-col pt-1 pb-0.5',
        className,
      )
    "
  >
    <ScrollAreaThumb class="grow rounded-full bg-[#C7DDFC]" />
  </RekaScrollAreaScrollbar>
</template>
