<script lang="ts" setup>
import { PlusOutlined,InfoCircleOutlined } from '@ant-design/icons-vue';
import { ref, watch, onMounted } from 'vue';
import type { UploadProps } from 'ant-design-vue';
import { message } from 'ant-design-vue';

const props = defineProps({
    urlList: {
        type: String,
        required: true,
        default: () => ''
    },
    disable: {
        type: Boolean,
        default: false
    },
    accept: {
        type: String,
        default:'.ppt,.pptx'
    }
})

const fileList = ref<UploadProps['fileList']>([]);

watch(() => props.urlList, (newVal) => {
    if (newVal) {
        fileList.value = [{ url: newVal , uid: '', name: '', status: 'done'}]
    }else {
        fileList.value = []
    }
}, { immediate: true, deep: true })

watch(() => props.disable, (newVal) => {
    console.log(newVal,'disable')
}, { immediate: true, deep: true })

function getBase64(file: File) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}

const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');


const handleRemove: UploadProps['onRemove'] = file => {
    fileList.value = [];
    emits('deleteFile', null)
};

const beforeUpload: UploadProps['beforeUpload'] = file => {
    const isLt2M = file.size / 1024 / 1024 < 10;
    
    if(file.name.length > 100){
        message.error('文件名称不能超过100个字！');
        return
    }
    if (!isLt2M) {
        message.error('文件大小不能超过10M！');
        return
    }
    emits('beforeUploadgetfile', file)
    return false;
};
const emits = defineEmits(['beforeUploadgetfile', 'deleteFile'])


const handleCancel = () => {
    previewVisible.value = false;
    previewTitle.value = '';
};
const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
        file.preview = (await getBase64(file.originFileObj)) as string;
    }
    previewImage.value = file.url || file.preview;
    previewVisible.value = true;
    previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
};
</script>
<template>
    <div class="flex items-center flex-wrap gap-[15px] w-full" style="">
        <div class="md:w-full lg:w-[336px]">
            <a-upload 
                class="[&_[aria-label=eye]]:flex! [&_[aria-label=eye]]:items-center! [&_[aria-label=eye]_svg]:mt-0.5!"
                v-model:file-list="fileList" 
                list-type="picture-card" 
                :accept="accept" 
                :multiple="false"
                @preview="handlePreview" 
                @remove="handleRemove" 
                :before-upload="beforeUpload" 
                :disabled="disable">
                <!-- <div v-if="fileList.length < 1" class="ant-upload-text">
                    <plus-outlined />
                    <div style="margin-top: 8px">上传图片</div>
                </div> -->
            </a-upload>
        </div>
        <!-- <div class="md:w-full lg:flex-1" style="height: 53px;display: flex;font-size: 12px;line-height: 17.38px;">
            <div style="margin-right: 9px;margin-top: 2px;">
                <InfoCircleOutlined />
            </div>
            <div>
                支持jpg/jpeg/png格式单张图片不超过3M，
                <br/>
                课程封面图最佳尺寸336*180
            </div>
        </div>
        <a-modal :open="previewVisible" :footer="null" @cancel="handleCancel">
            <img alt="example" style="width: 100%" :src="previewImage" />
        </a-modal> -->
    </div>
</template>

<style scoped lang="scss">
:deep(.ant-upload-select-picture-card) {
    max-width: 336px !important;
    width: 100% !important;
    height: 180px !important;
    background-color: #fff !important;
}
:deep(.ant-upload-list-item-actions){
    display: flex;
    align-items: center;
    justify-content: center;
}

:deep(.ant-upload-list-item-container) {
    max-width: 336px !important;
    width: 100% !important;
    height: 180px !important;
    background-color: #fff !important;
}
@media (min-width: 768px) {
    :deep(.ant-upload-list-item-container) {
      max-width: 336px;
    }
  }

/* you can make up upload button and sample style by using stylesheets */
.ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;

    /* background-color: #fff; */
    /* width: 336px;
    height: 180px; */
}

</style>
