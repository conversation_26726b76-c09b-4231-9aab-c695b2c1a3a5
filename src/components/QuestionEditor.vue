<template>
    
    <div ref="divRef" id="aiEditor" style="height: 162px;width: 100%;">
        <div class="aie-container">
            <div class="aie-container-header"></div>
            <div class="aie-container-main"></div>
            <div class="aie-container-footer" style="display: none;"></div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { AiEditor } from "aieditor";
import "aieditor/dist/style.css"
import { onMounted, onUnmounted, ref, watch } from "vue";

const props = defineProps({
    indexNumber: {
        type:  [Number, String] ,
        required: false
    },
    questionValue: {
        type: String,
        required: false
    }
}); //当前编辑选项的索引

const divRef = ref();
let aiEditor: AiEditor | null = null;

const valueEditor = ref();
const emit = defineEmits(['valueEditorChange']);


onMounted(() => {
    initEditor();
    //当类型是编辑时，创建完编辑器后，通过markdown插入的方式给组件赋值
    if(aiEditor&& props.questionValue){
        aiEditor.insertMarkdown(props.questionValue);
    }    
})

watch(() => props.questionValue, (newValue, oldValue) => {
    // aiEditor.insertMarkdown(newValue);
    if(!newValue){
        aiEditor&&aiEditor.clear();
    }
});
// <div class="cell-1-2 com-cell-d"  v-html="parseMarkdown(question.text)"></div>
function initEditor() {
    aiEditor = new AiEditor({
        element: divRef.value as Element,
        htmlPasteConfig: {
            pasteAsText: false,
            pasteClean: false,
            pasteProcessor: (html) => {
                return html;
            }
        },
        placeholder: "请输入...",
        content:  '',
        draggable: false,
        toolbarKeys: ["undo", "redo", "brush", "eraser",
            "|", "heading", "font-family", "font-size",
            "|", "bold", "italic", "underline", "strike", 
            "|", "highlight", "font-color",
            "|", "align", "line-height",
            {
                toolbarKeys: ["link", "code", "subscript", "superscript", "hr", "todo", "emoji","source-code", "printer", "fullscreen", "image", "video",
                    "bullet-list", "ordered-list", "indent-decrease", "indent-increase", "break",
                    "attachment", "quote", "code-block", "table",]
            }
        ],
        textSelectionBubbleMenu: {
            enable: false, //不启用选中按钮
            items: ["ai", "Bold", "Italic", "Underline", "Strike", "code", "comment"],
        },
        onChange:(aiEditor)=>{
            // 监听到用编辑器内容发生变化了，控制台打印编辑器的 html 内容...
            // valueEditor.value = aiEditor.getHtml()
            valueEditor.value = aiEditor.getMarkdown()
            // console.log(valueEditor.value,'----->>>>')
            emit('valueEditorChange',{
                indexNumber: props.indexNumber,
                valueEditor: aiEditor.isEmpty()?'':valueEditor.value //
            })
        },
        onBlur:(aiEditor)=>{
            if (!valueEditor.value)  return
            
        }
    })
}

onUnmounted(() => {
    aiEditor && aiEditor.destroy();
    // console.log("AIEditor 被销毁了---....",props.indexNumber)
})

</script>

<style scoped> 
    .aie-container {
        border-radius: 6px;
        border: 1px solid #e2dcdc;
    }
    .aie-container-main{
        overflow: auto;
    }
</style>