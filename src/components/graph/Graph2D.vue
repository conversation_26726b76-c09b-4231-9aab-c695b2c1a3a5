<template>
    <div class="layout">
        <div ref="containerRef" class="graph-container"></div>
        <!-- <FunctionalArea style="width: 300px;" :selectedNode="isEditMode ? updadeData : null" :isEditing="isEditMode" :updateLine="updateLine" /> -->
        <!-- 右键菜单 -->
        <div ref="contextMenuRef" class="context-menu" v-show="isContextMenuVisible"
            :style="{ left: menuLeft + 'px', top: menuTop + 'px' }">
            <ul>
                <template v-if="rightClickedObject === 'node'">
                    <li @click="handleMenuClick('addNode')">
                        <img src="@/assets/image/delete.png" alt="新增"
                            style="width: 12px; height: 12px; margin-right: 4px;" />新增
                    </li>
                    <li @click="handleMenuClick('deleteNode')">
                        <img src="@/assets/image/delete.png" alt="删除"
                            style="width: 12px; height: 12px; margin-right: 4px;" />删除
                    </li>
                    <li @click="handleMenuClick('addLine')">
                        <img src="@/assets/image/arrow.png" alt="添加边"
                            style="width: 12px; height: 12px; margin-right: 4px;" />添加边
                    </li>
                    <li @click="handleMenuClick('updateNode')">
                        <img src="@/assets/image/update.png" alt="编辑节点"
                            style="width: 12px; height: 12px; margin-right: 4px;" />编辑节点
                    </li>
                </template>
                <template v-else-if="rightClickedObject === 'line'">
                    <li @click="handleMenuClick('updateLine')">
                        <img src="@/assets/image/update.png" alt="编辑"
                            style="width: 12px; height: 12px; margin-right: 4px;" />编辑
                    </li>
                    <li @click="handleMenuClick('deleteLine')">
                        <img src="@/assets/image/delete.png" alt="删除"
                            style="width: 12px; height: 12px; margin-right: 4px;" />删除
                    </li>
                </template>
            </ul>
        </div>

        <!-- 左键详情菜单 -->
        <div ref="detailsMenuRef" class="details-menu" v-show="isDetailsMenuVisible"
            :style="{ left: menuLeft + 'px', top: menuTop + 'px' }">
            <ul>
                <template v-if="leftClickedObject === 'node'">
                    <li style="font-weight: 700;"> 节点详情 </li>
                    <li> 名称: {{ currentClickedNode.properties.name || '无名称' }}</li>
                    <li> ID: {{ currentClickedNode.id || '无ID' }} </li>
                    <li> 标签: {{ currentClickedNode.label || '无标签' }} </li>
                    <li> 内容: {{ currentClickedNode.properties.content || '无内容' }} </li>
                </template>
                <template v-else-if="leftClickedObject === 'line'">
                    <li style="font-weight: 700;"> 连线详情 </li>
                    <li> ID: {{ currentClickedLine.id.match(/'relationId'\s*:\s*'([^']+)'/)[1] }} </li>
                    <li> 标签: {{ currentClickedLine.label }} </li>
                    <li> 起始节点: {{ currentClickedLine.source.properties.name }} </li>
                    <li> 目标节点: {{ currentClickedLine.target.properties.name }} </li>
                </template>
            </ul>
        </div>
    </div>
</template>

<script setup lang="ts">
import * as d3 from "d3";
import { ref, onMounted, onUnmounted, watch } from "vue";
import { useElementSize } from '@vueuse/core';
import FunctionalArea from './FunctionalArea.vue'
import { deleteGraphNode, addGraphEdge, deleteGraphEdge } from "@/services/api/graph";
import { message } from 'ant-design-vue';

const props = defineProps({
    nodes: {
        type: Array as () => any[],
        default: () => []
    },
    links: {
        type: Array as () => any[],
        default: () => []
    },
    getGraphList: {
        type: Function,
        required: true
    },
    taskID: {
        type: Number,
        required: true
    },
});

const emit = defineEmits(['deleteNode','addNode','updateNode','updateEdge']); // 定义删除节点事件


const containerRef = ref<HTMLElement | null>(null);
const contextMenuRef = ref<HTMLElement | null>(null);
const detailsMenuRef = ref<HTMLElement | null>(null);

const { width, height } = useElementSize(containerRef);
let simulation: d3.Simulation<d3.SimulationNodeDatum, undefined>;
const isContextMenuVisible = ref(false);
const isDetailsMenuVisible = ref(false);
const menuLeft = ref(0); //存储菜单位置
const menuTop = ref(0);//存储菜单位置
let currentClickedNode: any;
let currentClickedLine: any;
const rightClickedObject = ref<string | null>(null); //右键点击的对象标记
const leftClickedObject = ref<string | null>(null); //左键点击的对象标记
const isEditMode = ref(false); //表示编辑的状态
let updadeData: any//更新节点数据
let updateLine: any; //更新连线数据
const isAddingLine = ref(false) //添加连线状态
let tempLine: any; //临时虚线
let startNode: any; //起始节点
const onTargetNodeSelected = ref<((targetNode: any) => void) | null>(null);
let detailsElement: HTMLElement | null = null; //节点详情
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import { number } from "zod";


// 渲染2D图形的主函数
const render2DGraph = async (container: HTMLElement) => {
    if (!container) return;

    console.log('2D Graph Data:', props.nodes, props.links);

    // 清理现有的 SVG
    d3.select(container).selectAll("*").remove();

    // 使用 API 获取的数据
    const graphData = { nodes: props.nodes, links: props.links };
    console.log('2D Graph Data:', graphData);
    if (!graphData || !graphData.nodes.length) {
        return
    };

    // 使用固定的尺寸
    const containerWidth = 800;
    const containerHeight = 600;

    const svg = d3
        .select(container)
        .append("svg")
        .attr("width", "100%")
        .attr("height", "100%")
        .attr("viewBox", `0 0 ${containerWidth} ${containerHeight}`)
        .attr("preserveAspectRatio", "xMidYMid meet");

    // 添加缩放和平移功能
    const zoom = d3
        .zoom()
        .scaleExtent([0.1, 4])
        .on("zoom", (event) => {
            g.attr("transform", event.transform);
        });

    svg.call(zoom as any);

    // 创建一个包含所有元素的组
    const g = svg.append("g");

    // 创建力导向图
    simulation = d3
        .forceSimulation(graphData.nodes as any)
        .force(
            "link",
            d3
                .forceLink(graphData.links)
                .id((d: any) => d.id.toString())
                .distance(150)
        )
        .force("charge", d3.forceManyBody().strength(-100))  //设置节点间的排斥力
        .force("center", d3.forceCenter(containerWidth/1.5, containerHeight / 1.5)) //设置所有节点向中心点
        .force("collision", d3.forceCollide().radius(40)); //设置节点间的碰撞检测

    // 定义箭头 marker
    svg.append("defs")
    .append("marker")
    .attr("id", "arrow") // marker ID
    .attr("viewBox", "0 -10 20 20") // viewBox 范围
    .attr("refX", 69) // 箭头坐标位置，决定箭头与线条的相对位置
    .attr("refY", 0)
    .attr("markerWidth", 10) // marker 宽高，决定箭头大小
    .attr("markerHeight", 10)
    .attr("orient", "auto") // 自动旋转方向
    .append("path")
    .attr("d", "M0,-10L10,0L0,10") // path 绘制箭头形状
    .attr("fill", "#CACBCC"); // 箭头颜色

    // 定义箭头 marker
    svg.append("defs")
    .append("marker")
    .attr("id", "arrow_selectLine") // marker ID
    .attr("viewBox", "0 -10 20 20") // viewBox 范围
    .attr("refX", 69) // 箭头坐标位置，决定箭头与线条的相对位置
    .attr("refY", 0)
    .attr("markerWidth", 10) // marker 宽高，决定箭头大小
    .attr("markerHeight", 10)
    .attr("orient", "auto") // 自动旋转方向
    .append("path")
    .attr("d", "M0,-10L10,0L0,10") // path 绘制箭头形状
    .attr("fill", "#2693FF"); // 箭头颜色

    // 创建 linksGroup
    const linksGroup = g.append("g")
        .attr("class", "links-group");

    // 绘制连接线
    const link = linksGroup
        .append("g")
        .attr("class", "link-lines")
        .selectAll("line")
        .data(graphData.links, (d: any) => d.id) // 使用唯一 id 作为 key
        .enter()
        .append("line")
        .attr("stroke", "#CACBCC")
        .attr("stroke-opacity", 1)
        .attr("stroke-width", 1)
        .attr("marker-end", "url(#arrow)")
        .on("click", function(event, d) {
            // 阻止事件冒泡（如果需要）
            event.stopPropagation();
            highlightLink(d.id);
            highlightLinkNode(d);
        });

    // 绘制连接线标签 group
    const linkLabels = linksGroup
        .append("g")
        .attr("class", "link-labels")
        .selectAll(".link-label-group")
        .data(graphData.links, (d: any) => d.id) // 同样使用 id
        .enter()
        .append("g")
        .attr("class", "link-label-group")
        .on("click", function(event, d) {
            event.stopPropagation();
            highlightLink(d.id);
            highlightLinkNode(d);
        });

    // 添加背景 rect
    linkLabels.append("rect")
        .attr("class", "link-labelBg")
        .attr("rx", 10)
        .attr("fill", "#F0F9FF")
        .attr("stroke", "none")
        .attr("stroke-width", 0);

    // 添加文本
    linkLabels.append("text")
        .attr("class", "link-label")
        .attr("fill", "black")
        .style("font-size", "14px")
        .style("text-anchor", "middle")
        .style("dominant-baseline", "central")
        .text(d => d.label);

    // 删除连线图标
    const icon_deleteLine = linkLabels.append("g")
    .attr("class", "icon-line")
    .style("display", "none");

    icon_deleteLine.append("image")
    .attr("class", "icon-deleteLine")
    .attr("xlink:href", "/graph/delete.svg")
    .attr("x", 25)
    .attr("y", -25)
    .attr("width", 14)
    .attr("height", 14);

    icon_deleteLine.append("text")
    .attr("class", "iconText-deleteLine")
    .attr("x", 45)  // 图标右边再偏移
    .attr("y", -13)   // 与图标垂直居中
    .attr("fill", "#666666")
    .style("font-size", "14px")
    .style("font-weight", "500")
    .style("pointer-events", "none")  // 不抢焦点
    .style("opacity", 0)  // 初始隐藏
    .text("删除");

    icon_deleteLine.select("image")
    .on("mouseover", function () {
        const parent = (this as SVGElement).parentNode as SVGGElement;
        d3.select(parent)
        .select("text")
        .transition()
        .duration(200)
        .style("opacity", 1);
    })
    .on("mouseout", function () {
        const parent = (this as SVGElement).parentNode as SVGGElement;
        d3.select(parent)
        .select("text")
        .transition()
        .duration(200)
        .style("opacity", 0);
    });


    // 编辑连线图标
    const icon_updateLine = linkLabels.append("g")
    .attr("class", "icon-line")
    .style("display", "none");

    icon_updateLine.append("image")
    .attr("class", "icon-updateLine")
    .attr("xlink:href", "/graph/updateLine.svg")
    .attr("x", 26)
    .attr("y", 12)
    .attr("width", 14)
    .attr("height", 14);

    icon_updateLine.append("text")
    .attr("class", "iconText-updateLine")
    .attr("x", 45)  // 图标右边再偏移
    .attr("y", 24)   // 与图标垂直居中
    .attr("fill", "#666666")
    .style("font-size", "14px")
    .style("font-weight", "500")
    .style("pointer-events", "none")  // 不抢焦点
    .style("opacity", 0)  // 初始隐藏
    .text("删除");

    icon_updateLine.select("image")
    .on("mouseover", function () {
        const parent = (this as SVGElement).parentNode as SVGGElement;
        d3.select(parent)
        .select("text")
        .transition()
        .duration(200)
        .style("opacity", 1);
    })
    .on("mouseout", function () {
        const parent = (this as SVGElement).parentNode as SVGGElement;
        d3.select(parent)
        .select("text")
        .transition()
        .duration(200)
        .style("opacity", 0);
    });

    // 高亮函数
    function highlightLink(id:any) {
        // 先清除所有高亮
        clearHighlight();

        // 重置所有节点和连接线的样式
        node.select("#circle")
            .attr("r", 30)
            .style("opacity", 0.1); // 将未选中的节点变灰
        node.select("#circleBg")
            .attr("r", 30)
            .style("opacity", 0.1); // 将未选中的节点变灰
        node.select("text")
            .style("font-size", "14px")
            .style("opacity", 0.1); // 将未选中的文本变灰
        node.selectAll(".icon-node")
            .style("display", "none"); // 隐藏节点图标
        link
            .attr("stroke", "#CACBCC")
            .style("opacity", 0.1); // 将未选中的连接线变灰
        linkLabels.select("text")
            .style("opacity", 0.1); // 将未选中的文本变灰
        linkLabels.selectAll(".icon-line")
            .style("display", "none"); // 隐藏图标

        // 高亮对应的 line
        linksGroup.selectAll("line")
            .filter((d:any) => d.id === id)
            .attr("stroke", "#2693FF")
            .attr("marker-end", "url(#arrow_selectLine)")
            .style("opacity", 1);

        // 高亮对应的 label rect
        linksGroup.selectAll(".link-label-group")
            .filter((d:any) => d.id === id)
            .select("rect")
            .attr("rx", 1)
            .attr("stroke", "#B8D2FF")      // 设置边框颜色
            .attr("stroke-width", 1)   // 设置边框宽度（可根据需求调整）
            .style("opacity", 1);

        // 高亮对应的 label text
        linksGroup.selectAll(".link-label-group")
            .filter((d:any) => d.id === id)
            .select("text")
            .style("opacity", 1);

        // 显示图标
        linksGroup.selectAll(".link-label-group")
            .filter((d:any) => d.id === id)
            .selectAll(".icon-line")
            .style("display", "block"); // 隐藏图标
    }

    function highlightLinkNode(d:any) {
        // 高亮与被点击线相连的所有节点
        node.filter((n: any) => n.id.toString() === d.source.id.toString() || n.id.toString() === d.target.id.toString())
            .select("#circle")
            .attr("r", 30)
            .style("opacity", 1);
        node.filter((n: any) => n.id.toString() === d.source.id.toString() || n.id.toString() === d.target.id.toString())
            .select("#circleBg")
            .attr("r", 30)
            .style("opacity", 1);
        node.filter((n: any) => n.id.toString() === d.source.id.toString() || n.id.toString() === d.target.id.toString())
            .select("text")
            .style("opacity", 1);
    }
    // 清除高亮
    function clearHighlight() {
        // 恢复所有 line
        linksGroup.selectAll("line")
            .attr("stroke", "#CACBCC")
            .attr("stroke-width", 1);

        // 恢复所有 label rect
        linksGroup.selectAll(".link-labelBg")
            .attr("rx", 10)
            .attr("fill", "#F0F9FF")
            .attr("stroke", "none")
            .attr("stroke-width", 0);
    }

    // // 绘制连接线
    // const link = g
    //     .append("g")
    //     .selectAll("line")
    //     .data(graphData.links)
    //     .enter()
    //     .append("line")
    //     .attr("stroke", "#CACBCC")
    //     .attr("stroke-opacity", 1)
    //     .attr("stroke-width", 1)
    //     .attr("marker-end", "url(#arrow)") // 设置箭头
    //     //添加线点击事件使线能够被选中
    //     .on("click", function (event: MouseEvent, d) {
    //         //重置所有线的样式
    //         link.attr("stroke", "#CACBCC")
    //             .attr("stroke-width", 1)
    //         //选中当前点击的线并更换样式
    //         const clickedLine = d3.select(this);
    //         clickedLine
    //             .attr("stroke", "#2693FF")
    //             .attr("stroke-width", 1);
            
    //             showDetails(event, d,'line');

    //         //console.log('被点击线的信息', d)
    //     })

    // const linkLabels = g.append("g")
    //     .selectAll(".link-label-group")
    //     .data(graphData.links)
    //     .enter()
    //     .append("g")
    //     .attr("class", "link-label-group");

    // // 添加背景 rect
    // linkLabels.append("rect")
    //     .attr("class", "link-labelBg")
    //     .attr("rx", 10)
    //     .attr("fill", "#F0F9FF");

    // // 添加文本
    // linkLabels.append("text")
    //     .attr("class", "link-label")
    //     .attr("fill", "black")
    //     .style("font-size", "14px")
    //     .style("text-anchor", "middle")
    //     .style("dominant-baseline", "central")
    //     .text(d => d.label); // 替换为实际字段

    // 根据 text 尺寸更新 rect 和icon 的位置和大小
    linkLabels.each(function() {
        const group = d3.select(this);
        const text = group.select("text");
        const rect = group.select("rect");
        const deleteLine = group.select(".icon-deleteLine");
        const deleteLineText = group.select(".iconText-deleteLine");
        const updateLine = group.select(".icon-updateLine");
        const updateLineText = group.select(".iconText-updateLine");

        const textNode = text.node() as SVGGraphicsElement | null;
        if (textNode) {
            const bbox = textNode.getBBox();
            rect
            .attr("x", bbox.x - 1)
            .attr("y", bbox.y - 0.5)
            .attr("width", bbox.width + 2)
            .attr("height", bbox.height + 1);

            deleteLine
            .attr("x", bbox.x + bbox.width + 10)
            .attr("y", bbox.y - bbox.height + 5);
            deleteLineText
            .attr("x", bbox.x + bbox.width + 30)
            .attr("y", bbox.y - bbox.height + 17);

            updateLine
            .attr("x", bbox.x + bbox.width + 10)
            .attr("y", bbox.y + bbox.height);
            updateLineText
            .attr("x", bbox.x + bbox.width + 30)
            .attr("y", bbox.y + bbox.height + 12);

        }
    });


    // 更新力导向图
    simulation.on("tick", () => {
        link
            .attr("x1", (d: any) => d.source.x)
            .attr("y1", (d: any) => d.source.y)
            .attr("x2", (d: any) => d.target.x)
            .attr("y2", (d: any) => d.target.y);

        node.attr("transform", (d: any) => `translate(${d.x},${d.y})`);

        linkLabels
        .attr("transform", (d: any) => {
            const x = (d.source.x + d.target.x) / 2;
            const y = (d.source.y + d.target.y) / 2;
            return `translate(${x},${y})`;
        });

        // linkLabels.select("text")
        // .text((d: any) => d.label);

        // // 更新线的 label 位置
        // linkLabels
        //   .attr("x", (d: any) => (d.source.x + d.target.x) / 2)
        //   .attr("y", (d: any) => (d.source.y + d.target.y) / 2)
        //   .text((d: any) => d.label );

        // linkLabelsBg
        //     .attr("x", (d: any) => (d.source.x + d.target.x) / 2 - 15)
        //     .attr("y", (d: any) => (d.source.y + d.target.y) / 2 - 10)
    });


    // 创建节点组
    const node = g
        .append("g")
        .selectAll(".node")
        .data(graphData.nodes)
        .enter()
        .append("g")
        .attr("class", "node")
        .call(drag(simulation) as any);

    // 创建颜色生成函数
    const getNodeColor = d3.scaleOrdinal(d3.schemeCategory10);

    // 定义一个生成随机 RGB 颜色的函数
    function getRandomColor() {
        const r = Math.floor(Math.random() * 256);
        const g = Math.floor(Math.random() * 256);
        const b = Math.floor(Math.random() * 256);
        return { r, g, b };
    }

    // 定义五种固定颜色
    const fixedColors = [
    { r: 255, g: 0, b: 0 },       // 红
    { r: 255, g: 200, b: 0 },     // 橙
    { r: 0, g: 255, b: 85 },      // 绿
    { r: 0, g: 94, b: 255 },      // 蓝
    { r: 132, g: 0, b: 255 },     // 紫
    ];

    // 定义一个从上述数组中随机选择的函数
    function getRandomFixedColor() {
        const index = Math.floor(Math.random() * fixedColors.length);
        return fixedColors[index];
    }

    // 创建 defs 容器
    const defs = svg.append("defs");

    // 为每个节点生成独立的渐变
    node.each(function(d, i) {
        const { r, g, b } = getRandomFixedColor();
        d.rgbColor = { r, g, b }; // 保存颜色到节点数据
        const gradientId = `gradient-${d.id}`;

        // 定义径向渐变
        const radialGradient = defs.append("radialGradient")
            .attr("id", gradientId)
            .attr("cx", "50%")
            .attr("cy", "50%")
            .attr("r", "50%")
            .attr("fx", "50%")
            .attr("fy", "50%");

        // 渐变起点：不透明色
        radialGradient.append("stop")
            .attr("offset", "0%")
            .attr("stop-color", `rgba(${r},${g},${b},1)`);

        // 渐变终点：同色但 alpha 0.3
        radialGradient.append("stop")
            .attr("offset", "100%")
            .attr("stop-color", `rgba(${r},${g},${b},0.3)`);
    });

    // 先绘制纯色背景圆
    node.append("circle")
        .attr("id", "circleBg")
        .attr("r", 30)
        .attr("fill", "#F0F9FF")

    // 绘制节点圆
    node
        .append("circle")
        .attr("id", "circle")
        .attr("r", 30)
        .attr("fill", (d) => `url(#gradient-${d.id})`) // 使用颜色生成函数设置节点颜色
        // .attr("stroke", "#fff")
        // .attr("stroke-width", 2);

    // 添加一个 group 包裹背景和文字
    const labelGroup = node.append("g")
    .attr("class", "textGroup")
    .attr("transform", "translate(0, -50)"); // 位置调整到节点正上方（根据需求微调）

    // 添加 rect 作为文字背景
    labelGroup.append("rect")
    .attr("x", -30) // 先给定默认宽度，后续用 text 宽度更新
    .attr("y", -10)
    .attr("width", 60)
    .attr("height", 20)
    .attr("rx", 10) // 圆角矩形
    .attr("fill", (d) => `rgba(${d.rgbColor.r},${d.rgbColor.g},${d.rgbColor.b},0.2)`);

    // 添加 text
    labelGroup.append("text")
    .text((d: any) => d.properties.name)
    .attr("dy", ".35em")
    .attr("text-anchor", "middle")
    // .attr("dominant-baseline", "middle")
    .attr("fill", "black")
    .style("font-size", "14px")
    .style("font-weight", "400");

    // 计算 text 宽度并更新 rect 尺寸
    labelGroup.each(function() {
        const g = d3.select(this);
        const text = g.select("text");
        const rect = g.select("rect");

        const textNode = text.node() as SVGGraphicsElement | null;
        if (textNode) {
        const bbox = textNode.getBBox();

        rect
            .attr("x", bbox.x - 8)
            .attr("y", bbox.y - 4)
            .attr("width", bbox.width + 16)
            .attr("height", bbox.height + 8);
        }
    });

    // 删除节点图标
    const icon_deleteNode = node.append("g")
    .attr("class", "icon-node")
    .style("display", "none");

    icon_deleteNode.append("image")
    .attr("xlink:href", "/graph/delete.svg")
    .attr("x", 45)
    .attr("y", -45)
    .attr("width", 14)
    .attr("height", 14);

    icon_deleteNode.append("text")
    .attr("class", "icon-label")
    .attr("x", 65)  // 图标右边再偏移
    .attr("y", -33)   // 与图标垂直居中
    .attr("fill", "#666666")
    .style("font-size", "14px")
    .style("font-weight", "500")
    .style("pointer-events", "none")  // 不抢焦点
    .style("opacity", 0)  // 初始隐藏
    .text("删除节点");

    icon_deleteNode.select("image")
    .on("mouseover", function () {
        const parent = (this as SVGElement).parentNode as SVGGElement;
        d3.select(parent)
        .select("text")
        .transition()
        .duration(200)
        .style("opacity", 1);
    })
    .on("mouseout", function () {
        const parent = (this as SVGElement).parentNode as SVGGElement;
        d3.select(parent)
        .select("text")
        .transition()
        .duration(200)
        .style("opacity", 0);
    });

    // 添加节点图标
    const icon_addNode = node.append("g")
    .attr("class", "icon-node")
    .style("display", "none");

    icon_addNode.append("image")
    .attr("xlink:href", "/graph/addNode.svg")
    .attr("x", 60)
    .attr("y", -5)
    .attr("width", 14)
    .attr("height", 14);

    icon_addNode.append("text")
    .attr("class", "icon-label")
    .attr("x", 80)  // 图标右边再偏移
    .attr("y", 6)   // 与图标垂直居中
    .attr("fill", "#666666")
    .style("font-size", "14px")
    .style("font-weight", "500")
    .style("pointer-events", "none")  // 不抢焦点
    .style("opacity", 0)  // 初始隐藏
    .text("添加节点");

    icon_addNode.select("image")
    .on("mouseover", function () {
        const parent = (this as SVGElement).parentNode as SVGGElement;
        d3.select(parent)
        .select("text")
        .transition()
        .duration(200)
        .style("opacity", 1);
    })
    .on("mouseout", function () {
        const parent = (this as SVGElement).parentNode as SVGGElement;
        d3.select(parent)
        .select("text")
        .transition()
        .duration(200)
        .style("opacity", 0);
    });

    // 添加连线图标
    const icon_addLine = node.append("g")
    .attr("class", "icon-node")
    .style("display", "none");

    icon_addLine.append("image")
    .attr("xlink:href", "/graph/addLine.svg")
    .attr("x", 45)
    .attr("y", 35)
    .attr("width", 14)
    .attr("height", 14);

    icon_addLine.append("text")
    .attr("class", "icon-label")
    .attr("x", 65)  // 图标右边再偏移
    .attr("y", 46)   // 与图标垂直居中
    .attr("fill", "#666666")
    .style("font-size", "14px")
    .style("font-weight", "500")
    .style("pointer-events", "none")  // 不抢焦点
    .style("opacity", 0)  // 初始隐藏
    .text("添加连线");

    icon_addLine.select("image")
    .on("mouseover", function () {
        const parent = (this as SVGElement).parentNode as SVGGElement;
        d3.select(parent)
        .select("text")
        .transition()
        .duration(200)
        .style("opacity", 1);
    })
    .on("mouseout", function () {
        const parent = (this as SVGElement).parentNode as SVGGElement;
        d3.select(parent)
        .select("text")
        .transition()
        .duration(200)
        .style("opacity", 0);
    });

    // 添加节点标签
    // node
    //     .append("text")
    //     .attr("dy", ".35em")
    //     .attr("text-anchor", "middle")
    //     .text((d: any) => d.properties.name)
    //     .attr("fill", "black")
    //     .style("font-size", "14px")
    //     .style("font-weight", "400");

    // 添加点击高亮效果
    node.on("click", function (event: MouseEvent, d) {
        currentClickedNode = d; // 当前点击的节点数据
        emit('updateNode',{ 
            mode: "编辑节点", 
            id: currentClickedNode.id,
            name: currentClickedNode.properties.name,
            label: currentClickedNode.label,
            content: currentClickedNode.properties.content
        });

        // 重置所有节点和连接线的样式
        node.select("#circle")
            .attr("r", 30)
            .style("opacity", 0.1); // 将未选中的节点变灰
        node.select("#circleBg")
            .attr("r", 30)
            .style("opacity", 0.1); // 将未选中的节点变灰
        node.select("text")
            .style("font-size", "14px")
            .style("opacity", 0.1); // 将未选中的文本变灰
        node.selectAll(".icon-node")
            .style("display", "none"); // 隐藏节点图标

        link
            .attr("stroke", "#CACBCC")
            .style("opacity", 0.1); // 将未选中的连接线变灰
        linkLabels.select("text")
            .style("opacity", 0.1); // 将未选中的文本变灰
        linkLabels.selectAll(".icon-line")
            .style("display", "none"); // 隐藏连线图标

        // 高亮被点击的节点及其相关连接
        const clickedNode = d3.select(this);
        clickedNode.select("#circle")
            .attr("r", 45)
            .style("opacity", 1); // 设置选中节点的不透明度
        clickedNode.select("#circleBg")
            .attr("r", 45)
            .style("opacity", 1); // 设置选中节点的不透明度
        clickedNode.select("text")
            .style("font-size", "14px")
            .style("opacity", 1); // 设置选中节点文本的不透明度
        clickedNode.select(".textGroup")
            .attr("transform", "translate(0, -65)")// 设置选中节点文本的背景高度
        clickedNode.selectAll(".icon-node")
            .style("display", "block"); // 显示图标

        // 高亮与被点击节点相连的所有连接线和节点
        link.filter((l: any) => l.source.id.toString() === d.id.toString() || l.target.id.toString() === d.id.toString())
            .style("opacity", 1) // 恢复相关连接线的不透明度
            .each(function (l: any) {
                // 高亮相连的节点
                const connectedNode = l.source.id.toString() === d.id.toString() ? l.target : l.source;
                node.filter((n: any) => n.id.toString() === connectedNode.id.toString())
                    .select("#circle")
                    .style("opacity", 1)
                node.filter((n: any) => n.id.toString() === connectedNode.id.toString())
                    .select("#circleBg")
                    .style("opacity", 1)
                node.filter((n: any) => n.id.toString() === connectedNode.id.toString())
                    .select("text")
                    .style("opacity", 1);
            });

        // 定义箭头 marker
        svg.append("defs")
        .append("marker")
        .attr("id", "arrow_selectNode") // marker ID
        .attr("viewBox", "0 -10 20 20") // viewBox 范围
        .attr("refX", 99) // 箭头坐标位置，决定箭头与线条的相对位置
        .attr("refY", 0)
        .attr("markerWidth", 10) // marker 宽高，决定箭头大小
        .attr("markerHeight", 10)
        .attr("orient", "auto") // 自动旋转方向
        .append("path")
        .attr("d", "M0,-10L10,0L0,10") // path 绘制箭头形状
        .attr("fill", "#CACBCC"); // 箭头颜色

        // 修改指向被选中节点的线的箭头位置
        link.filter((l: any) => l.target.id.toString() === d.id.toString())
            .attr("marker-end", "url(#arrow_selectNode)") // 距离100箭头

        // 恢复被点击节点相连的所有连接线的label
        link.filter((l: any) => l.source.id.toString() === d.id.toString() || l.target.id.toString() === d.id.toString())
            .each(function (l: any) {
            // 恢复对应的 label 
            linksGroup.selectAll(".link-label-group")
                .filter((d:any) => d.id === l.id)
                .select("text")
                .style("opacity", 1); // 恢复 label 文本的不透明度
            })

        // 停止模拟
        simulation.stop();

        //添加连线逻辑
        if (isAddingLine.value && onTargetNodeSelected.value) {
            onTargetNodeSelected.value(d);
            /* console.log('起始节点数据:', startNode);
            console.log('目标节点数据:', d);
            isAddingLine.value = false;
            if (tempLine) {
                tempLine.remove();
            } */
        }
        showDetails(event, d,'node');
    });

    // 添加鼠标虚线移动事件
    svg.on("mousemove", function (event) {
        if (isAddingLine.value) {
            const [mx, my] = d3.pointer(event);
            const svg = d3.select(containerRef.value).select("svg");//选中节点的svg
            const transform = d3.zoomTransform(svg.node() as SVGSVGElement); //获取选中节点缩放变换的位置
            //应用缩放变换到起始位置
            const scaledX = transform.applyX(startNode.x);
            const scaledY = transform.applyY(startNode.y);
            tempLine
                .attr("x1", scaledX)
                .attr("y1", scaledY)
                .attr("x2", mx)
                .attr("y2", my);
        }
    });

    //节点和线右键菜单
    const showContextMenu = (event: MouseEvent, element: SVGGraphicsElement, type: string, data: any) => {
        isDetailsMenuVisible.value = false; // 隐藏详情菜单
        event.preventDefault();
        if (type === 'node') {
            currentClickedNode = data;
        } else if (type === 'line') {
            currentClickedLine = data;
        }
        rightClickedObject.value = type;
        isContextMenuVisible.value = true;

        const rect = containerRef.value!.getBoundingClientRect()
        const menuWidth = 130
        const menuHeight = 160

        // 将client坐标转为容器内部坐标
        let x = event.clientX - rect.left 
        let y = event.clientY - rect.top

        if (x + menuWidth > rect.width) {
            x = rect.width - menuWidth
        }
        if (y + menuHeight > rect.height) {
            y = rect.height - menuHeight
        }

        // 设置菜单的位置
        menuLeft.value = x + 10;
        menuTop.value = y + 10;
       
    };

    // 添加节点右键点击事件
    node.on("contextmenu", function (event: MouseEvent, d) {
        showContextMenu(event, this as SVGGraphicsElement, 'node', d);
    });
    // 添加线右键点击事件
    link.on("contextmenu", function (event: MouseEvent, d) {
        showContextMenu(event, this as SVGGraphicsElement, 'line', d);
    });

    // 详情菜单
    const showDetails = (event: MouseEvent, data: any, type: any ) => {
        isContextMenuVisible.value = false; // 隐藏右键菜单
        if (type === 'node') {
            currentClickedNode = data;
        } else if (type === 'line') {
            currentClickedLine = data;
        }
        leftClickedObject.value = type;
        isDetailsMenuVisible.value = true;

        const rect = containerRef.value!.getBoundingClientRect()
        const menuWidth = 130
        const menuHeight = 160

        // 将client坐标转为容器内部坐标
        let x = event.clientX - rect.left 
        let y = event.clientY - rect.top

        if (x + menuWidth > rect.width) {
            x = rect.width - menuWidth
        }
        if (y + menuHeight > rect.height) {
            y = rect.height - menuHeight
        }

        // 设置菜单的位置
        menuLeft.value = x + 10;
        menuTop.value = y + 10;
    
    };

    // 添加点击背景重置效果
    svg.on("click", function (event) {
        clearHighlight();

        if (event.target === this) {
            // 重置所有节点和连接线的样式
            node.select("#circle")
                .attr("r", 30)
                .style("opacity", 1);
            node.select("#circleBg")
                .attr("r", 30)
                .style("opacity", 1);
            node.select("text")
                .style("opacity", 1);
            node.select(".textGroup")
                .attr("transform", "translate(0, -50)")// 文本的背景高度
            node.selectAll(".icon-node")
                .style("display", "none"); // 显示删除图标

            link
                .attr("stroke", "#CACBCC")
                .attr("stroke-width", 1)
                .style("opacity", 1)
                .attr("marker-end", "url(#arrow)") // 距离70箭头

            linkLabels.select("text")
                .style("opacity", 1);
            linkLabels.selectAll(".icon-line")
                .style("display", "none"); // 隐藏连线图标

            // 重新启动模拟
            simulation.restart();
            isContextMenuVisible.value = false; // 隐藏右键菜单
            isDetailsMenuVisible.value = false; // 隐藏详情菜单

            isAddingLine.value = false;
            if (tempLine) {
                tempLine.remove();
            }
        }
    });

};


// 实现拖拽功能
const drag = (simulation: d3.Simulation<d3.SimulationNodeDatum, undefined>) => {
    function dragstarted(event: any) {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        event.subject.fx = event.subject.x;
        event.subject.fy = event.subject.y;
    }

    function dragged(event: any) {
        event.subject.fx = event.x;
        event.subject.fy = event.y;
    }

    function dragended(event: any) {
        if (!event.active) simulation.alphaTarget(0);
        event.subject.fx = null;
        event.subject.fy = null;
    }

    return d3
        .drag()
        .on("start", dragstarted)
        .on("drag", dragged)
        .on("end", dragended);
};

// 监听容器大小变化
watch([width, height], async () => {
    if (containerRef.value) {
        const svg = d3.select(containerRef.value).select("svg");
        if (!svg.empty()) {
            const containerWidth = containerRef.value.clientWidth;
            const containerHeight = containerRef.value.clientHeight;
            const width = Math.max(containerWidth, 800);
            const height = Math.max(containerHeight, 600);

            svg
                .attr("width", "100%")
                .attr("height", "100%")
                .attr("viewBox", [0, 0, width, height])
                .attr("preserveAspectRatio", "xMidYMid meet");
        }
    }
});

// 监听 nodes 和 links 的变化
watch([() => props.nodes, () => props.links], async () => {
    console.log('nodes or links changed', props.nodes, props.links);
    if (containerRef.value) {
        await render2DGraph(containerRef.value);
    }
});

//右键菜单编辑节点选项
const updateMenuItemClick = (option: string) => {
    console.log(`点击了选项: ${option}`);
    console.log('被点击节点的信息:', currentClickedNode);
    updadeData = currentClickedNode;
    isEditMode.value = true;
    isContextMenuVisible.value = false;
};

// 右键菜单添加连线选项
const addLineClick = (option: string) => {
    console.log(`点击了选项: ${option}`);
    isAddingLine.value = true;
    startNode = currentClickedNode;
    isContextMenuVisible.value = false;

    const svg = d3.select(containerRef.value).select("svg");
    const foundNode = simulation.find(startNode.x, startNode.y);
    const { x, y } = foundNode || startNode; // 如果没找到，使用 startNode 本身的 x 和 y
    // const transform  = d3.zoomTransform(svg.node() as SVGSVGElement); //获取当前选中节点缩放变换的位置
    // // 应用缩放变换到起始位置
    // const scaledX = transform.applyX(x);
    // const scaledY = transform.applyY(y);
    tempLine = svg.append("line")
        .attr("x1", x)
        .attr("y1", y)
        .attr("x2", x)
        .attr("y2", y)
        .attr("stroke", "red")
        .attr("stroke-dasharray", "5,5")
        .style("pointer-events", "none"); // 让鼠标事件穿透虚线

    onTargetNodeSelected.value = async (targetNode) => {
        console.log('起始节点数据:', startNode);
        console.log('目标节点数据:', targetNode);
        console.log(' props.taskID:',  props.taskID);
        const form = {
            label: "暂无",
            from_vertex_id: startNode.id,
            to_vertex_id: targetNode.id,
            properties: {
                type: "武将",
                taskId: props.taskID
            }
        };
        console.log("!!!!!!!!!!!!!!!!!!!!!!!!!!!",form);
        try {            
            const response = addGraphEdge(form);
            console.log('添加边成功', response);
            message.success('添加连线成功！');
            await props.getGraphList();
        } catch (error) {
            console.log('添加边失败', error)
            message.error('添加连线失败！')
        }
        isAddingLine.value = false;
        if (tempLine) {
            tempLine.remove();
        }
    };
};

// 统一的菜单点击处理函数
const handleMenuClick = async (option: string) => {
    //console.log(`点击了菜单选项: ${option}`);
    try {
        //添加边
        if (option === 'addLine') {
            addLineClick('option2');
        }
        //添加节点
        else if (option === 'addNode') {            
            emit('addNode',{ mode:"添加节点" });
        }
        //删除节点
        else if (option === 'deleteNode') {
            const confirmed = await showDeleteConfirm('确定要删除吗？删除后无法恢复！');
            if (confirmed) {
                const form = {
                    vertex_id: currentClickedNode.id
                };
                const response = await deleteGraphNode(form);
                console.log('删除节点成功', response);
                message.success('删除节点成功！');
                const newNodes = props.nodes.filter(node => node.id !== currentClickedNode.id);
                const newLinks = props.links.filter(link => link.source.id !== currentClickedNode.id && link.target.id !== currentClickedNode.id);
                emit('deleteNode', { nodes: newNodes, links: newLinks });
            }
        } 
        //编辑节点
        else if (option === 'updateNode') {
            updateMenuItemClick('option2');
            emit('updateNode',{ 
                mode: "编辑节点", 
                id: currentClickedNode.id,
                name: currentClickedNode.properties.name,
                content: currentClickedNode.properties.content
            });
        } 
        //删除边
        else if (option === 'deleteLine') {
            const confirmed = await showDeleteConfirm('确定要删除吗？删除后无法恢复！');
            if (confirmed) {
                const edgeId = currentClickedLine.id.match(/'relationId'\s*:\s*'([^']+)'/)[1]
                console.log("edgeId",edgeId);
                const form = {
                    edge_id: edgeId
                };
                const response = await deleteGraphEdge(form);
                console.log('删除连线成功', response);
                message.success('删除连线成功！');
                const newLinks = props.links.filter(link => link.id !== currentClickedLine.id.match(/'relationId'\s*:\s*'([^']+)'/)[1]);
                emit('deleteNode', { nodes: props.nodes, links: newLinks });
            }
        }
        //编辑边
        else if (option === 'updateLine'){
            console.log('被点击的连线数据:', currentClickedLine);
            updateLine = currentClickedLine;
            console.log('被点击的连线数据666:', updateLine);
            emit('updateEdge',{ 
                mode:'编辑边', 
                id: currentClickedLine.id.match(/'relationId'\s*:\s*'([^']+)'/)[1],
                label: currentClickedLine.label
            });
        }

    } catch (error) {
        message.error(`操作失败: ${error}`);
        console.log('操作失败', error);
    }
    isContextMenuVisible.value = false;
};

onMounted(async () => {
    if (containerRef.value) {
        await render2DGraph(containerRef.value);
    }
});

onUnmounted(() => {
    if (simulation) {
        simulation.stop();
    }
    if (containerRef.value) {
        d3.select(containerRef.value).selectAll("*").remove();
    }
    const detailsElement = document.getElementById('details-element');
    if (detailsElement) {
        detailsElement.parentNode?.removeChild(detailsElement);
    }
});
</script>

<style scoped>
.layout {
    display: flex;
    width: 100%;
    height: 100%;
}

.graph-container {
    width: 100%;
    height: 100%;
    /* min-height: 600px;
    min-width: 800px; */
    background: rgba(240, 249, 255, 1);
    position: relative;
    overflow: hidden;
    flex: 1;
    /* 让容器填充剩余空间 */
}

.context-menu {
    position: absolute;
    width: 115px;
    border-radius: 2.95px;
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.1);
    background-color: white;
    padding: 12px;
    font-size: 14px;
    z-index: 1;
    font-weight: 500;
    line-height: 14px;
    color: rgba(102, 102, 102, 1);
}

.context-menu ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.context-menu li {
    display: flex;
    justify-content: left;
    align-items: center;
    cursor: pointer;
    padding: 4px 6px;
    gap: 5px
}

.context-menu li:hover {
    background-color: #f0f0f0;
}

.details-menu {
    position: absolute;
    border-radius: 2.95px;
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.1);
    background-color: white;
    padding: 12px;
    font-size: 14px;
    z-index: 1;
    font-weight: 500;
    line-height: 14px;
    color: rgba(102, 102, 102, 1);
    overflow: auto;
}

.details-menu ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.details-menu li {
    display: flex;
    justify-content: left;
    align-items: center;
    cursor: pointer;
    padding: 4px 6px;
    gap: 5px
}

.details-menu li:hover {
    background-color: #f0f0f0;
}

</style>