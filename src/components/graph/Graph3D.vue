<template>
    <div class="layout">
        <div ref="containerRef" class="graph-container"></div>
        <div style="position: absolute; top: 5px; right: 300px;">
            <!-- <button @click="toggleAnimation" style="margin: 8px; height: 25px; width: 150px;">
                {{ isAnimationActive ? '暂停动画' : '开始动画' }}
            </button> -->
            <button @click="toggleRotation" style="margin: 8px; height: 25px; width: 150px;">
                {{ isRotationActive ? '暂停旋转' : '开始旋转' }}
            </button>
        </div>
        <!-- <FunctionalArea style="width: 300px;" :selectedNode="isEditMode ? updadeData : null" :isEditing="isEditMode" :updateLine="updateLine" /> -->
        <!-- 右键菜单 -->
        <div ref="contextMenuRef" class="context-menu" v-show="isContextMenuVisible"
            :style="{ left: menuLeft + 'px', top: menuTop + 'px' }">
            <ul>
            <!-- <li @click="handleMenuClick('addLine')">
                <img src="@/assets/image/arrow.png" alt="添加连线图标"
                style="width: 12px; height: 12px; margin-right: 4px;" />添加连线
            </li>
             <li @click="handleMenuClick('editNode')">
                <img src="@/assets/image/update.png" alt="修改图标"
                style="width: 12px; height: 12px; margin-right: 4px;" />编辑节点</li>
             <li @click="handleMenuClick('deleteNode')">
                <img src="@/assets/image/delete.png" alt="删除图标"
                style="width: 12px; height: 12px; margin-right: 4px;" />删除节点</li> -->
                <template v-if="rightClickedObject === 'node'">
                    <li @click="handleMenuClick('addLine')">
                        <img src="@/assets/image/arrow.png" alt="添加连线图标"
                            style="width: 12px; height: 12px; margin-right: 4px;" />添加连线
                    </li>
                    <li @click="handleMenuClick('deleteNode')">
                        <img src="@/assets/image/delete.png" alt="删除图标"
                            style="width: 12px; height: 12px; margin-right: 4px;" />删除节点
                    </li>
                    <li @click="handleMenuClick('editNode')">
                        <img src="@/assets/image/update.png" alt="修改图标"
                            style="width: 12px; height: 12px; margin-right: 4px;" />编辑节点
                    </li>
                </template>
                <template v-else-if="rightClickedObject === 'line'">
                    <li @click="handleMenuClick('deleteLine')">
                        <img src="@/assets/image/delete.png" alt="删除图标"
                            style="width: 12px; height: 12px; margin-right: 4px;" />删除连线
                    </li>
                    <li @click="handleMenuClick('updateLine')">
                        <img src="@/assets/image/update.png" alt="编辑图标"
                            style="width: 12px; height: 12px; margin-right: 4px;" />编辑连线
                    </li>
                </template>
            </ul>
        </div>
        
    </div>
</template>

<script setup lang="ts">
import * as d3 from "d3";
import { ref, onMounted, onUnmounted, watchEffect, nextTick, watch } from "vue";
import * as THREE from "three";
import { useElementSize } from '@vueuse/core';
import { shallowRef } from 'vue';
import FunctionalArea from './FunctionalArea.vue'
import { ElMessage } from "element-plus";
import { deleteGraphNode, addGraphEdge, deleteGraphEdge } from "@/services/api/graph";

const props = defineProps({
    nodes: {
        type: Array as () => any[],
        default: () => []
    },
    links: {
        type: Array as () => any[],
        default: () => []
    },
    getGraphList: {
        type: Function,
        required: true
    },
});

const containerRef = ref<HTMLElement | null>(null);
const { width, height } = useElementSize(containerRef);
let graph: any;
let highlightedNodeId: string | null = null;

// 使用 shallowRef 来存储动态导入的 ForceGraph3D
const ForceGraph3D = shallowRef<any>(null);
// 创建颜色生成函数
const getNodeColor = d3.scaleOrdinal(d3.schemeCategory10);
const updadeData = ref<any>(null); //更新节点数据
let updateLine: any; //更新连线数据
const isEditMode = ref(false); //表示编辑的状态
const rightClickedObject = ref<string | null>(null); //右键点击的对象标记
// 定义节点详情信息展示元素函数
const createDetailsElement = () => {
    const detailsElement = document.createElement('div');
    detailsElement.id = 'node-details';
    detailsElement.style.display = 'none';
    detailsElement.style.position = 'absolute';
    detailsElement.style.bottom = '20px';
    detailsElement.style.right = '310px';
    detailsElement.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
    detailsElement.style.padding = '10px';
    detailsElement.style.borderRadius = '5px';
    detailsElement.style.zIndex = '100';
    detailsElement.style.minWidth = '200px';
    document.body.appendChild(detailsElement);
    return detailsElement;
};

// 定义显示节点详情函数
const showNodeDetails = (data: any, type: 'node' | 'line') => {
    const detailsElement = document.getElementById('node-details') || createDetailsElement();
    console.log('showNodeDetails', data, type);
    let detailsContent = ''
    if (type === 'node') {
        detailsContent = `
            <h3>节点详情</h3>
            <p>ID: ${data.id || '无ID'} </p>
            <p>名称: ${data.properties.name || '无名称'}</p>
            <p>标签：${data.label || '无标签'}</p>
            <p>组别: ${data.properties.group || '无组别'}</p>
        `;
    }else if (type === 'line') {
        detailsContent = `
            <h3>连线详情</h3>
            <p>ID: ${data.id}</p>
            <p>关系标签: ${data.label}</p>
            <p>起始节点: ${data.source.properties.name}</p>
            <p>目标节点: ${data.target.properties.name}</p>
        `;
    }
    detailsElement.innerHTML = detailsContent;
    detailsElement.style.display = 'block';
};

// 定义隐藏节点详情函数
const hideNodeDetails = () => {
    const detailsElement = document.getElementById('node-details');
    if (detailsElement) {
        detailsElement.style.display = 'none';
    }
};

const isContextMenuVisible = ref(false); // 用于控制右键菜单的显示状态
const menuLeft = ref(0); // 用于存储右键菜单的位置
const menuTop = ref(0);
let currentClickedNode: any; // 存储当前右键点击的节点
let selectedLink: any;
// 旋转逻辑变量以下四个
const isRotationActive = ref(true); // 控制旋转
let angle = 0;
const distance = 300;
const isAnimationActive = ref(true); // 控制动画
const isAddingLine = ref(false); // 添加连线状态
let tempLine: any; // 临时虚线
let startNode: any; // 起始节点
let currentClickedLink: any;
const onTargetNodeSelected = ref<((targetNode: any) => void) | null>(null);

const positionNodesInSphere = (nodes: any[], radius = 100) => { //球体
    const total = nodes.length;
    nodes.forEach((node, i) => {
        const phi = Math.acos(-1 + (2 * i) / total);
        const theta = Math.sqrt(total * Math.PI) * phi;
        node.x = radius * Math.cos(theta) * Math.sin(phi);
        node.y = radius * Math.sin(theta) * Math.sin(phi);
        node.z = radius * Math.cos(phi);
    });
};

// 封装显示右键菜单的函数
const showContextMenu = (clickedObject: 'node' | 'line', event: PointerEvent, clickedData: any) => {
    // console.log(`on${clickedObject === 'node' ? 'Node' : 'Link'}RightClick 事件触发`);
    event.preventDefault(); // 阻止默认行为

    if (clickedObject === 'node') {
        currentClickedNode = clickedData;
    } else {
        currentClickedLink = clickedData;
    }
    rightClickedObject.value = clickedObject;
    isContextMenuVisible.value = true;

    menuLeft.value = event.clientX;
    menuTop.value = event.clientY;

    console.log('isContextMenuVisible 的值：', isContextMenuVisible.value);
};

// 动态导入 ForceGraph3D
watchEffect(async () => {
    if (!ForceGraph3D.value) {
        try {
            const module = await import('3d-force-graph');
            ForceGraph3D.value = module.default;
            // 导入完成后尝试渲染
            await render3DGraph();
        } catch (error) {
            console.error('Failed to import 3d-force-graph:', error);
        }
    }
});

// 渲染3D图形的主函数
const render3DGraph = async () => {
    if (!containerRef.value || !ForceGraph3D.value) return;

    try {
        // 清理现有的图形
        if (graph) graph._destructor();
        hideNodeDetails(); // 隐藏节点详细信息
        // 获取容器的实际尺寸
        const observer = new ResizeObserver((entries) => {
            for (const entry of entries) {
                const containerWidth = entry.contentRect.width;
                const containerHeight = entry.contentRect.height;
                // console.log('Container Size:', containerWidth, containerHeight);

                // 相机尺寸来渲染图形
                if (graph) {
                    graph.width(containerWidth);
                    graph.height(containerHeight);
                }
            }
        });

        if (containerRef.value) {
            observer.observe(containerRef.value);
        }

        // 将 Proxy 对象转换为普通 JavaScript 对象
        let nodes = JSON.parse(JSON.stringify(props.nodes));
        let links = JSON.parse(JSON.stringify(props.links));

        // 将 nodes 中的 id 转换为字符串
        nodes = nodes.map((node: any) => ({
            ...node,
            id: node.id.toString()
        }));

        links = links.map((link: any) => ({
            ...link,
            source: typeof link.source === 'object' ? link.source.id.toString() : link.source.toString(),
            target: typeof link.target === 'object' ? link.target.id.toString() : link.target.toString()
        }));


        const graphData = { nodes, links };

        console.log('3D Graph Data:', graphData);
        if (!graphData || !graphData.nodes.length) return;

        // 获取容器的实际尺寸
        // const containerWidth = containerRef.value.clientWidth;
        // const containerHeight = containerRef.value.clientHeight;
        // console.log('Container Size:', containerWidth, containerHeight);

        positionNodesInSphere(graphData.nodes, 120); // 调整半径让它更聚拢

        // 创建3D力导向图
        graph = ForceGraph3D.value()(containerRef.value)
           // .cooldownTicks(100) // 设置冷却帧数
            .graphData(graphData)
            .width(containerRef.value.clientWidth)
            .height(containerRef.value.clientHeight)
            .nodeLabel('properties.name')
            .cooldownTicks(0)
            .d3VelocityDecay(1) // 无摩擦
            .linkColor((link: any) => {
                if (highlightedNodeId && (link.source.id === highlightedNodeId || link.target.id === highlightedNodeId)) {
                    return '#ff0000'; // 与高亮节点相关的连线颜色
                }
                if(selectedLink && (link.id === selectedLink.id)){
                    return '#00ff00';
                }
                return '#ffffff'; // 普通连线颜色
            })
            .linkWidth((link: any) => {
                if (highlightedNodeId && (link.source.id === highlightedNodeId || link.target.id === highlightedNodeId)) {
                    return 3; // 与高亮节点相关的连线宽度
                }
                if(selectedLink && (link.id === selectedLink.id)){
                    return 3; //选中连线的宽度
                }
                return 1; // 普通连线宽度
            })
            .linkOpacity(0.2)
            // .linkDirectionalParticles(3) // 启用粒子效果
            .linkDirectionalParticleSpeed(0.002) // 设置粒子速度
            .linkDirectionalParticleWidth(1) // 设置粒子宽度
            .nodeThreeObject((node: any) => {
                const group = new THREE.Group();

                // 创建球体几何体
                const geometry = new THREE.SphereGeometry(4, 32, 32);
                // 修改材质为 ShaderMaterial 实现外圈亮内里暗效果
                const vertexShader = `
                    varying vec3 vNormal;
                    void main() {
                        vNormal = normalize(normalMatrix * normal);
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `;
                const fragmentShader = `
                    varying vec3 vNormal;
                    uniform vec3 color;
                    uniform float emissiveIntensity; // 自发光强度
                    uniform float glowIntensity; // 泛光强度
                    uniform bool isHighlighted; // 高亮状态
                    void main() {
                        float intensity = pow(1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0)), 0.5);
                        // 计算自发光颜色
                        vec3 emissiveColor = color * emissiveIntensity;
                        // 计算泛光效果
                        float glowFactor = pow(1.0 - abs(dot(vNormal, vec3(0.0, 0.0, 1.0))), 2.0);
                        vec3 glowColor = color * glowFactor * glowIntensity;
                        vec3 finalColor = color * intensity + emissiveColor + glowColor;
                        if (isHighlighted) {
                            finalColor += vec3(1.0, 0.0, 0.0); // 高亮时增加白光
                        }
                        gl_FragColor = vec4(finalColor, 1.0);
                    }
                `;
                const material = new THREE.ShaderMaterial({
                    uniforms: {
                        color: { value: new THREE.Color(getNodeColor(node.id)) },
                        emissiveIntensity: { value: 0.6 }, // 设置自发光强度
                        glowIntensity: { value: 0.3 }, // 设置泛光强度
                        isHighlighted: { value: false } // 初始化高亮状态为 false
                    },
                    vertexShader: vertexShader,
                    fragmentShader: fragmentShader
                });
                const sphere = new THREE.Mesh(geometry, material);
                group.add(sphere);

                // 添加圆环
                const ringGeometry = new THREE.RingGeometry(5, 5.2, 32);
                const ringMaterial = new THREE.MeshBasicMaterial({ color: getNodeColor(node.id), side: THREE.DoubleSide });
                const ring = new THREE.Mesh(ringGeometry, ringMaterial);
                ring.rotation.x = Math.PI / 2; // 旋转圆环使其水平
                group.add(ring);

                // 创建文字标签
                const sprite = new THREE.Sprite(
                    new THREE.SpriteMaterial({
                        map: new THREE.CanvasTexture(createTextCanvas(node.properties.name)),
                        depthWrite: false,
                    })
                );
                sprite.scale.set(20, 10, 1);
                sprite.position.set(0, 7, 0);

                group.add(sprite);
                return group;
            })
            .onNodeClick((node: any) => {
                console.log('点击了节点:', node);
                console.log('isAddingLine.value', isAddingLine.value);
                console.log('onTargetNodeSelected.value', onTargetNodeSelected.value);
                if (isAddingLine.value && onTargetNodeSelected.value) {
                    onTargetNodeSelected.value(node);
                    console.log('起始节点数据:', startNode);
                    return;
                }
                const distance = 40;
                const distRatio = 1 + distance / Math.hypot(node.x, node.y, node.z);

                const newPos = node.x || node.y || node.z
                   ? { x: node.x * distRatio, y: node.y * distRatio, z: node.z * distRatio }
                    : { x: 0, y: 0, z: distance };

                graph.cameraPosition(
                    newPos,
                    node,
                    3000,
                );
                // 重置所有高亮
                resetHighlight();
                highlightedNodeId = node.id.toString();
                // highlightNode(node);
                highlightConnectedNodes(node);
                graph.refresh(); // 刷新图形以应用新的连线样式
                showNodeDetails(node, 'node'); // 显示节点详细信息
                isRotationActive.value = false; // 点击节点时停止旋转
            })

            // 添加节点右键点击事件处理逻辑
            .onNodeRightClick((node: any, event: PointerEvent) => {
                showContextMenu('node', event, node);
            })

            // 连线右键点击事件处理逻辑
            .onLinkRightClick((link: any, event: PointerEvent) => {
                if(selectedLink){
                    showContextMenu('line', event, link);
                }
            })
            .onBackgroundClick(() => {
                resetHighlight();
                highlightedNodeId = null;
                selectedLink = null; // 取消选中的连线
                graph.refresh(); // 刷新图形以应用新的连线样式
                hideNodeDetails(); // 隐藏节点详细信息
                isRotationActive.value = true; // 取消节点选中时重新开始旋转
            })
            .onLinkClick((link:any) => {
                console.log('onLinkClick 事件触发', link);
                selectedLink = link;
                // updateLine = link;
                // console.log('updateLine', updateLine);
                showNodeDetails(link, 'line');
                graph.refresh();
                console.log('onLinkClick 事件触发', link);
            })
            .showNavInfo(false);  // 去除导航页脚提示

        // 添加环境光和平行光
        const ambientLight = new THREE.AmbientLight(0xcccccc, 1.6);
        graph.scene().add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        directionalLight.position.set(1, 1, 1);
        graph.scene().add(directionalLight);
        graph.cameraPosition({ x: 0, y: 0, z: 300 });

        // 添加窗口大小变化监听
        window.addEventListener('resize', handleResize);

        // 相机旋转逻辑
        const rotationInterval = setInterval(() => {
            if (isRotationActive.value && graph) {
                graph.cameraPosition({
                    x: distance * Math.sin(angle),
                    z: distance * Math.cos(angle)
                });
                angle += Math.PI / 900;
            }
        }, 10);

        onUnmounted(() => {
            clearInterval(rotationInterval);
            if (containerRef.value) {
                observer.unobserve(containerRef.value);
            }
        });
        // 添加鼠标移动事件监听
        const handleMouseMove = (event: MouseEvent) => {
            if (isAddingLine.value && tempLine) {
                const rect = containerRef.value!.getBoundingClientRect();
                const mouseX = (event.clientX - rect.left) / rect.width * 2 - 1;
                const mouseY = -(event.clientY - rect.top) / rect.height * 2 + 1;

                const raycaster = new THREE.Raycaster();
                const camera = graph.camera();
                const mouse = new THREE.Vector2(mouseX, mouseY);
                raycaster.setFromCamera(mouse, camera);

                const intersects = raycaster.intersectObjects(graph.scene().children, true);
                if (intersects.length > 0) {
                    const point = intersects[0].point;
                    const positions = new Float32Array([startNode.x, startNode.y, startNode.z, point.x, point.y, point.z]);
                    tempLine.geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                    tempLine.geometry.attributes.position.needsUpdate = true;
                }
            }
        };
        // 点击空白处处理函数
        const handleBackgroundClick = (event: MouseEvent) => {
            const isClickOnNode = graph.graphData().nodes.some((node:any) => {
                const nodeObject = node.__threeObj;
                if (nodeObject) {
                    const raycaster = new THREE.Raycaster();
                    const mouse = new THREE.Vector2();
                    const rect = containerRef.value!.getBoundingClientRect();
                    mouse.x = (event.clientX - rect.left) / rect.width * 2 - 1;
                    mouse.y = -(event.clientY - rect.top) / rect.height * 2 + 1;
                    raycaster.setFromCamera(mouse, graph.camera());
                    const intersects = raycaster.intersectObject(nodeObject, true);
                    return intersects.length > 0;
                }
                return false;
            });
            if (isClickOnNode) {
                return; // 如果点击在节点上，不执行后续操作
            }
            if (isAddingLine.value && tempLine) {
                // 移除临时虚线
                graph.scene().remove(tempLine);
                tempLine = null;
                console.log('设置 isAddingLine.value 后的值666:', isAddingLine.value);
                // 重置添加连线状态
                isAddingLine.value = false;
                console.log('设置 isAddingLine.value 后的值9999:', isAddingLine.value);
            }
        };
        if (containerRef.value) {
            containerRef.value.addEventListener('mousemove', handleMouseMove);
            containerRef.value.addEventListener('click', (event) => handleBackgroundClick(event));  // 新增：添加点击事件监听
            onUnmounted(() => {
                containerRef.value!.removeEventListener('mousemove', handleMouseMove);
                containerRef.value!.removeEventListener('click', (event) => handleBackgroundClick(event));  // 新增：移除点击事件监听
            });
        }
    } catch (error) {
        console.error('渲染3D图形时出错:', error);
    }
};

// 高亮选中的节点
// const highlightNode = (node: any) => {
//     const nodeObject = node.__threeObj;
//     if (nodeObject) {
//         nodeObject.children[0].material.uniforms.isHighlighted.value = true;
//         nodeObject.children[0].material.needsUpdate = true;
//         nodeObject.children[1].material.color.setHex(0xff0000); // 高亮圆环
//         nodeObject.children[1].material.needsUpdate = true;
//     }
// };

// 定义光晕材质
const glowMaterial = new THREE.ShaderMaterial({
    uniforms: {
        glowColor: { value: new THREE.Color(0xff0000) },
        viewVector: { value: new THREE.Vector3() }
    },
    vertexShader: `
        uniform vec3 viewVector;
        varying float intensity;
        void main() {
            vec3 vNormal = normalize(normalMatrix * normal);
            vec3 vNormel = normalize(vNormal + viewVector);
            intensity = pow(0.5 - dot(vNormal, vNormel), 2.0);
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
    `,
    fragmentShader: `
        uniform vec3 glowColor;
        varying float intensity;
        void main() {
            vec3 glow = glowColor * intensity;
            gl_FragColor = vec4(glow, 1.0);
        }
    `,
    side: THREE.BackSide,
    blending: THREE.AdditiveBlending,
    transparent: true
});

// 高亮相连的节点和连接线
const highlightConnectedNodes = (node: any) => {
    const nodeId = node.id.toString();
    graph.graphData().links.forEach((link: any) => {
        const sourceId = typeof link.source === 'object' ? link.source.id.toString() : link.source.toString();
        const targetId = typeof link.target === 'object' ? link.target.id.toString() : link.target.toString();
        if (sourceId === nodeId || targetId === nodeId) {
            const connectedNode = sourceId === nodeId ? link.target : link.source;
            // highlightNode(connectedNode);
            // 添加光晕效果
            const glowGeometry = link.__lineObj.geometry.clone();
            const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
            link.__lineObj.add(glowMesh);
        } else {
            // 移除光晕效果
            if (link.__lineObj.children.length > 0) {
                link.__lineObj.remove(link.__lineObj.children[0]);
            }
        }
    });
};

// 重置高亮
const resetHighlight = () => {
    graph.graphData().nodes.forEach((node: any) => {
        const nodeObject = node.__threeObj;
        if (nodeObject) {
            nodeObject.children[0].material.uniforms.color.value.set(getNodeColor(node.id));
            nodeObject.children[0].material.uniforms.isHighlighted.value = false;
            nodeObject.children[0].material.needsUpdate = true;
            nodeObject.children[1].material.color.set(getNodeColor(node.id)); // 恢复圆环颜色
            nodeObject.children[1].material.needsUpdate = true;
            isContextMenuVisible.value = false;
        }
    });

    graph.graphData().links.forEach((link: any) => {
        if (link.__lineObj.children.length > 0) {
            link.__lineObj.remove(link.__lineObj.children[0]);
        }
    });
};

const handleResize = () => {
    if (graph && containerRef.value) {
        graph.width(width.value);
        graph.height(height.value);
    }
};

// 创建文字画布
const createTextCanvas = (text: string) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return canvas;

    const fontSize = 2;
    ctx.font = `${fontSize}px Sans-Serif`;

    // 设置画布大小
    const textWidth = ctx.measureText(text).width;
    const scale = 10; // 提高分辨率的缩放因子
    canvas.width = (textWidth + 4) * scale;
    canvas.height = (fontSize + 4) * scale;

    // 重新设置字体，因为改变画布大小会重置上下文
    ctx.font = `${fontSize * scale}px Sans-Serif`;
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, canvas.width / 2, canvas.height / 2);

    return canvas;
};

// 菜单选项点击处理函数
const handleMenuClick = async (option: string) => {
    try{
        if(rightClickedObject.value === 'node'){
            if (option === 'editNode') {
                // 处理编辑节点逻辑
                updadeData.value = currentClickedNode;
                isEditMode.value = true;
                console.log('编辑节点', currentClickedNode);
            } else if (option === 'deleteNode') {
                // 处理删除节点逻辑
                const form = {
                    operation: "delete",
                    vertex_id: currentClickedNode.id
                };
                const response = await deleteGraphNode(form);
                console.log('删除节点成功', response);
                ElMessage.success('删除节点成功！');
                await props.getGraphList();
                console.log('删除节点', currentClickedNode);
            } else if (option === 'addLine'){
                console.log('进入添加连线操作，准备设置 isAddingLine.value 为 true');
                isAddingLine.value = true;
                console.log('设置 isAddingLine.value 后的值:', isAddingLine.value);
                startNode = currentClickedNode;
                isContextMenuVisible.value = false;

                // 使用 LineDashedMaterial 创建虚线
                const geometry = new THREE.BufferGeometry();
                const positions = new Float32Array([startNode.x, startNode.y, startNode.z, startNode.x, startNode.y, startNode.z]);
                geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                const material = new THREE.LineDashedMaterial({
                    color: 0xff0000,
                    dashSize: 5,
                    gapSize: 5
                });
                tempLine = new THREE.Line(geometry, material);
                // 需要计算线的长度来正确渲染虚线
                tempLine.computeLineDistances(); 
                graph.scene().add(tempLine);
                console.log('起始节点数据:', startNode);
                onTargetNodeSelected.value = async (targetNode) => {
                    console.log('起始节点数据:', startNode);
                    console.log('目标节点数据:', targetNode);
                    const form = {
                        operation: "add",
                        label: "ball",
                        from_vertex_id: startNode.id,
                        to_vertex_id: targetNode.id,
                        properties: {
                    time: "20250414"
                        }
                    };
                    try {
                        const response = await addGraphEdge(form);
                        console.log('添加边成功', response);
                        ElMessage.success('添加连线成功！');
                        await props.getGraphList();
                    } catch (error) {
                        console.log('添加边失败', error);
                        ElMessage.error('添加连线失败！');
                    }
                    isAddingLine.value = false;
                    if (tempLine) {
                        graph.scene().remove(tempLine);
                        tempLine = null;
                    }
                };
            } 
        }else if(rightClickedObject.value === 'line'){
            if (option === 'deleteLine') {
                // 处理删除连线逻辑
                const form = {
                operation: "delete",
                edge_id: currentClickedLink.id
                };
                console.log('删除连线', form);
                const response = await deleteGraphEdge(form);
                console.log('删除连线成功', response);
                ElMessage.success('删除连线成功！');
                await props.getGraphList();
            } else if (option === 'updateLine') {
                // 处理编辑连线逻辑
                updateLine = currentClickedLink;
                console.log('编辑连线', currentClickedLink);
            }
        }
    }catch(error){
        console.log('操作失败', error);
        ElMessage.error(`操作失败: ${error}`);
    }
    isContextMenuVisible.value = false;
};

// 切换动画状态
const toggleAnimation = () => {
    if (isAnimationActive.value) {
        graph.pauseAnimation();
    } else {
        graph.resumeAnimation();
    }
    isAnimationActive.value = !isAnimationActive.value;
};

// 切换旋转状态
const toggleRotation = () => {
    isRotationActive.value = !isRotationActive.value;
};

// 监听 nodes 和 links 的变化
watch([() => props.nodes, () => props.links], async () => {
    console.log('nodes or links changed', props.nodes, props.links);
    if (containerRef.value && ForceGraph3D.value) {
        await render3DGraph();
    }
});

onMounted(async () => {
    await nextTick();
    // 如果已经导入了 ForceGraph3D，尝试渲染
    if (ForceGraph3D.value) {
        await render3DGraph();
    }
});

onUnmounted(() => {
    if (graph) {
        graph._destructor();
        graph = null;
    }
    if (containerRef.value) {
        window.removeEventListener('resize', handleResize);
    }
    const detailsElement = document.getElementById('node-details');
    if (detailsElement) {
        detailsElement.parentNode?.removeChild(detailsElement);
    }
});
</script>

<style scoped>
.layout {
    display: flex;
    flex-direction: row;
}
.graph-container {
    width: 100%;
    height: 100%;
    /* min-height: 600px;
    min-width: 800px; */
    background: #000011;
    position: relative;
    overflow: hidden;
    flex: 1; /* 让容器填充剩余空间 */
}

.context-menu {
    position: absolute;
    background-color: white;
    border: 1px solid #ccc;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
    padding: 6px;
    font-size: 14px;
    z-index: 1000;
}

.context-menu ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.context-menu li {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    padding: 4px 6px;
}

.context-menu li:hover {
    background-color: #f0f0f0;
}

</style>    