<template>
    <div class="sidebar">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="增加节点" name="first">
                <div class="input-wrapper">
                    <el-form :model="form" label-width="auto" style="max-width: 600px">
                        <el-form-item label="标签">
                            <el-input v-model="form.label" />
                        </el-form-item>
                        <el-form-item label="名称">
                            <el-input v-model="form.properties.name" />
                        </el-form-item>
                        <!-- <el-form-item label="年龄">
                            <el-input v-model="form.properties.age" />
                        </el-form-item> -->
                        <el-form-item>
                            <el-button type="primary" @click="onSubmit('nodeAdd')">增加</el-button>
                            <!-- <el-button>取消</el-button> -->
                        </el-form-item>
                    </el-form>
                </div>
            </el-tab-pane>
            <el-tab-pane label="编辑节点" name="second">
                <div id="outline" v-if="isEditing && selectedNode">
                    <el-form :model="form" label-width="auto" style="max-width: 600px">
                        <el-form-item label="标签">
                            <el-input v-model="selectedNode.label" />
                        </el-form-item>
                        <el-form-item label="名称">
                            <el-input v-model="selectedNode.properties.name" />
                        </el-form-item>
                        <!-- <el-form-item label="年龄">
                            <el-input v-model="selectedNode.properties.age" />
                        </el-form-item> -->
                        <el-form-item>
                            <el-button type="primary" @click="onSubmit('nodeUpdate')">修改</el-button>
                            <!-- <el-button>取消</el-button> -->
                        </el-form-item>
                    </el-form>
                </div>
            </el-tab-pane>
            <el-tab-pane label="编辑连线" name="threed">
                <div id="outline" v-if="updateLine">
                    <el-form :model="form" label-width="auto" style="max-width: 600px">
                        <el-form-item label="标签">
                            <el-input v-model="updateLine.label" />
                        </el-form-item>
                        <el-form-item label="创建时间">
                            <el-input v-model="lineData.properties.time" />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="onSubmit('lineUpdate')">修改</el-button>
                            <!-- <el-button>取消</el-button> -->
                        </el-form-item>
                    </el-form>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, watch, ref } from "vue";
import type { TabsPaneContext } from "element-plus";
import { ElMessage } from "element-plus";
import { reactive, inject } from 'vue';
import { addGraphNode, updateGraphNode, updateGraphEdge } from "@/services/api/graph";

const props = defineProps({
    selectedNode: {
        type: Object,
        default: null
    },
    isEditing: {
        type: Boolean,
        default: false
    },
    updateLine: {
        type: Object,
        default: null

    }
});

const form = reactive({
    label: '',
    properties: {
        name: '',
        // age: '',
    },
    operation: '' // 可选属性
})

const lineData = reactive({
    properties: {
        time: formatDate(new Date()),
    },

})

function formatDate(date:any) {
  const year = date.getFullYear(); // 获取年份
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 获取月份，+1是因为getMonth返回的是0-11，padStart确保两位数
  const day = String(date.getDate()).padStart(2, '0'); // 获取日期，padStart确保两位数
  return `${year}${month}${day}`;
}

const activeName = ref("first");
// 存储动态计算的最大高度
const contentMaxHeight = ref('');

// tabs标签
const handleClick = (tab: TabsPaneContext, event: Event) => {
    console.log(tab, event);
};

// 监听 isEditing 和 selectedNode 的变化
watch([() => props.isEditing, () => props.selectedNode, () => props.updateLine], ([isEditing, selectedNode, updateLine]) => {
    if (isEditing && selectedNode) {
        activeName.value = "second";
    }
    if (updateLine) {
        activeName.value = "threed";
    }
});

// 定义 refreshGraphData 的类型
interface RefreshGraphDataType {
    (): Promise<void>;
}
const refreshGraphData = inject<RefreshGraphDataType>('refreshGraphData');

const onSubmit = async ( option: string) => {
    try {
        if(option === 'nodeAdd'){
            form.operation = "add";
            const response = await addGraphNode(form);
            console.log('提交成功', response)
            form.label = '';
            form.properties.name = '';
            // form.properties.age = '';
            //刷新数据
            if (refreshGraphData) {
                await refreshGraphData();
            }
            ElMessage.success('添加成功！')
        }else if(option === 'nodeUpdate'){
            let params = {
                operation: "update",
                vertex_id: props.selectedNode.id,
                label: props.selectedNode.label,
                properties: props.selectedNode.properties,
            }
            console.log("修改节点666", params);
            const response = await updateGraphNode(params);
            console.log('修改成功', response)
            if (refreshGraphData) {
                await refreshGraphData();
            }
            ElMessage.success('修改成功！')
        }else if(option === 'lineUpdate'){
            let params = {
                operation: "update",
                edge_id: props.updateLine.id,
                properties: lineData.properties
            }
            console.log("连线666", params);
            const response = await updateGraphEdge(params);
            console.log('修改连线成功', response)
            if (refreshGraphData) {
                await refreshGraphData();
            }
            ElMessage.success('修改成功！')
        }
    } catch (error) {
        ElMessage.error('操作失败！')
        console.log('操作失败', error)
    }
};

// 计算最大高度的函数
const calculateMaxHeight = () => {
    contentMaxHeight.value = `${window.innerHeight - 200}px`;
};

onMounted(() => {
    calculateMaxHeight();  // 页面加载的时候计算最大高度
    window.addEventListener('resize', calculateMaxHeight); // 监听窗口大小变化事件
});

onUnmounted(() => {
    window.removeEventListener('resize', calculateMaxHeight);   // 组件卸载时移除事件监听避免内存泄漏
});

</script>

<style>
.demo-tabs>.el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}

.el-tabs--right .el-tabs__content,
.el-tabs--left .el-tabs__content {
    height: 100%;
}

.el-tabs--bottom>.el-tabs__header .el-tabs__item:nth-child(2),
.el-tabs--top>.el-tabs__header .el-tabs__item:nth-child(2) {
    padding-left: 20px !important;
}

.el-tabs--bottom>.el-tabs__header .el-tabs__item:last-child,
.el-tabs--top>.el-tabs__header .el-tabs__item:last-child {
    padding-left: 20px !important;
}

.demo-tabs>.el-tabs__content {
    max-height: v-bind(contentMaxHeight);
    padding: 20px;
    font-weight: 600;
    font-size: 20px;
    overflow: auto;
}

.aie-content a {
    color: #409eff;
}
</style>