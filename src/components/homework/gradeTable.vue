<template>
    <div>
        <a-table :class="twMerge(
                'reset-ant-table-pagination',
                '[&_.ant-table-pagination-right]:items-center ',
                 'rounded-[5px] bg-white pb-2.5 [&_.ant-pagination]:px-5! [&_.ant-table-content]:px-5!',
                '[&_.ant-table-thead_.ant-table-cell]:bg-white! [&_.ant-table-thead_.ant-table-cell]:before:hidden',
                '[&_.ant-table-thead_.ant-table-cell]:text-[#333333]!',
                '[&_.ant-table-row]:h-[58px] [&_.ant-table-row]:text-[#666]',
                '[&_.ant-table-column-sorters]:justify-center! [&_.ant-table-column-sorters_.ant-table-column-title]:max-w-max!'
            )" 
            :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }" :columns="columns"
            :data-source="data" 
            :pagination="{ size: 'small', showSizeChanger: false }"
        >
            <template #bodyCell="{ column, record }">
                <div v-if="column.key === 'end_time'">
                    {{ record.end_time == null?'':formatDate(record.end_time) }}
                </div>
                <div v-if="column.key === 'take_part_status'" class="flex items-center justify-center">
                    <div style="" class="stutas" :class="{
                        'stutas1': record.take_part_status === '未提交', 'stutas2': record.take_part_status === '阅卷中',
                        'stutas3': record.take_part_status === '已阅卷', 'stutas4': record.take_part_status === '已发布',
                    }">
                        {{ record.take_part_status }}
                    </div>
                </div>
                <div v-if="column.key === 'total_score'" 
                    @mouseenter="onRowHover(record.id)" 
                    @mouseleave="onRowLeave"
                    class="flex items-center w-[100px]"
                >
                    <div class="w-[46px] text-right">
                        {{ record.total_score }}
                    </div>
                    <div v-show="hoveredRowId === record.id" class="w-[40px]">
                        <a-tooltip placement="bottom">
                            <template #title>
                                <span>编辑</span>
                            </template>
                            <img class="ml-[20px]" style="cursor: pointer;width: 14px;height: 14px;" src="@/assets/icon/editIcon.png" v-if="record.take_part_status == '已阅卷'" />
                            <img class="ml-[20px]" style="cursor: pointer;width: 14px;height: 14px;" src="@/assets/icon/editIconw.svg" v-else />
                        </a-tooltip>
                    </div>
                </div>
                <div
                    v-if="column.key === 'action'"
                    class="flex items-center justify-center gap-[36px]"
                >
                    <a-tooltip placement="bottom">
                        <template #title>
                            <span>批阅</span>
                        </template>
                        <img class="cursor-pointer h-[14px] w-[14px]" src="@/assets/icon/piyue.png" v-if="record.take_part_status != '已阅卷'"/>
                        <img class="cursor-pointer h-[14px] w-[14px]" src="@/assets/icon/piyuea.png"  @click="goToGrading(record)" v-else/>
                    </a-tooltip>
                    <a-tooltip placement="bottom">
                        <template #title>
                            <span>发布</span>
                        </template>
                        <img class="cursor-pointer h-[14px] w-[14px]" src="@/assets/icon/fabu.png"  v-if="record.take_part_status != '已阅卷'"/>
                        <img class="cursor-pointer h-[14px] w-[14px]" src="@/assets/icon/fabua.png" @click="goToPublish(record)" v-else/>
                    </a-tooltip>
                </div>
            </template>
        </a-table>

    </div>
</template>
<script lang="ts" setup>
import { formatDate } from '@/utils/util'
import { twMerge } from 'tailwind-merge'
import { computed, reactive } from 'vue';
import IconTrash from '~icons/lucide/trash-2'
import Square from '~icons/lucide/square-check-big'
// 在 script setup 中添加以下代码

const emit = defineEmits(['go-to-grading','go-to-publish']);
import type { ColumnType } from 'ant-design-vue/es/table'
type Key = string | number;
interface DataType {
  id: Key;
  key: Key;
  student_name: string;
  student_number: number;
  end_time: string;
  take_part_status: string;
  total_score: number;
}
const columns:ColumnType[] = [
    {
        title: '姓名',
        dataIndex: 'student_name',
        align: 'center',
    },
    {
        title: '学号',
        dataIndex: 'student_number',
        align: 'center',
        sorter: {
            compare: (a, b) => a.student_number - b.student_number,
            multiple: 2,
        },
    },
    {
        title: '提交时间',
        dataIndex: 'end_time',
        align: 'center',
        key: 'end_time',
    },
    {
        title: '状态',
        dataIndex: 'take_part_status',
        key:'take_part_status',
        align: 'center',
    },
    {
        title: '成绩',
        dataIndex: 'total_score',
        align: 'center',
        key: 'total_score',
        width: '100px',
        sorter: {
            compare: (a, b) => a.total_score - b.total_score,
            multiple: 2,
        }
    },
    {
        title: '操作',
        align: 'center',
        key:'action',
    },
];
const hoveredRowId = ref<number | null>(null)
function onRowHover(id: number) {
    hoveredRowId.value = id
}
function onRowLeave() {
    hoveredRowId.value = null
}


const props = defineProps<{
    handleExamList: any[]
}>()

console.log(props.handleExamList,'props.handleExamList')

const data = ref<DataType[]>([]);
watch(() => props.handleExamList, (newVal, oldVal) => { 
    console.log(newVal,oldVal,'添加数据')
    data.value = []
    newVal.forEach((item: any) => { 
        data.value.push({
            id: item.id,
            key: item.id,
            student_name: item.student_name,
            student_number: item.student_number,
            end_time: item.end_time,
            take_part_status: item.take_part_status,
            total_score: item.total_score,
        });
    })
}, { immediate: true, deep: true })



const state = reactive<{
    selectedRowKeys: Key[];
    loading: boolean;
}>({
    selectedRowKeys: [],
    loading: false,
});

const onSelectChange = (selectedRowKeys: Key[]) => {
    state.selectedRowKeys = selectedRowKeys;
    console.log(selectedRowKeys,'selectedRowKeys')
    // emits('selection-change', selectedRowKeys);
};

//批阅
const goToGrading = (id: any) => {
    emit('go-to-grading', id)
}
const goToPublish = (id: any) => {
    emit('go-to-publish', id)
}
const onClickChange = (data:any) => {
    console.log(data,'selectedRowKeys')
    // emits('selection-change', data);
};
</script>

<style scoped>
/* :deep(.ant-table-thead > tr > th) {
    color: #333333 !important;
    font-weight: bold;
} */
.stutas {
    width: 60px;
    height: 22px;
    line-height: 22px;
    font-size: 12px;
    font-weight: 700;
    opacity: 1;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.stutas1{
    border: 1px solid #FFA39E;
    background: #FFF1F0;
    color: #FF4D4F;
}
.stutas2{
    background: #E6F7FF;
    border: 1px solid #91D5FF;
    color: #1890FF;
}
.stutas3{
    background: #F6FFED;
    border: 1px solid #B7EB8F;
    color: #52C41A;
}
.stutas4{
    background: #FFFBE6;
    border: 1px solid #FFE58F;
    color: #FAAD14;
}

</style>