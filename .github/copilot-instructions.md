# AI Education Platform - Copilot Guide

## Project Architecture Overview

This is an AI education platform built with Vue 3 + TypeScript, featuring knowledge base management, intelligent dialogue, question generation, lesson planning, and other core functionalities.

### Core Technology Stack

- **Frontend**: Vue 3.5 + TypeScript + Composition API (`<script setup>`)
- **Build**: Vite + pnpm
- **UI**: Ant Design Vue (prefix `A`) + TailwindCSS 4.x
- **State Management**: Pinia 3.x + persistence plugin
- **Routing**: Vue Router 4.5 + file-system routing (`unplugin-vue-router`)
- **Data Fetching**: TanStack Vue Query + Axios/ofetch
- **Dev Tools**: Auto-import APIs/components, MSW mocking

## Architectural Design Principles

### Encapsulation Principles

1. **Export Only Coordination-Necessary Interfaces** - Components are black boxes, providing minimal, purpose-specific interfaces

   ```typescript
   // ✅ Clear interface design
   export type KbCard = {
     id: string
     name: string
     description?: string
     fileNum: number
     courses?: string[]
   }
   // ❌ Avoid exposing internal implementation details
   ```

2. **State Changes Flow Through Defined Channels** - Prohibit direct manipulation, only through props/emits

   ```vue
   <!-- ✅ Use defined interfaces -->
   const { setDeployFormRawModel, setIsDeployModalOpen } = injectModelsContext()
   <!-- ❌ Avoid directly modifying parent component state -->
   ```

3. **Collocate Related Concerns** - Keep mutation, error handling, and state together
   ```vue
   const { mutate: deleteKb, status } = useMutation({ mutationFn() { return kbFetcher('/datasets/',
   { method: 'delete', body: { ids: [props.id] } }) }, onSuccess() { return
   queryClient.invalidateQueries({ queryKey: kbQueryKey.kbList() }) }, onError(err) {
   message.error(err.message) } })
   ```

### Abstraction Principles

1. **Abstractions Must Prove Their Complexity** - Abstract only when clearly improving maintainability or readability
2. **Follow Data, Not UI** - Abstract based on data flow patterns, not visual similarity
3. **Repeat Until Patterns Emerge** - Wait for genuine patterns to appear before abstracting

```typescript
// ✅ Data flow-based abstraction pattern
export const kbQueryKey = {
  kbList: () => ['kb', 'list'],
  kbDetail: (id: MaybeRefOrGetter<string>) => ['kb', id, 'detail'],
}
// ❌ Avoid premature abstraction based on UI similarity
```

## Key Development Patterns

### 1. API Layer Design Pattern

```typescript
// Query Key Pattern - Each module defines standardized query keys
export const kbQueryKey = {
  kbList: () => ['kb', 'list'],
  kbDetail: (id: MaybeRefOrGetter<string>) => ['kb', id, 'detail'],
  kbFiles: (id: MaybeRefOrGetter<string>) => ['kb', id, 'files'],
}

// Type definitions: src/types/api/ - Use namespace organization
declare namespace API {
  namespace Kb {
    interface Kb {
      /* Complete structured types */
    }
    interface File {
      /* Related entity types */
    }
  }
  namespace Models {
    interface Model {
      /* Model definitions */
    }
    type CreateModel = Omit<Model, 'id' | 'created_at' | 'updated_at'>
  }
  namespace Chat {
    interface Conversation {
      /* Conversation structure */
    }
  }
}

// Fetcher Pattern - Modular HTTP client
export const kbFetcher = baseFetcher.create({
  baseURL: '/api/v1',
  // Module-specific response interceptors
})
```

### 2. Component Architecture Conventions

- **Page Components**: Use `injectContext` pattern for cross-component state, follow "export only coordination-necessary" principle
- **Business Components**: Strict props/emit interface, state changes through defined channels, support auto-import
- **UI Components**: AntDV components with `A` prefix, custom components PascalCase
- **Context Injection**: Complex state shared through `Symbol` InjectionKey, collocate related concerns

```vue
<!-- Page-level Context Provider Pattern: Minimize exposed interfaces -->
<script lang="ts">
  export function injectModelsContext() {
    const context = inject(modelsContextInjectionKey)
    if (!context) throw new Error('context not provided')
    return context
  }
</script>
```

**Encapsulation Decision Guide**:

- **Use Context**: Cross-level state sharing, complex form state, modal state
- **Use Props**: Pure display components, simple data passing, reusable components
- **Forbidden**: Child components directly modifying parent state, exposing internal implementation details

### 3. Component Auto-Import Mechanism

All components in the `src/components` directory are auto-imported, no need to manually write import statements:

```vue
<!-- ❌ No need for manual imports -->
<script setup>
  // import KbCard from '@/components/kb/KbCard.vue'  // Not needed
  // import ChatContainer from '@/components/chat/ChatContainer.vue'  // Not needed
</script>

<!-- ✅ Use components directly -->
<template>
  <KbCard
    :id="kb.id"
    :name="kb.name"
  />
  <ChatContainer>
    <ChatMessageThread />
  </ChatContainer>
</template>
```

**Auto-Import Rules**:

- Component names use PascalCase (e.g., `KbCard`, `ChatMessageThread`)
- Nested directory components named by file path (e.g., `chat/ChatContainer.vue` → `ChatContainer`)
- AntDV components automatically add `A` prefix (e.g., `<AButton>`, `<AModal>`)

### 4. Routing and Data Loading

- File-system routing: `src/pages/(app)/` main app, `src/pages/chat/` standalone module
- Dynamic routes: `[[id]].vue` optional parameters, supports UUID auto-generation
- Data preloading: Page component `async setup` + `<Suspense>` wrapper

## Core Module Architecture Patterns

### KB (Knowledge Base) Module

- **Data Flow**: `kbFetcher` → TanStack Query → reactive component updates
- **Component Design**: `KbCard` card-based layout, built-in delete confirmation Modal, collocated related concerns
- **State Management**: Local component state + Query cache, avoid global store (abstraction complexity principle)
- **File Structure**: `components/kb/` + `types/api/kb.d.ts` + `queries/kb.ts`

```vue
<!-- KB Component Pattern: Collocate related concerns -->
const { mutate: deleteKb, status } = useMutation({ mutationFn: () => kbFetcher('/datasets/', {
method: 'delete', body: { ids: [props.id] } }), onSuccess: () => queryClient.invalidateQueries({
queryKey: kbQueryKey.kbList() }), onError: (err) => message.error(err.message) })
```

### Models (Model Management) Module

- **Page Context Pattern**: `injectModelsContext()` for cross-component state sharing, minimize interface exposure
- **Form Data Transformation**: `rawDataToFormValues` / `formValuesToRawData` data layer separation
- **Deployment Workflow**: Modal + Form + state machine-driven user interaction
- **Permission Control**: Route-level `definePage({ meta: { roles: ['admin'] } })`

```typescript
// Context Injection Pattern - Export only coordination-necessary interfaces
const context = {
  selectedModels: shallowReadonly(selectedModels),
  setSelectedModels(value) {
    selectedModels.value = value
  },
  deployFormRawModel: shallowReadonly(deployFormRawModel),
  setDeployFormRawModel(value) {
    deployFormRawModel.value = value
  },
  // State changes flow through defined channels
}
provide(modelsContextInjectionKey, context)
```

### Chat (Intelligent Dialogue) Module

- **Message Flow Architecture**: `@wicii/chat-primitive` + custom message components
- **Real-time State**: `injectChatContext()` provides `messages`, `status`, `error`
- **Component Composition**: `MessageRoot` + role-specific message components (`ChatUserMessage`, `ChatAssistantMessage`)
- **Auto-scroll**: `useAutoScroll` custom Hook manages scroll behavior

```vue
<!-- Chat Component Pattern: Primitive + Custom Composition -->
<MessageRoot v-for="message in messages" :message="message" as-child>
  <ChatUserMessage v-if="message.role === 'user'" />
  <ChatAssistantMessage v-else-if="message.role === 'assistant'" />
</MessageRoot>
```

## Project-Specific Patterns

### 1. Authentication Flow

- CAS Single Sign-On: `env.VITE_API_BASE_URL/v1/cas/login/`
- 401 error auto-redirect to login: `authStore.attemptAuth()`

### 2. Environment Configuration

```typescript
// env.ts uses @t3-oss/env-core for environment variable validation
// Client variables: VITE_ prefix
// Server variables: build-time use, not exposed to frontend
```

### 3. HTTP Client Hierarchy

```typescript
// Base layer: baseFetcher (common configuration)
// Module layer: kbFetcher, modelsFetcher (specific baseURL + interceptors)
// Business layer: queries/ folder unified Query Key management
```

### 4. Development Debugging

```bash
# Development start
pnpm dev  # Run with dotenvx environment variables

# Build
pnpm build
```

## Development Workflow Guide

### New Business Module Checklist

1. **Type Definitions**: `src/types/api/[module].d.ts` - namespace organization
2. **Query Keys**: `src/queries/[module].ts` - standardized cache keys
3. **HTTP Client**: Consider if dedicated `fetcher` configuration is needed
4. **Mock Data**: `src/mocks/[module].ts` - MSW simulate complete API
5. **Component Structure**: `src/components/[module]/` - group by functionality

### Context vs Props Decision Tree

- **Use Context**: Cross-level state sharing, complex form state, modal state
- **Use Props**: Pure display components, simple data passing, reusable components
- **Forbidden**: Child components directly modifying parent state, exposing internal implementation details

**Encapsulation Decision Guide**:

- **Export Only Coordination-Necessary**: Minimize component interfaces, clear responsibility boundaries
- **State Changes Through Defined Channels**: Use setter functions instead of direct assignment
- **Collocate Related Concerns**: Keep mutation + error handling + cache invalidation together

## Code Standards

### Vue Components

- Use `<script setup lang="ts">` syntax
- Props use `defineProps<{}>()`
- Events use `defineEmits<{}>()`
- Component names use PascalCase

### Style Conventions

- Prioritize TailwindCSS classes
- AntDV style overrides: `src/assets/css/antdv-override.css`
- Support native CSS nesting (LightningCSS)

## Key File Locations

- **Type Definitions**: `src/types/api/`
- **State Management**: `src/stores/`
- **Vue Query Configuration**: `src/utils/vue-query.ts`
- **Mock Data**: `src/mocks/` (MSW)
