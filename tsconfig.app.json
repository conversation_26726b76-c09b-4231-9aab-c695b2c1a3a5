{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts", "env.ts", "typed-router.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "unplugin-vue-router/client", "unplugin-icons/types/vue"]}}